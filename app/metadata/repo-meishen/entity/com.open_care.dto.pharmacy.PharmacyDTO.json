{"id": "115859a1-3936-4861-82f0-79f263645c03", "className": "com.open_care.dto.pharmacy.PharmacyDTO", "name": "PharmacyDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "31d50563-359b-4ee4-b8d8-ab2d961e729e", "name": "registeredLongitude", "title": "注册地址经度", "type": "java", "classType": "Double", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "4338e0c8-cfd3-4156-bc9d-760bfd0ccbac", "name": "creditCode", "title": "统一社会信用代码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "4ff44391-c6b9-4806-86a4-58e28c07c64d", "name": "businessLicense", "title": "营业执照", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "557ae850-db0a-4c95-9d84-881381f4b698", "name": "officePhone", "title": "办公电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "5898fda4-b601-4b11-bb54-9eb59cdf8ab9", "name": "updated", "title": "更新时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "5953c381-da14-4d4f-abcd-9ad1a8b5f30f", "name": "updatedBy", "title": "更新者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "5e180a25-d613-4c3c-b31b-2ba26a22046c", "name": "ocId", "title": "ocId", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": true, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "7cbd5bf4-c8f2-40b0-a193-1a9e80e82105", "name": "businessAddress", "title": "经营地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "807c480d-64d2-4307-9604-9fc02595597b", "name": "registeredLatitude", "title": "注册地址纬度", "type": "java", "classType": "Double", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "9a494773-40dc-4a37-ad7f-9eb7b3ae699c", "name": "enterpriseName", "title": "企业名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "a26c214a-747a-4bfb-a8cb-e73c2722df8b", "name": "businessLatitude", "title": "经营地址纬度", "type": "java", "classType": "Double", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "a6030294-5237-40a1-bdb1-749159c49b5d", "name": "registeredAddress", "title": "注册地址", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "a878138b-c394-4f5b-9853-412d50a402f3", "name": "created", "title": "创建时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "a98fc3f5-3a7f-41fd-b60a-14a003564a88", "name": "businessLongitude", "title": "经营地址经度", "type": "java", "classType": "Double", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "b579910c-1f86-41ea-8885-043424bdeff8", "name": "businessHours", "title": "营业时间", "type": "java", "classType": "BusinessHours", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "b9d4f745-b944-4e98-87ff-450f706ac763", "name": "medicalLicense", "title": "医疗许可证", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "be6dbba5-9b19-4c82-a636-9b037a1bec40", "name": "created<PERSON>y", "title": "创建者", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "e533b48f-5e64-4458-944e-c91b4f20f14f", "name": "updatedByName", "title": "更新人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}, {"id": "fcf34506-beed-42fc-9945-9da7a1a5a3b5", "name": "createdByName", "title": "创建人名字", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "115859a1-3936-4861-82f0-79f263645c03", "valid": true}]}