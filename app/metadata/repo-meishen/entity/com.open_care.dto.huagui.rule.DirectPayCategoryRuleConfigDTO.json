{"id": "62a12c7e-f6bc-4c53-a031-3f56158eae0b", "className": "com.open_care.dto.huagui.rule.DirectPayCategoryRuleConfigDTO", "name": "DirectPayCategoryRuleConfigDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "9653d894-8f06-4aae-a007-cb0ee0d8d0f8", "name": "executeAction", "title": "执行动作", "type": "java", "classType": "QueryBuilderRuleExpressionDTO", "refEntityId": "c2fdac30-fbbc-45af-80c3-a94179c958bc", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "62a12c7e-f6bc-4c53-a031-3f56158eae0b", "valid": true}, {"id": "bf0de748-5a47-4ebc-9862-4f6b48ecfce7", "name": "matchRule", "title": "匹配规则", "type": "java", "classType": "QueryBuilderRuleExpressionDTO", "refEntityId": "c2fdac30-fbbc-45af-80c3-a94179c958bc", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "62a12c7e-f6bc-4c53-a031-3f56158eae0b", "valid": true}]}