{"id": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "className": "com.open_care.dto.order.SaveOrderAccountPayResponseDTO", "name": "SaveOrderAccountPayResponseDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "0185bde2-dca3-41a9-9502-ac1148b17b26", "name": "internalTransactionNo", "title": "系统内部支付流水号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "02bf701a-73e8-438f-8270-c2feb0cfb10e", "name": "processingTime", "title": "处理耗时（毫秒）", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "04c21b58-ac5a-44c1-92b9-9b34e5526864", "name": "quotaDeductionSuccess", "title": "额度扣减成功标识", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "071b33ce-0398-4f4f-855a-46d15cf7a141", "name": "individualPayAmount", "title": "个人自付金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "07b467fa-148b-4c13-a067-21b6209434bd", "name": "paymentConfirmTime", "title": "支付确认时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "0a78255d-79d0-434c-a403-7775dd3446ab", "name": "quotaDeductionTime", "title": "额度扣减时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "1b1a0fab-a685-484f-a135-25585b978ebe", "name": "systemOrderNo", "title": "系统订单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "21684be2-b758-41d8-b20f-a65a5026a4c0", "name": "beforeQuotaAmount", "title": "扣减前客户可用额度", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "21c972c7-fbb2-4ead-965e-d2a5a15d8ac9", "name": "quotaDeductionMessage", "title": "额度扣减消息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "2c8be92e-5dce-4e7e-a565-a14daf0cb1c6", "name": "paymentStatus", "title": "支付状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "3d52e602-7b92-4467-996e-238c9e975f99", "name": "afterQuotaAmount", "title": "扣减后客户可用额度", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "4d1ba88c-2938-4883-b704-1956cf6c0961", "name": "notificationMessage", "title": "通知消息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "59e7c8d2-4f45-49c5-ab80-fb7eeda493e9", "name": "remark", "title": "备注信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "5ed90567-a3cb-4c24-85df-16787fd2dcfc", "name": "message", "title": "处理消息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "5efee055-af1c-4080-84f9-3a68d1edaa4d", "name": "systemProcessTime", "title": "系统处理时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "60066618-1468-47d8-aa0b-79080c3c9dfa", "name": "externalTransactionNo", "title": "外部支付流水号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "65715e51-ec03-4fbc-8d8f-222d21b68735", "name": "errorCode", "title": "错误码", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "78804e1c-2442-487e-8875-0e4e62c98ac4", "name": "transactionId", "title": "事务ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "953eb9a3-336b-4175-a671-0df5003f35a6", "name": "errorMessage", "title": "错误信息", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "ad6479b0-937d-446c-a6a2-2e5e3c01b1ae", "name": "success", "title": "处理成功标识", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "b27ce6e7-88ba-494f-ac5c-2ed87c8e5b60", "name": "orderStatus", "title": "订单状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "c09cbff2-fe6b-41b2-94a3-91eb1a36f8f7", "name": "paymentAmount", "title": "支付金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "c27ce154-0bde-4d66-8079-5067da396a82", "name": "paymentTime", "title": "支付时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "c7740ea6-bf8e-4fa7-a4f4-ec3fd53901a9", "name": "insuranceReimbursementAmount", "title": "保险报销金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "e58d7dbf-2662-415a-8e5c-4a105772db0c", "name": "processingNode", "title": "处理节点", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "ecdd0a75-fed6-42a5-81cf-85160a30a6a7", "name": "quotaDeductionAmount", "title": "额度扣减金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "ed0a1141-3cf1-42a2-af1c-cdfe11e7f218", "name": "serverTimestamp", "title": "服务器时间戳", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "f1fbbb78-7b36-4539-88bf-2ae575652008", "name": "businessSerialNo", "title": "业务流水号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "f6d2d19f-7974-4199-b69b-de4a64f2c3b1", "name": "paymentRecordId", "title": "支付记录ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "f899f2e9-51ac-474b-a850-021ed641e4fa", "name": "externalOrderNo", "title": "外部订单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}, {"id": "fe381276-43a2-4552-8449-cb1bbdee21ce", "name": "needCustomerNotification", "title": "是否需要发送客户通知", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "caa69e89-2cbd-498c-b7f4-74cdcccfbcc5", "valid": true}]}