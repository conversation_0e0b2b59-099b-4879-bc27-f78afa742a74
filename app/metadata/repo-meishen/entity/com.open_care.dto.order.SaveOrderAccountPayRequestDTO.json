{"id": "9fc1c868-3497-4468-a25b-63076c7a091f", "className": "com.open_care.dto.order.SaveOrderAccountPayRequestDTO", "name": "SaveOrderAccountPayRequestDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "043cecf9-48a3-4784-8de0-b465f3856467", "name": "paymentRemark", "title": "支付备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "14f92bec-c87a-436d-891c-ba2c22fcf4fb", "name": "payerPhone", "title": "付款人电话", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "3456366c-1ed7-4786-9923-b3b94fd6e3d8", "name": "individualPayAmount", "title": "个人自付金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "415ef86a-a25d-43a8-ab83-e85a82078aa7", "name": "paymentSignature", "title": "支付签名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "4fcdf327-10c4-4b4b-b222-49948291472b", "name": "paymentTime", "title": "支付时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "6d0790fc-ab89-40d5-bf87-b0b8659be5bd", "name": "paymentAmount", "title": "支付金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "6fe2ca86-a693-481e-96d1-528df145bf1f", "name": "payerName", "title": "付款人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "8eac5053-46ef-4320-86f1-2e12fcb9cc70", "name": "quotaDeductionAmount", "title": "额度扣减金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "9c639b71-9033-4c2b-9694-4459abef620b", "name": "systemOrderNo", "title": "系统订单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "a2a65bd9-d9d8-44ce-966e-9bed474bf1b3", "name": "externalTransactionNo", "title": "外部支付流水号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "adffad62-b92a-4c0e-9b94-1de49c37f017", "name": "insuranceReimbursementAmount", "title": "保险报销金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "b32abf15-f863-4e69-9c58-174b1b78660a", "name": "externalOrderNo", "title": "外部订单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "bf023450-c45d-4866-bc60-17f7cadb0331", "name": "paymentStatus", "title": "支付状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "c13f8591-d2a9-4658-8473-e08f60e1a49b", "name": "paymentChannel", "title": "支付渠道", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "d1c8057c-7507-4607-9b03-00068ddd1290", "name": "callbackData", "title": "外部支付平台回调数据", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "d27f7c2b-988f-453d-9c67-7f480663b71b", "name": "externalPaymentPlatform", "title": "外部支付平台", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "d55f612c-2508-4154-89e8-b6c2c9d0da4f", "name": "failureReason", "title": "支付失败原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "db0058f1-e3a8-4ade-a593-a65ae287dbce", "name": "paymentIp", "title": "支付IP", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "ed5f2621-19b4-4262-8c0b-1ef5b320c629", "name": "bankTransactionNo", "title": "银行流水号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "f4c617eb-33e9-48ad-852b-09840480b44a", "name": "paymentMethod", "title": "支付方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "f5d5ef73-0567-4e82-bec0-2cfcfcd636da", "name": "paymentConfirmTime", "title": "支付确认时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}, {"id": "f9775ea5-8188-4468-bef3-9d69daba5daa", "name": "paymentTerminal", "title": "支付终端", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "9fc1c868-3497-4468-a25b-63076c7a091f", "valid": true}]}