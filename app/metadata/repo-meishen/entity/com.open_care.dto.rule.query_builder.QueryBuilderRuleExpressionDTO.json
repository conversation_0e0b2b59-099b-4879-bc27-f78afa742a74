{"id": "c2fdac30-fbbc-45af-80c3-a94179c958bc", "className": "com.open_care.dto.rule.query_builder.QueryBuilderRuleExpressionDTO", "name": "QueryBuilderRuleExpressionDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "c7b09823-ba66-4bba-875a-d46c7d3dec23", "name": "treeJsonValue", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c2fdac30-fbbc-45af-80c3-a94179c958bc", "valid": true}, {"id": "d1981486-7371-4e7c-9f06-4b79970bca7c", "name": "jsonLogicValue", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "c2fdac30-fbbc-45af-80c3-a94179c958bc", "valid": true}]}