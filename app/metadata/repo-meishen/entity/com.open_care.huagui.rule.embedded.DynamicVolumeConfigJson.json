{"id": "d37010eb-b30d-41bc-8732-b9c744fb2e32", "className": "com.open_care.huagui.rule.embedded.DynamicVolumeConfigJson", "name": "DynamicVolumeConfigJson", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "208ed672-a8f1-46cf-a034-ff91338534c2", "name": "statisticsPeriod", "title": "统计周期", "type": "java", "classType": "StatisticsPeriodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "dd368e7cb05e9cda59d94748d1a2f84d91ff528d", "entityId": "d37010eb-b30d-41bc-8732-b9c744fb2e32", "valid": true}, {"id": "5bf2400c-74a9-4489-ab5d-74a143b4fb88", "name": "platformConfigs", "title": "平台配置列表", "type": "java", "classType": "object", "refEntityId": "b0ad962d-632d-4fd1-968b-7336114ea0e0", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "d37010eb-b30d-41bc-8732-b9c744fb2e32", "valid": true}, {"id": "7c1b155d-81f4-43d5-be1a-573a0798e26c", "name": "allocationStrategy", "title": "分配策略", "type": "java", "classType": "AllocationStrategyEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "9eebe1988648f87ee40dd659086539f33d147895", "entityId": "d37010eb-b30d-41bc-8732-b9c744fb2e32", "valid": true}]}