{"id": "ea6183b0-d1da-4bce-85e6-f9eba07efea0", "className": "com.open_care.dto.huagui.rule.PaymentPlatformPriorityDTO", "name": "PaymentPlatformPriorityDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "1c42b9bd-eb28-49d2-9a3a-143b59db7b47", "name": "priority", "title": "优先级", "type": "java", "classType": "Integer", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ea6183b0-d1da-4bce-85e6-f9eba07efea0", "valid": true}, {"id": "2403fe96-7abd-44b9-b2c3-8fd045498fd8", "name": "platformId", "title": "平台ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ea6183b0-d1da-4bce-85e6-f9eba07efea0", "valid": true}, {"id": "ed7e58aa-ca3a-4872-94bf-ab7a64a0f37e", "name": "platformName", "title": "平台名称", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "ea6183b0-d1da-4bce-85e6-f9eba07efea0", "valid": true}]}