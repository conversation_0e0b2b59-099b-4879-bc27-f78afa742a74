{"id": "77e625b8-1e45-45f0-97e4-50fb50b78b7f", "className": "com.open_care.dto.huagui.rule.DynamicVolumeConfigDTO", "name": "DynamicVolumeConfigDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "4f0e5124-d88d-419e-997a-6f01dd2ba7dc", "name": "statisticsPeriod", "title": "统计周期", "type": "java", "classType": "StatisticsPeriodEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "dd368e7cb05e9cda59d94748d1a2f84d91ff528d", "entityId": "77e625b8-1e45-45f0-97e4-50fb50b78b7f", "valid": true}, {"id": "b8ebb190-92f2-4375-ae4f-98e5e4d5c530", "name": "platformConfigs", "title": "平台配置列表", "type": "java", "classType": "DynamicPlatformConfigDTO", "refEntityId": "90e28305-223d-43e4-8e7c-3d3103663ce3", "collection": true, "standard": true, "visible": true, "identifier": false, "entityId": "77e625b8-1e45-45f0-97e4-50fb50b78b7f", "valid": true}, {"id": "ebe634fd-12a8-403a-bec8-1a5f7a9ac692", "name": "allocationStrategy", "title": "分配策略", "type": "java", "classType": "AllocationStrategyEnum", "collection": false, "standard": true, "visible": true, "identifier": false, "referenceId": "9eebe1988648f87ee40dd659086539f33d147895", "entityId": "77e625b8-1e45-45f0-97e4-50fb50b78b7f", "valid": true}]}