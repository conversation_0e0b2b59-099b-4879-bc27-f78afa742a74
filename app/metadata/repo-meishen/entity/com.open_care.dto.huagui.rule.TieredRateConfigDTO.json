{"id": "4a45ba07-2141-41aa-ad18-07a524c06ba1", "className": "com.open_care.dto.huagui.rule.TieredRateConfigDTO", "name": "TieredRateConfigDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "75dad047-0ad4-4872-809f-54c30b0393ed", "name": "matchRule", "title": "规则表达式", "type": "java", "classType": "QueryBuilderRuleExpressionDTO", "refEntityId": "c2fdac30-fbbc-45af-80c3-a94179c958bc", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4a45ba07-2141-41aa-ad18-07a524c06ba1", "valid": true}, {"id": "8433c49e-5096-4d9e-89b4-1d804530dae9", "name": "executeAction", "title": "动作表达式", "type": "java", "classType": "QueryBuilderRuleExpressionDTO", "refEntityId": "c2fdac30-fbbc-45af-80c3-a94179c958bc", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4a45ba07-2141-41aa-ad18-07a524c06ba1", "valid": true}, {"id": "9445563e-c107-42c2-b182-1a4a7e7123e6", "name": "ruleDescription", "title": "规则描述（仅前端使用）", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "4a45ba07-2141-41aa-ad18-07a524c06ba1", "valid": true}]}