{"id": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "className": "com.open_care.dto.order.RefundOrderAccountRequestDTO", "name": "RefundOrderAccountRequestDTO", "standard": true, "server": "open-care-healthcare-crm-service", "valid": true, "remoteServerName": "", "remoteEntityName": "", "fields": [{"id": "013cd447-f6ca-4746-8b64-454ff054f50e", "name": "externalPlatformVersion", "title": "外部平台版本", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "08caca80-5bc2-4985-b988-bab4223ba824", "name": "needApproval", "title": "是否需要审批", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "0e567319-8727-4ee3-8770-a3eeaacf55fb", "name": "applicantName", "title": "申请人姓名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "0ecc62d5-bc01-41ab-a620-db398a229a63", "name": "expectedCompleteTime", "title": "预计完成时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "0ff2b281-631d-40d2-8e3b-e7cbf3b8a813", "name": "extendField2", "title": "扩展字段2", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "12a65ed4-6f0d-4c6e-afa5-af938fde94c2", "name": "customerNotification", "title": "客户通知", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "1a5ef15a-ed97-4467-adac-3c804346bada", "name": "urgencyLevel", "title": "紧急程度", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "205285b5-5b08-4805-a2ca-fb964200ca8c", "name": "refundMethod", "title": "退费方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "21cb1aff-d4e3-4387-8877-6c330c5ffbb3", "name": "notificationContent", "title": "通知内容", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "223307f4-5028-4420-9a57-6c4c36b9ad03", "name": "refundTotalAmount", "title": "退费总金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "43ce9000-de90-4a0d-a1a2-83395cead80b", "name": "insuranceRefundAmount", "title": "保险报销退费金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "4e51cb9d-a713-4bc8-9260-930c412ba53c", "name": "externalRefundStatus", "title": "外部退费状态", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "58ab7fb0-f8d7-463f-9541-e31b4d689433", "name": "quotaRestoreRemark", "title": "额度恢复说明", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "5a9e3d76-c815-4aca-85cb-e15cdb9e6332", "name": "bankRefundTransactionNo", "title": "银行退费流水号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "5cc35d3b-d745-43f1-9ce3-13037504ca31", "name": "restoreQuota", "title": "是否恢复额度", "type": "java", "classType": "Boolean", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "5db3d0f6-42c7-40a7-aabc-fc7f5b0b5f08", "name": "refundType", "title": "退费类型", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "687b1835-e3ae-4863-b51c-341c3a2bcea4", "name": "externalPaymentPlatform", "title": "外部支付平台", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "70f9eaf4-727a-48b9-b979-3f5e280a8da3", "name": "requestTimestamp", "title": "请求时间戳", "type": "java", "classType": "<PERSON>", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "7e6123c2-2baa-41b3-b9df-e567eeed6c81", "name": "requestIp", "title": "请求IP", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "86622a13-047a-4770-bff8-f92aa7770819", "name": "externalOrderNo", "title": "外部订单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "8f104862-a160-4212-a40e-dff359279935", "name": "processRemark", "title": "处理备注", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "8fc133b4-5676-4c93-994e-28db200a50f1", "name": "platformSignature", "title": "平台签名", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "965f3e74-96e3-40bf-a2e7-462ee7012961", "name": "systemOrderNo", "title": "系统订单号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "9fb715d9-c85c-46f7-949e-8d12286e9333", "name": "applicantId", "title": "申请人ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "a1518dd8-7dcc-4488-a947-d23c727b850b", "name": "extendField1", "title": "扩展字段1", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "a279769d-2bc5-4d04-a7c1-087d134e9363", "name": "individualRefundAmount", "title": "个人自付退费金额", "type": "java", "classType": "BigDecimal", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "be46fe6a-02b8-4d06-9823-679890734cfb", "name": "notificationMethod", "title": "通知方式", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "c3e57545-9a42-4712-8552-66b7672cd22f", "name": "externalProcessTime", "title": "外部处理时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "ce83e9a7-82c5-4621-8a08-c0fce325e4c4", "name": "refundReason", "title": "退费原因", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "cf4e2af4-c478-494f-bbee-1d14dc664566", "name": "refundApplyTime", "title": "退费申请时间", "type": "java", "classType": "Date", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "cfc175f9-c9e6-462b-9c1b-17ebafa90e98", "name": "approvalWorkflowId", "title": "审批流程ID", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "f4b7de5e-10ae-4bba-87a3-8f785a857d24", "name": "externalRefundTransactionNo", "title": "外部退费流水号", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}, {"id": "fe2b1622-f62c-40be-9b4c-d3d109b0b96e", "name": "extendField3", "title": "扩展字段3", "type": "java", "classType": "String", "collection": false, "standard": true, "visible": true, "identifier": false, "entityId": "82c6821b-ec65-4b28-86e9-f12a7aecc046", "valid": true}]}