---
description: 
globs: 
alwaysApply: false
---

# API测试用例生成规则

## 上下文
- 适用于基于API文档生成RESTful API测试用例
- 确保API符合预期行为和规范
- 验证各种操作和边界条件
- 自动化API测试过程

## 要求
- 测试类必须继承BaseRestAssuredTest基类
- 测试类名应遵循{EntityName}ApiTest命名规范
- 为每个API端点创建相应的测试方法
- 测试方法名应清晰描述测试意图，以test开头
- 使用@DisplayName注解描述测试目的
- 实现以下基本测试场景:
  * 保存实体（正常情况）
  * 批量保存实体（如适用）
  * 查询实体列表（带分页和过滤）
  * 获取实体详情
  * 删除实体（如适用）
  * 必填字段校验
  * 字段可清空校验
  * 唯一性校验
  * 关联实体查询测试规范（如适用）
- 在@BeforeEach中准备测试数据
- 在@AfterEach中清理测试资源
- 使用RestAssured发送HTTP请求并验证响应
- 为所有测试数据创建专用的生成方法
- 验证HTTP状态码、响应状态和核心字段
- 关联实体测试需设置eagerProperties属性

## 测试方法模板

### 初始化与准备
```java
private String ocId;
private Map<String, Object> testData;
private static final String ENTITY_NAME = "com.open_care.dto.{package}.{EntityName}";
private static final String API_PATH = "/api";

@BeforeEach
public void setup() {
    prepareTestData();
}

private void prepareTestData() {
    testData = createTestData();
}

private Map<String, Object> createTestData() {
    Map<String, Object> data = new HashMap<>();
    // 生成唯一标识
    String uniqueId = UUID.randomUUID().toString().substring(0, 8);
    // 设置必填字段
    data.put("field1", "value1");
    data.put("field2", "value2" + uniqueId);
    // 设置选填字段
    data.put("field3", "value3");
    return data;
}
```

### 保存测试
```java
@Test
@DisplayName("测试保存{实体名称}")
public void testSave{EntityName}() {
    // 构建保存请求
    SaveRequestDTO saveRequest = new SaveRequestDTO();
    saveRequest.setEntityName(ENTITY_NAME);
    saveRequest.setData(testData);

    // 关联实体设置
    saveRequest.setEagerProperties("relationEntity");

    // 发送请求并验证结果
    Response response = given()
            .spec(requestSpec)
            .body(saveRequest)
            .when()
            .post(API_PATH + "/save")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"))
            .body("data", notNullValue())
            .extract()
            .response();

    // 保存创建的ID，用于后续测试
    ocId = response.jsonPath().getString("data.ocId");
    assertNotNull(ocId, "创建的{实体名称}ID不应为空");
}
```

### 查询测试
```java
@Test
@DisplayName("测试查询{实体名称}列表")
public void testQuery{EntityName}List() {
    // 先创建一个测试数据
    testSave{EntityName}();

    // 构建查询请求
    QueryRequestDTO queryRequest = new QueryRequestDTO();
    queryRequest.setEntityName(ENTITY_NAME);

    // 设置过滤条件
    List<FieldData> filters = new ArrayList<>();
    FieldData filter = new FieldData();
    filter.setFieldName("field1");
    filter.setOperator("equal");
    filter.setFieldValue("value1");
    filters.add(filter);
    queryRequest.setFilters(filters);

    // 设置分页信息
    queryRequest.setPagination(QueryUtils.page(10, 1));

    // 设置排序
    Map<String, String> sorter = new HashMap<>();
    sorter.put("ocId", "descend");
    queryRequest.setSorter(sorter);

    // 关联属性
    queryRequest.setEagerProperties("relationEntity");

    // 发送请求并验证结果
    given()
        .spec(requestSpec)
        .body(queryRequest)
        .when()
        .post(API_PATH + "/query")
        .then()
        .statusCode(HttpStatus.OK.value())
        .body("status", equalTo("0"))
        .body("data.data", notNullValue())
        .body("data.pagination", notNullValue())
        .body("data.pagination.current", equalTo(1))
        .body("data.pagination.pageSize", equalTo(10));
}
```

### 获取详情测试
```java
@Test
@DisplayName("测试获取{实体名称}详情")
public void testGet{EntityName}() {
    // 先创建一个测试数据
    testSave{EntityName}();

    // 发送请求并验证结果
    given()
            .spec(requestSpec)
            .body(new HashMap<>())
            .when()
            .post(API_PATH + "/get/" + ENTITY_NAME + "/" + ocId)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"))
            .body("data.data", notNullValue())
            .body("data.data.ocId", equalTo(ocId));
}
```

### 必填字段验证测试
```java
@Test
@DisplayName("测试保存{实体名称}-必填字段校验")
public void testSave{EntityName}Validation() {
    // 创建缺少必填字段的数据
    Map<String, Object> invalidData = new HashMap<>();
    invalidData.put("field3", "value3"); // 只提供非必填字段

    // 构建保存请求
    SaveRequestDTO saveRequest = new SaveRequestDTO();
    saveRequest.setEntityName(ENTITY_NAME);
    saveRequest.setData(invalidData);

    // 发送请求并验证错误响应
    given()
            .spec(requestSpec)
            .body(saveRequest)
            .when()
            .post(API_PATH + "/save")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("-1"))
            .body("msg", notNullValue());
}
```

### 批量保存测试(如适用)
```java
@Test
@DisplayName("测试批量保存{实体名称}")
public void testBatchSave{EntityName}() {
    // 创建批量测试数据
    List<Map<String, Object>> batchData = createBatchTestData();

    // 构建批量保存请求
    List<SaveRequestDTO> saveRequests = new ArrayList<>();
    for (Map<String, Object> data : batchData) {
        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(data);
        saveRequests.add(saveRequest);
    }

    // 发送请求并验证结果
    given()
            .spec(requestSpec)
            .body(saveRequests)
            .when()
            .post(API_PATH + "/batch/save")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"))
            .body("data", notNullValue());
}

private List<Map<String, Object>> createBatchTestData() {
    List<Map<String, Object>> batchData = new ArrayList<>();
    // 创建多条测试数据
    for (int i = 0; i < 2; i++) {
        batchData.add(createTestData());
    }
    return batchData;
}
```

### 删除测试(如适用)
```java
@Test
@DisplayName("测试删除{实体名称}")
public void testDelete{EntityName}() {
    // 先创建一个测试数据
    testSave{EntityName}();

    // 构建删除请求
    Map<String, Object> deleteRequest = new HashMap<>();
    deleteRequest.put("entityName", ENTITY_NAME);
    List<String> ids = new ArrayList<>();
    ids.add(ocId);
    deleteRequest.put("ids", ids);

    // 发送请求并验证结果
    given()
            .spec(requestSpec)
            .body(deleteRequest)
            .when()
            .post(API_PATH + "/delete")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"));

    // 验证删除是否成功
    given()
            .spec(requestSpec)
            .body(new HashMap<>())
            .when()
            .post(API_PATH + "/get/" + ENTITY_NAME + "/" + ocId)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("-1"));
}
```

### 唯一性校验
#### 适用范围：所有具有唯一性约束的字段（如唯一索引、唯一标识、业务唯一字段等）。
#### 测试要求：
 - 必须为每个唯一性字段编写重复保存的测试用例。
 - 场景包括：先保存一条数据，再用相同唯一字段保存另一条数据，断言接口返回唯一性冲突的错误（如 status = -1，msg 包含唯一性字段提示）。
```java
@Test
@DisplayName("测试保存实体-唯一性校验")
public void testSaveEntityWithDuplicateUniqueField() {
      // 第一次保存
      Map<String, Object> data1 = createTestData();
      String uniqueValue = (String) data1.get("uniqueField");
      saveEntity(data1);

      // 第二次保存，使用相同唯一字段
      Map<String, Object> data2 = createTestData();
      data2.put("uniqueField", uniqueValue);
      given()
          .spec(requestSpec)
          .body(data2)
          .when()
          .post(API_PATH + "/save")
          .then()
          .statusCode(HttpStatus.OK.value())
          .body("status", equalTo("-1"))
          .body("msg", containsString("唯一性"));
}
```

### 字段可清空校验
#### 适用范围：所有允许置空（可选填）的字段。
#### 测试要求：
 - 必须测试字段先有值，再清空（设为""或null）后保存，断言数据库中该字段已为空。
 - 场景包括：首次保存时字段有值，二次保存时字段置空，查询详情断言该字段为空。
```java
@Test
@DisplayName("测试字段可清空")
public void testClearField() {
      // 第一次保存，字段有值
      Map<String, Object> data = createTestData();
      data.put("fieldName", "有值");
      String id = saveEntityAndGetId(data);

      // 验证已保存
      assertEquals("有值", getEntityDetail(id).get("fieldName"));

      // 第二次保存，字段清空
      data.put("id", id);
      data.put("fieldName", "");
      saveEntity(data);

      // 验证已清空
      assertTrue(getEntityDetail(id).get("fieldName") == null || "".equals(getEntityDetail(id).get("fieldName")));
}
```

### 关联实体查询测试规范
#### 适用范围：所有包含关联实体的API测试用例，特别是需要通过关联实体字段进行查询过滤的场景。
#### 测试要求：
 - 关联实体查询必须使用organization.fieldName格式指定关联实体字段
 - 必须测试至少一个关联实体精确匹配查询(DbOperator.EQ)
 - 创建测试数据时应确保不同关联属性值的对比样本
 - 关联查询应明确设置必要的排序条件以保证结果稳定
```java
@Test
@DisplayName("测试关联实体查询功能")
public void testRelationEntityQuery() {
    // 1. 创建不同属性值的多个测试数据
    // 2. 使用关联实体字段进行查询过滤
    // 3. 验证查询结果中的关联实体属性值符合预期
    
    // 创建测试数据
    List<String> relatedValues = Arrays.asList("值1", "值2");
    
    for (int i = 0; i < relatedValues.size(); i++) {
        // 创建主实体数据
        MainEntity entity = createTestData();
        // 设置关联实体的属性值
        entity.getRelationEntity().setField(relatedValues.get(i));
        
        // 保存测试数据
        saveEntity(entity);
    }
    
    // 构建关联查询
    QueryRequestDTO queryRequest = new QueryRequestDTO();
    queryRequest.setEntityName(ENTITY_NAME);
    
    // 设置过滤条件 - 按关联实体字段查询
    List<FieldData> filters = new ArrayList<>();
    FieldData relatedFilter = new FieldData();
    relatedFilter.setFieldName("relationEntity.field");
    relatedFilter.setOperator(DbOperator.EQ.getOpt());
    relatedFilter.setFieldValue("值1");
    filters.add(relatedFilter);
    
    queryRequest.setFilters(filters);
    
    // 执行查询并验证结果
    Response response = given()
            .spec(requestSpec)
            .body(queryRequest)
            .when()
            .post(API_PATH + QUERY_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
            .extract()
            .response();
    
    // 验证查询结果
    List<Map> results = response.jsonPath().getList(FIELD_DATA_DATA, Map.class);
    assertEquals(1, results.size(), "应该只返回1条匹配记录");
    
    // 验证结果中的关联实体属性
    Map<String, Object> result = results.get(0);
    Map<String, Object> relationEntity = (Map<String, Object>) result.get("relationEntity");
    assertEquals("值1", relationEntity.get("field"), "查询结果中关联实体的属性应匹配查询条件");
}
```

### 排序功能测试规范
#### 适用范围：所有支持排序功能的API测试用例。
#### 测试要求：
 - 必须测试至少一个字段的升序排序和降序排序
 - 使用Collections.sort进行本地排序验证服务端排序结果的正确性
 - 当测试分页与排序结合时，验证跨页数据排序的连贯性
 - 对于时间类型字段的排序，应添加适当延迟确保测试数据排序明确
```java
@Test
@DisplayName("测试实体排序功能")
public void testEntitySorting() {
    // 1. 创建多条有排序差异的测试数据
    // 2. 分别测试升序和降序排序
    // 3. 验证排序结果符合预期
    
    // 创建测试数据
    List<String> testItems = new ArrayList<>();
    for (int i = 1; i <= 3; i++) {
        // 创建测试数据并保存
        Entity entity = createTestData();
        entity.setSortField("值" + i);
        saveEntity(entity);
        
        // 确保创建时间有差异
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    // 测试升序排序
    QueryRequestDTO ascRequest = new QueryRequestDTO();
    ascRequest.setEntityName(ENTITY_NAME);
    
    // 设置排序，升序
    Map<String, String> ascSorter = new HashMap<>();
    ascSorter.put("sortField", "asc");
    ascRequest.setSorter(ascSorter);
    
    // 执行查询并验证结果
    Response ascResponse = given()
            .spec(requestSpec)
            .body(ascRequest)
            .when()
            .post(API_PATH + QUERY_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .extract()
            .response();
    
    // 验证排序结果
    List<Map> ascResults = ascResponse.jsonPath().getList(FIELD_DATA_DATA, Map.class);
    List<String> sortedValues = ascResults.stream()
            .map(item -> (String) item.get("sortField"))
            .collect(Collectors.toList());
    
    // 验证升序排列
    List<String> expectedAscValues = new ArrayList<>(sortedValues);
    Collections.sort(expectedAscValues);
    assertEquals(expectedAscValues, sortedValues, "数据应按升序排列");
    
    // 测试降序排序
    QueryRequestDTO descRequest = new QueryRequestDTO();
    descRequest.setEntityName(ENTITY_NAME);
    
    // 设置排序，降序
    Map<String, String> descSorter = new HashMap<>();
    descSorter.put("sortField", "desc");
    descRequest.setSorter(descSorter);
    
    // 执行查询并验证结果
    Response descResponse = given()
            .spec(requestSpec)
            .body(descRequest)
            .when()
            .post(API_PATH + QUERY_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .extract()
            .response();
    
    // 验证排序结果
    List<Map> descResults = descResponse.jsonPath().getList(FIELD_DATA_DATA, Map.class);
    List<String> descValues = descResults.stream()
            .map(item -> (String) item.get("sortField"))
            .collect(Collectors.toList());
    
    // 验证降序排列
    List<String> expectedDescValues = new ArrayList<>(descValues);
    Collections.sort(expectedDescValues, Collections.reverseOrder());
    assertEquals(expectedDescValues, descValues, "数据应按降序排列");
}
```

### 分页功能测试规范
#### 适用范围：所有支持分页功能的API测试用例。
#### 测试要求：
 - 测试至少包含标准页大小(如10条)和自定义页大小的分页场景
 - 验证多页数据时，必须校验页间数据的完整性和非重复性
 - 测试超出范围的页码请求，验证系统返回空数据集而非错误
 - 当多页结合排序时，需验证跨页数据的排序连贯性
 - 验证分页元数据(总数、当前页、页大小)的正确性
```java
@Test
@DisplayName("测试分页功能")
public void testPagination() {
    // 1. 创建足够多的测试数据以测试分页
    // 2. 测试不同页码和页大小
    // 3. 验证分页数据的完整性和准确性
    
    // 创建测试数据(至少超过一页)
    for (int i = 1; i <= 12; i++) {
        Entity entity = createTestData();
        entity.setName("测试分页" + i);
        saveEntity(entity);
    }
    
    // 第一页查询 - 验证第一页数据
    QueryRequestDTO firstPageRequest = new QueryRequestDTO();
    firstPageRequest.setEntityName(ENTITY_NAME);
    firstPageRequest.setPagination(QueryUtils.page(10, 1));
    
    Response firstPageResponse = given()
            .spec(requestSpec)
            .body(firstPageRequest)
            .when()
            .post(API_PATH + QUERY_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body(FIELD_PAGINATION_CURRENT, equalTo(1))
            .body(FIELD_PAGINATION_PAGE_SIZE, equalTo(10))
            .extract()
            .response();
    
    List<Map> firstPageList = firstPageResponse.jsonPath().getList(FIELD_DATA_DATA, Map.class);
    assertEquals(10, firstPageList.size(), "第一页应返回10条记录");
    
    int total = firstPageResponse.jsonPath().getInt("data.pagination.total");
    assertTrue(total >= 12, "总记录数应至少为12");
    
    // 第二页查询 - 验证第二页数据
    QueryRequestDTO secondPageRequest = new QueryRequestDTO();
    secondPageRequest.setEntityName(ENTITY_NAME);
    secondPageRequest.setPagination(QueryUtils.page(10, 2));
    
    Response secondPageResponse = given()
            .spec(requestSpec)
            .body(secondPageRequest)
            .when()
            .post(API_PATH + QUERY_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("data.pagination.current", equalTo(2))
            .extract()
            .response();
    
    List<Map> secondPageList = secondPageResponse.jsonPath().getList(FIELD_DATA_DATA, Map.class);
    assertEquals(2, secondPageList.size(), "第二页应返回2条记录");
    
    // 验证分页数据的完整性 - 确保第一页和第二页的数据没有重复
    List<String> firstPageIds = firstPageList.stream()
            .map(item -> (String) item.get("ocId"))
            .collect(Collectors.toList());
    
    List<String> secondPageIds = secondPageList.stream()
            .map(item -> (String) item.get("ocId"))
            .collect(Collectors.toList());
    
    // 验证两页数据没有交集
    firstPageIds.retainAll(secondPageIds);
    assertTrue(firstPageIds.isEmpty(), "第一页和第二页的数据不应有重复");
    
    // 测试自定义页大小
    int customPageSize = 5;
    QueryRequestDTO customPageSizeRequest = new QueryRequestDTO();
    customPageSizeRequest.setEntityName(ENTITY_NAME);
    customPageSizeRequest.setPagination(QueryUtils.page(customPageSize, 1));
    
    Response customPageSizeResponse = given()
            .spec(requestSpec)
            .body(customPageSizeRequest)
            .when()
            .post(API_PATH + QUERY_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body(FIELD_PAGINATION_PAGE_SIZE, equalTo(customPageSize))
            .extract()
            .response();
    
    List<Map> customPageSizeList = customPageSizeResponse.jsonPath().getList(FIELD_DATA_DATA, Map.class);
    assertEquals(customPageSize, customPageSizeList.size(), "自定义页大小应返回指定条数记录");
}

```

## 示例

<example>
/**
 * 客户信息API测试类
 * 基于Customer-API.md文档进行测试
 */
public class CustomerApiTest extends BaseRestAssuredTest {

    private String ocId;
    private Map<String, Object> testCustomerData;
    private static final String ENTITY_NAME = "com.open_care.dto.customer.CustomerDTO";
    private static final String API_PATH = "/api";

    @BeforeEach
    public void setup() {
        prepareTestData();
    }

    private void prepareTestData() {
        testCustomerData = createCustomerTestData();
    }

    private Map<String, Object> createCustomerTestData() {
        Map<String, Object> customerData = new HashMap<>();

        // 生成唯一标识
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);

        // 设置必填字段
        customerData.put("name", "测试客户" + uniqueId);
        customerData.put("type", "PERSONAL");
        customerData.put("status", "ACTIVE");

        // 设置选填字段
        customerData.put("phone", "13800138000");
        customerData.put("email", "test" + uniqueId + "@example.com");
        customerData.put("address", "北京市朝阳区");

        return customerData;
    }

    @Test
    @DisplayName("测试保存客户信息")
    public void testSaveCustomer() {
        // 构建保存请求
        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(testCustomerData);

        // 发送请求并验证结果
        Response response = given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + "/save")
                .then()
                .statusCode(HttpStatus.OK.value())
                .body("status", equalTo("0"))
                .body("data", notNullValue())
                .body("data.name", equalTo(testCustomerData.get("name")))
                .extract()
                .response();

        // 保存创建的ID，用于后续测试
        ocId = response.jsonPath().getString("data.ocId");

        assertNotNull(ocId, "创建的客户ID不应为空");
    }

    @Test
    @DisplayName("测试查询客户列表")
    public void testQueryCustomerList() {
        // 先创建一个测试数据
        testSaveCustomer();

        // 构建查询请求
        QueryRequestDTO queryRequest = new QueryRequestDTO();
        queryRequest.setEntityName(ENTITY_NAME);

        // 设置过滤条件
        List<FieldData> filters = new ArrayList<>();
        FieldData typeFilter = new FieldData();
        typeFilter.setFieldName("type");
        typeFilter.setOperator("equal");
        typeFilter.setFieldValue("PERSONAL");
        filters.add(typeFilter);

        queryRequest.setFilters(filters);

        // 设置分页信息
        queryRequest.setPagination(QueryUtils.page(10, 1));

        // 设置排序
        Map<String, String> sorter = new HashMap<>();
        sorter.put("ocId", "descend");
        queryRequest.setSorter(sorter);

        // 发送请求并验证结果
        given()
            .spec(requestSpec)
            .body(queryRequest)
            .when()
            .post(API_PATH + "/query")
            .then()
            .statusCode(HttpStatus.OK.value())
            .body("status", equalTo("0"))
            .body("data.data", notNullValue())
            .body("data.pagination", notNullValue());
    }

    @AfterEach
    public void tearDown() {
        // 清理测试资源
        System.out.println("测试完成，测试客户ID: " + ocId);
    }
}
</example>

<example type="invalid">
public class CustomerApiTest {

    @Test
    public void testSave() {
        // 直接使用HTTP客户端而不是RestAssured
        HttpClient client = HttpClient.newHttpClient();
        // 不使用统一的请求规范
        // 无明确的测试数据准备
        // 没有响应验证
    }

    @Test
    public void testQuery() {
        // 没有构建标准的查询请求
        // 没有设置过滤、分页、排序
        // 没有验证响应
    }
}
</example>

<example type="invalid">
public class CustomerApiTest extends BaseRestAssuredTest {

    // 缺少测试数据准备
    // 没有继承BaseRestAssuredTest
    // 没有定义实体名称和API路径常量

    @Test
    public void testSaveCustomer() {
        // 直接构造JSON字符串而不是使用SaveRequestDTO
        String jsonBody = "{\"name\":\"测试客户\"}";

        given()
            .body(jsonBody)
            .post("/api/save");
        // 没有验证响应
        // 没有保存创建的ID用于后续测试
    }
}
</example>