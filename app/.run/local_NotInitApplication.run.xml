<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="local_NotInitApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
    <module name="app.open-care-app-server.main" />
    <option name="PROGRAM_PARAMETERS" value="--spring.application.name=open-care-healthcare-crm-service --initSchema=false --repo=repo-meishen --server.port=8635 --server.host=host.docker.internal --sever.self=${server.host} --arthas.telnet-port=3575 --spring.cloud.nacos.discovery.server-addr=${server.host}:8848 --spring.cloud.nacos.discovery.ip=${sever.self} --spring.data.redis.host=${server.host} --spring.datasource.url=jdbc:postgresql://${server.host}:54321/sunshine?currentSchema=open_care_application --spring.datasource.password=123456 --seata.enabled=false --spring.pulsar.client.service-url=pulsar://${server.host}:6650 --dubbo.application.qos-port=22222 --powerjob.worker.enabled=true --powerjob.worker.server-address=${server.host}:7700 --web.filePath=$USER_HOME$/yxTech/open-care/mounts/file/ --web.uploadFilePath=$USER_HOME$/yxTech/open-care/mounts/file/" />
    <option name="SPRING_BOOT_MAIN_CLASS" value="com.open_care.Application" />
    <method v="2" />
  </configuration>
</component>