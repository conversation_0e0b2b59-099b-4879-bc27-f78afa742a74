package com.open_care.huagui.rule.embedded;

import com.open_care.annotation.OcColumn;
import com.open_care.core.IBaseJsonObject;
import com.open_care.enums.huagui.AllocationStrategyEnum;
import com.open_care.enums.huagui.StatisticsDimensionEnum;
import com.open_care.enums.huagui.StatisticsPeriodEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 动态单量配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicVolumeConfigJson implements IBaseJsonObject {
    
    @OcColumn(title = "分配策略")
    private AllocationStrategyEnum allocationStrategy;

    @OcColumn(title = "时间周期")
    private StatisticsPeriodEnum statisticsPeriod;
    
    @OcColumn(title = "平台配置列表")
    private List<DynamicPlatformConfigJson> platformConfigs;
} 