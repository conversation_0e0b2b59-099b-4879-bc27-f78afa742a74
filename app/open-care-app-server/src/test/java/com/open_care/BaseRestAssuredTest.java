package com.open_care;

import cn.hutool.core.util.StrUtil;
import io.restassured.RestAssured;
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.builder.ResponseSpecBuilder;
import io.restassured.filter.log.LogDetail;
import io.restassured.http.ContentType;
import io.restassured.specification.RequestSpecification;
import io.restassured.specification.ResponseSpecification;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;

/**
 * REST Assured测试基类
 * 提供通用的REST测试配置和工具方法
 * 纯REST API测试，不启动Spring上下文
 */
public abstract class BaseRestAssuredTest {

    protected RequestSpecification requestSpec;
    protected ResponseSpecification responseSpec;
//    protected static final String BASE_PATH = "/api";

    // 测试通用的认证令牌
    protected static final String AUTH_TOKEN = "test-token";

    private static final String BASE_HOST = "host.docker.internal";
    private static final String BASE_URL = StrUtil.format("http://{}", BASE_HOST);

    // 数据库备份和恢复的URL
    private static final String BACKUP_URL = StrUtil.format("{}:8080/backup", BASE_URL);
    private static final String RESTORE_URL = StrUtil.format("{}:8080/restore", BASE_URL);

    //常量
    public static final String API_PATH = "/api";
    public static final String SAVE_ENDPOINT = "/save";
    public static final String QUERY_ENDPOINT = "/query";
    public static final String GET_ENDPOINT = "/get";

    public static final String FIELD_STATUS = "status";
    public static final String FIELD_MSG = "msg";
    public static final String FIELD_DATA = "data";
    public static final String FIELD_DATA_DATA = "data.data";
    public static final String FIELD_DATA_PAGINATION = "data.pagination";
    public static final String FIELD_PAGINATION_CURRENT = "data.pagination.current";
    public static final String FIELD_PAGINATION_PAGE_SIZE = "data.pagination.pageSize";
    public static final String FIELD_OC_ID = "data.data.ocId";

    public static final String STATUS_SUCCESS = "0";
    public static final String STATUS_ERROR = "-1";

    // 分页常量
    public static final int DEFAULT_PAGE_SIZE = 10;
    public static final int DEFAULT_PAGE_NUM = 1;

    @BeforeEach
    public void setupTest() {
        //TODO 一次执行多个测试用例时，前一个测试用例执行完成且restoreDatabase后，需要延迟一段时间再执行下一个测试用例，否则会提示：java.lang.AssertionError at Constructor.java:499
        try {
            Thread.sleep(10000);
        } catch (Exception e) {
            throw new IllegalStateException(e.getMessage());
        }

        // 配置REST Assured基础URL
        RestAssured.baseURI = BASE_URL;
        RestAssured.port = 80;

        String cookie = "OAUTH2SESSION=NmM0NzdiMTctZDAyMi00YTJkLTlkNDMtZTBkZTg2NjJmMGRm; XSRF-TOKEN=0a67613f-c989-454a-b02a-cec9c8bd851f";

        // 创建通用请求规范
        requestSpec = new RequestSpecBuilder()
                .setContentType(ContentType.JSON)
                .addHeader("Cookie", cookie)
                //.addHeader("userInfo", "{\"userId\":\"d977fc67-8477-4289-b767-dad84e89b13d\",\"roles\":[\"737f5583-e63f-4f6e-8fff-94bd70ecb564\"]}")
                .log(LogDetail.ALL)
                .build();

        // 创建通用响应规范
        responseSpec = new ResponseSpecBuilder()
                .expectContentType(ContentType.JSON)
                .log(LogDetail.ALL)
                .build();

        // 执行数据库备份
        backupDatabase();
    }

    @AfterEach
    public void tearDown() {
        // 测试结束后恢复数据库
        restoreDatabase();
    }

    /**
     * 执行数据库备份
     */
    private void backupDatabase() {
        if(!enableDataBaseBackupAndRestore()){
            return;
        }
        RestAssured.given()
                .contentType(ContentType.JSON)
                .when()
                .post(BACKUP_URL)
                .then()
                .statusCode(200);
    }

    /**
     * 执行数据库恢复
     */
    private void restoreDatabase() {
        if(!enableDataBaseBackupAndRestore()){
            return;
        }
        RestAssured.given()
                .contentType(ContentType.JSON)
                .when()
                .post(RESTORE_URL)
                .then()
                .statusCode(200);
    }

    protected boolean enableDataBaseBackupAndRestore(){
        return true;
    }
}
