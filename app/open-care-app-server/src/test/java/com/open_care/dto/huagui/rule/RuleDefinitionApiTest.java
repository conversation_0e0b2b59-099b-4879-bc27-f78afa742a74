package com.open_care.dto.huagui.rule;

import com.open_care.BaseRestAssuredTest;
import com.open_care.dto.common.QueryRequestDTO;
import com.open_care.dto.common.SaveRequestDTO;
import com.open_care.dto.common.FieldData;
import com.open_care.dto.rule.query_builder.QueryBuilderRuleExpressionDTO;
import com.open_care.enums.huagui.*;
import com.open_care.utils.QueryUtils;
import io.restassured.response.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.*;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 规则定义DTO API测试类
 * 测试规则定义的保存、查询、获取、删除功能
 * 包含四种规则类型的完整测试覆盖和验证注解测试
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RuleDefinitionApiTest extends BaseRestAssuredTest {

    private String ocId;
    private Map<String, Object> testData;
    private static final String ENTITY_NAME = "com.open_care.dto.huagui.rule.RuleDefinitionDTO";
    
    // 从数据库查询的真实QueryBuilder数据
    private QueryBuilderRuleExpressionDTO realMatchRule;
    private QueryBuilderRuleExpressionDTO realExecuteAction;

    @BeforeEach
    public void setup() {
        prepareTestData();
    }

    private void prepareTestData() {
        // 准备真实的QueryBuilder数据
        prepareRealQueryBuilderData();
        
        // 创建测试数据
        testData = createTestData();
    }

    /**
     * 准备真实的QueryBuilder数据（从数据库中获取）
     */
    private void prepareRealQueryBuilderData() {
        realMatchRule = QueryBuilderRuleExpressionDTO.builder()
            .treeJsonValue("{\"id\":\"889a89ab-89ab-4cde-b012-31975d7a156e\",\"type\":\"group\",\"properties\":{\"conjunction\":\"AND\"},\"children1\":{\"b88abb8a-89ab-4cde-b012-31975d7a1f2a\":{\"type\":\"rule\",\"properties\":{\"field\":\"drugCategory\",\"operator\":\"select_any_in\",\"value\":[{\"compoisteValues\":[{\"key\":\"CLASS_A\",\"label\":\"甲类药品\"},{\"key\":\"CLASS_B\",\"label\":\"乙类药品\"},{\"key\":\"CLASS_C\",\"label\":\"丙类药品\"}]}],\"valueSrc\":[\"value\"],\"valueError\":[null],\"valueType\":[\"multiselect\"]}}}}")
            .jsonLogicValue("{\"and\":[{\"in\":[{\"var\":\"drugCategory\"},[\"CLASS_A\",\"CLASS_B\",\"CLASS_C\"]]}]}")
            .build();
            
        realExecuteAction = QueryBuilderRuleExpressionDTO.builder()
            .treeJsonValue("{\"id\":\"8baa88a9-89ab-4cde-b012-31975df12d50\",\"type\":\"group\",\"properties\":{\"conjunction\":\"AND\"},\"children1\":{\"9899b8aa-cdef-4012-b456-71975df23c44\":{\"type\":\"rule\",\"properties\":{\"field\":{\"func\":\"isDirectPayAllowed\",\"args\":{\"allowDirectPay\":{\"value\":\"true\"}},\"fieldName\":\"isDirectPayAllowed\"},\"operator\":\"equal\",\"value\":[true],\"valueSrc\":[\"value\"],\"valueError\":[null],\"valueType\":[\"boolean\"]}}}}")
            .jsonLogicValue("{\"and\":[{\"==\":[{\"isDirectPayAllowed\":[\"true\"]},true]}]}")
            .build();
    }

    /**
     * 创建测试数据
     */
    private Map<String, Object> createTestData() {
        Map<String, Object> data = new HashMap<>();
        
        // 生成唯一标识
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        
        // 设置基础必填字段
        data.put("ruleName", "直付品类规则_" + uniqueId);
        data.put("ruleDescription", "测试规则描述_" + uniqueId);
        data.put("ruleType", RuleTypeEnum.DIRECT_PAY_CATEGORY);
        data.put("effectiveTime", new Date());
        
        // 设置失效时间为30天后
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        data.put("expiryTime", calendar.getTime());
        
        // 设置直付品类规则配置
        Map<String, Object> directPayCategoryConfig = new HashMap<>();
        directPayCategoryConfig.put("matchRule", convertToMap(realMatchRule));
        directPayCategoryConfig.put("executeAction", convertToMap(realExecuteAction));
        data.put("directPayCategoryConfig", directPayCategoryConfig);
        
        return data;
    }

    /**
     * 将QueryBuilderRuleExpressionDTO转换为Map
     */
    private Map<String, Object> convertToMap(QueryBuilderRuleExpressionDTO dto) {
        Map<String, Object> map = new HashMap<>();
        map.put("treeJsonValue", dto.getTreeJsonValue());
        map.put("jsonLogicValue", dto.getJsonLogicValue());
        return map;
    }

    @Test
    @DisplayName("测试保存直付品类规则")
    public void testSaveDirectPayCategoryRule() {
        // 构建保存请求
        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(testData);

        // 发送请求并验证结果
        Response response = given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
                .body(FIELD_DATA, notNullValue())
                .body("data.ruleName", equalTo(testData.get("ruleName")))
                .body("data.ruleType", equalTo(testData.get("ruleType").toString()))
                .extract()
                .response();

        // 保存创建的ID，用于后续测试
        ocId = response.jsonPath().getString("data.ocId");
        assertNotNull(ocId, "创建的规则ID不应为空");
    }

    @Test
    @DisplayName("测试保存直付机构规则")
    public void testSaveDirectPayInstitutionRule() {
        // 创建直付机构规则数据
        Map<String, Object> institutionData = createDirectPayInstitutionRuleData();
        
        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(institutionData);

        Response response = given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
                .body(FIELD_DATA, notNullValue())
                .extract()
                .response();

        String institutionRuleId = response.jsonPath().getString("data.ocId");
        assertNotNull(institutionRuleId, "创建的直付机构规则ID不应为空");
    }

    @Test
    @DisplayName("测试保存计费规则")
    public void testSaveBillingRule() {
        // 创建计费规则数据
        Map<String, Object> billingData = createBillingRuleData();
        
        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(billingData);

        Response response = given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
                .body(FIELD_DATA, notNullValue())
                .extract()
                .response();

        String billingRuleId = response.jsonPath().getString("data.ocId");
        assertNotNull(billingRuleId, "创建的计费规则ID不应为空");
    }

    @Test
    @DisplayName("测试保存结算规则")
    public void testSaveSettlementRule() {
        // 创建结算规则数据
        Map<String, Object> settlementData = createSettlementRuleData();
        
        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(settlementData);

        Response response = given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
                .body(FIELD_DATA, notNullValue())
                .extract()
                .response();

        String settlementRuleId = response.jsonPath().getString("data.ocId");
        assertNotNull(settlementRuleId, "创建的结算规则ID不应为空");
    }

    /**
     * 创建直付机构规则数据
     */
    private Map<String, Object> createDirectPayInstitutionRuleData() {
        Map<String, Object> data = new HashMap<>();
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        
        data.put("ruleName", "直付机构规则_" + uniqueId);
        data.put("ruleDescription", "测试直付机构规则描述_" + uniqueId);
        data.put("ruleType", RuleTypeEnum.DIRECT_PAY_INSTITUTION);
        data.put("effectiveTime", new Date());
        
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        data.put("expiryTime", calendar.getTime());
        
        // 设置直付机构规则配置
        Map<String, Object> institutionConfig = new HashMap<>();
        institutionConfig.put("strategy", PriorityStrategyEnum.FIXED_PRIORITY);
        
        // 设置固定优先级配置
        Map<String, Object> fixedPriorityConfig = new HashMap<>();
        List<Map<String, Object>> platformPriorities = new ArrayList<>();
        
        Map<String, Object> platform1 = new HashMap<>();
        platform1.put("platformName", "普跃健康_" + uniqueId);
        platform1.put("priority", 1);
        platform1.put("enabled", true);
        platformPriorities.add(platform1);
        
        Map<String, Object> platform2 = new HashMap<>();
        platform2.put("platformName", "亿保健康_" + uniqueId);
        platform2.put("priority", 2);
        platform2.put("enabled", true);
        platformPriorities.add(platform2);
        
        fixedPriorityConfig.put("platformPriorities", platformPriorities);
        institutionConfig.put("fixedPriorityConfig", fixedPriorityConfig);
        
        data.put("directPayInstitutionConfig", institutionConfig);
        
        return data;
    }

    /**
     * 创建计费规则数据
     */
    private Map<String, Object> createBillingRuleData() {
        Map<String, Object> data = new HashMap<>();
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        
        data.put("ruleName", "计费规则_" + uniqueId);
        data.put("ruleDescription", "测试计费规则描述_" + uniqueId);
        data.put("ruleType", RuleTypeEnum.BILLING_RULE);
        data.put("effectiveTime", new Date());
        
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        data.put("expiryTime", calendar.getTime());
        
        // 设置计费规则配置
        Map<String, Object> billingConfig = new HashMap<>();
        billingConfig.put("matchRule", convertToMap(realMatchRule));
        billingConfig.put("executeAction", convertToMap(realExecuteAction));
        data.put("billingConfig", billingConfig);
        
        return data;
    }

    /**
     * 创建结算规则数据
     */
    private Map<String, Object> createSettlementRuleData() {
        Map<String, Object> data = new HashMap<>();
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        
        data.put("ruleName", "结算规则_" + uniqueId);
        data.put("ruleDescription", "测试结算规则描述_" + uniqueId);
        data.put("ruleType", RuleTypeEnum.SETTLEMENT_RULE);
        data.put("effectiveTime", new Date());
        
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        data.put("expiryTime", calendar.getTime());
        
        // 设置结算规则配置
        Map<String, Object> settlementConfig = new HashMap<>();
        settlementConfig.put("settlementCycle", 1);
        settlementConfig.put("settlementCycleType", SettlementCycleEnum.MONTHLY);
        settlementConfig.put("baseServiceFeeRate", new BigDecimal("2.50"));
        settlementConfig.put("transactionProcessingFee", new BigDecimal("1.00"));
        settlementConfig.put("platformTechnicalServiceFee", new BigDecimal("100.00"));
        settlementConfig.put("taxRate", new BigDecimal("6.00"));
        
        // 设置阶梯费率配置
        List<Map<String, Object>> tieredRateConfigs = new ArrayList<>();
        Map<String, Object> tieredRate = new HashMap<>();
        tieredRate.put("matchRule", convertToMap(realMatchRule));
        tieredRate.put("executeAction", convertToMap(realExecuteAction));
        tieredRate.put("ruleDescription", "测试阶梯费率描述_" + uniqueId);
        tieredRateConfigs.add(tieredRate);
        
        settlementConfig.put("tieredRateConfigs", tieredRateConfigs);
        data.put("settlementConfig", settlementConfig);

        return data;
    }

    @Test
    @DisplayName("测试查询规则列表")
    public void testQueryRuleList() {
        // 先创建一个测试数据
        testSaveDirectPayCategoryRule();

        // 构建查询请求
        QueryRequestDTO queryRequest = new QueryRequestDTO();
        queryRequest.setEntityName(ENTITY_NAME);

        // 设置过滤条件
        List<FieldData> filters = new ArrayList<>();
        FieldData filter = new FieldData();
        filter.setFieldName("ruleType");
        filter.setOperator("equal");
        filter.setFieldValue(RuleTypeEnum.DIRECT_PAY_CATEGORY.toString());
        filters.add(filter);
        queryRequest.setFilters(filters);

        // 设置分页信息
        queryRequest.setPagination(QueryUtils.page(10, 1));

        // 设置排序
        Map<String, String> sorter = new HashMap<>();
        sorter.put("ocId", "descend");
        queryRequest.setSorter(sorter);

        // 发送请求并验证结果
        given()
            .spec(requestSpec)
            .body(queryRequest)
            .when()
            .post(API_PATH + QUERY_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
            .body(FIELD_DATA_DATA, notNullValue())
            .body(FIELD_DATA_PAGINATION, notNullValue())
            .body(FIELD_PAGINATION_CURRENT, equalTo(1))
            .body(FIELD_PAGINATION_PAGE_SIZE, equalTo(10));
    }

    @Test
    @DisplayName("测试获取规则详情")
    public void testGetRule() {
        // 先创建一个测试数据
        testSaveDirectPayCategoryRule();

        // 发送请求并验证结果
        given()
                .spec(requestSpec)
                .body(new HashMap<>())
                .when()
                .post(API_PATH + GET_ENDPOINT + "/" + ENTITY_NAME + "/" + ocId)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
                .body("data.data", notNullValue())
                .body("data.data.ocId", equalTo(ocId))
                .body("data.data.ruleName", equalTo(testData.get("ruleName")))
                .body("data.data.ruleType", equalTo(testData.get("ruleType").toString()));
    }

    @Test
    @DisplayName("测试删除规则")
    public void testDeleteRule() {
        // 先创建一个测试数据
        testSaveDirectPayCategoryRule();

        // 构建删除请求
        Map<String, Object> deleteRequest = new HashMap<>();
        deleteRequest.put("entityName", ENTITY_NAME);
        List<String> ids = new ArrayList<>();
        ids.add(ocId);
        deleteRequest.put("ids", ids);

        // 发送请求并验证结果
        given()
                .spec(requestSpec)
                .body(deleteRequest)
                .when()
                .post(API_PATH + "/delete")
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS));

        // 验证删除是否成功
        given()
                .spec(requestSpec)
                .body(new HashMap<>())
                .when()
                .post(API_PATH + GET_ENDPOINT + "/" + ENTITY_NAME + "/" + ocId)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR));
    }

    // ========== 验证注解测试 ==========

    @Test
    @DisplayName("测试保存规则-必填字段校验-规则名称为空")
    public void testSaveRuleValidation_RuleNameEmpty() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        invalidData.put("ruleName", ""); // 规则名称为空

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("规则名称不能为空"));
    }

    @Test
    @DisplayName("测试保存规则-必填字段校验-规则类型为空")
    public void testSaveRuleValidation_RuleTypeNull() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        invalidData.remove("ruleType"); // 移除规则类型

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("规则类型不能为空"));
    }

    @Test
    @DisplayName("测试保存规则-必填字段校验-生效时间为空")
    public void testSaveRuleValidation_EffectiveTimeNull() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        invalidData.remove("effectiveTime"); // 移除生效时间

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("生效开始时间不能为空"));
    }

    @Test
    @DisplayName("测试保存规则-必填字段校验-失效时间为空")
    public void testSaveRuleValidation_ExpiryTimeNull() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        invalidData.remove("expiryTime"); // 移除失效时间

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("结束时间不能为空"));
    }

    @Test
    @DisplayName("测试保存规则-字段长度校验-规则名称超长")
    public void testSaveRuleValidation_RuleNameTooLong() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        // 创建超过64个字符的规则名称
        String longName = "这是一个非常长的规则名称".repeat(10);
        invalidData.put("ruleName", longName);

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("规则名称长度不能超过64个字符"));
    }

    @Test
    @DisplayName("测试保存规则-字段长度校验-规则描述超长")
    public void testSaveRuleValidation_RuleDescriptionTooLong() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        // 创建超过1024个字符的规则描述
        String longDescription = "这是一个非常长的规则描述".repeat(100);
        invalidData.put("ruleDescription", longDescription);

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("规则描述长度不能超过1024个字符"));
    }

    @Test
    @DisplayName("测试保存直付品类规则-匹配规则为空")
    public void testSaveDirectPayCategoryRule_MatchRuleNull() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        Map<String, Object> categoryConfig = (Map<String, Object>) invalidData.get("directPayCategoryConfig");
        categoryConfig.remove("matchRule"); // 移除匹配规则

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("匹配规则不能为空"));
    }

    @Test
    @DisplayName("测试保存直付品类规则-执行动作为空")
    public void testSaveDirectPayCategoryRule_ExecuteActionNull() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        Map<String, Object> categoryConfig = (Map<String, Object>) invalidData.get("directPayCategoryConfig");
        categoryConfig.remove("executeAction"); // 移除执行动作

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("执行动作不能为空"));
    }

    @Test
    @DisplayName("测试保存直付机构规则-策略为空")
    public void testSaveDirectPayInstitutionRule_StrategyNull() {
        Map<String, Object> invalidData = createDirectPayInstitutionRuleData();
        Map<String, Object> institutionConfig = (Map<String, Object>) invalidData.get("directPayInstitutionConfig");
        institutionConfig.remove("strategy"); // 移除策略

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("规则策略不能为空"));
    }

    @Test
    @DisplayName("测试保存结算规则-结算周期为空")
    public void testSaveSettlementRule_SettlementCycleNull() {
        Map<String, Object> invalidData = createSettlementRuleData();
        Map<String, Object> settlementConfig = (Map<String, Object>) invalidData.get("settlementConfig");
        settlementConfig.remove("settlementCycle"); // 移除结算周期

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("结算周期不能为空"));
    }

    @Test
    @DisplayName("测试保存结算规则-结算周期类型为空")
    public void testSaveSettlementRule_SettlementCycleTypeNull() {
        Map<String, Object> invalidData = createSettlementRuleData();
        Map<String, Object> settlementConfig = (Map<String, Object>) invalidData.get("settlementConfig");
        settlementConfig.remove("settlementCycleType"); // 移除结算周期类型

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("结算周期类型不能为空"));
    }

    @Test
    @DisplayName("测试保存结算规则-基础服务费率为空")
    public void testSaveSettlementRule_BaseServiceFeeRateNull() {
        Map<String, Object> invalidData = createSettlementRuleData();
        Map<String, Object> settlementConfig = (Map<String, Object>) invalidData.get("settlementConfig");
        settlementConfig.remove("baseServiceFeeRate"); // 移除基础服务费率

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("基础服务费率不能为空"));
    }

    @Test
    @DisplayName("测试保存结算规则-阶梯费率配置列表为空")
    public void testSaveSettlementRule_TieredRateConfigsEmpty() {
        Map<String, Object> invalidData = createSettlementRuleData();
        Map<String, Object> settlementConfig = (Map<String, Object>) invalidData.get("settlementConfig");
        settlementConfig.put("tieredRateConfigs", new ArrayList<>()); // 设置为空列表

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("阶梯费率配置列表不能为空"));
    }

    @Test
    @DisplayName("测试保存规则-数值范围校验-基础服务费率超出范围")
    public void testSaveSettlementRule_BaseServiceFeeRateOutOfRange() {
        Map<String, Object> invalidData = createSettlementRuleData();
        Map<String, Object> settlementConfig = (Map<String, Object>) invalidData.get("settlementConfig");
        settlementConfig.put("baseServiceFeeRate", new BigDecimal("150.00")); // 超过100%

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("基础服务费率不能大于100%"));
    }

    @Test
    @DisplayName("测试保存规则-数值范围校验-基础服务费率为负数")
    public void testSaveSettlementRule_BaseServiceFeeRateNegative() {
        Map<String, Object> invalidData = createSettlementRuleData();
        Map<String, Object> settlementConfig = (Map<String, Object>) invalidData.get("settlementConfig");
        settlementConfig.put("baseServiceFeeRate", new BigDecimal("-1.00")); // 负数

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("基础服务费率不能小于0%"));
    }

    @Test
    @DisplayName("测试保存规则-数值范围校验-结算周期为非正整数")
    public void testSaveSettlementRule_SettlementCycleNotPositive() {
        Map<String, Object> invalidData = createSettlementRuleData();
        Map<String, Object> settlementConfig = (Map<String, Object>) invalidData.get("settlementConfig");
        settlementConfig.put("settlementCycle", 0); // 非正整数

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("结算周期必须为正整数"));
    }

    @Test
    @DisplayName("测试保存规则-唯一性校验-规则名称重复")
    public void testSaveRuleValidation_DuplicateRuleName() {
        // 第一次保存
        testSaveDirectPayCategoryRule();
        String firstRuleName = (String) testData.get("ruleName");

        // 第二次保存，使用相同规则名称
        Map<String, Object> duplicateData = createTestData();
        duplicateData.put("ruleName", firstRuleName);

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(duplicateData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("规则名称"));
    }

    @Test
    @DisplayName("测试保存规则-时间范围校验-失效时间早于生效时间")
    public void testSaveRuleValidation_ExpiryTimeBeforeEffectiveTime() {
        Map<String, Object> invalidData = new HashMap<>(testData);

        // 设置失效时间早于生效时间
        Date effectiveTime = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(effectiveTime);
        calendar.add(Calendar.DAY_OF_MONTH, -1); // 失效时间比生效时间早1天
        Date expiryTime = calendar.getTime();

        invalidData.put("effectiveTime", effectiveTime);
        invalidData.put("expiryTime", expiryTime);

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("时间"));
    }

    @Test
    @DisplayName("测试保存规则-QueryBuilder字段校验-treeJsonValue为空")
    public void testSaveRuleValidation_TreeJsonValueEmpty() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        Map<String, Object> categoryConfig = (Map<String, Object>) invalidData.get("directPayCategoryConfig");
        Map<String, Object> matchRule = (Map<String, Object>) categoryConfig.get("matchRule");
        matchRule.put("treeJsonValue", ""); // 设置为空字符串

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("规则前端展示信息不能为空"));
    }

    @Test
    @DisplayName("测试保存规则-QueryBuilder字段校验-jsonLogicValue为空")
    public void testSaveRuleValidation_JsonLogicValueEmpty() {
        Map<String, Object> invalidData = new HashMap<>(testData);
        Map<String, Object> categoryConfig = (Map<String, Object>) invalidData.get("directPayCategoryConfig");
        Map<String, Object> matchRule = (Map<String, Object>) categoryConfig.get("matchRule");
        matchRule.put("jsonLogicValue", ""); // 设置为空字符串

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(invalidData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_ERROR))
                .body(FIELD_MSG, containsString("规则JsonLogic表达式不能为空"));
    }

    @Test
    @DisplayName("测试字段可清空-规则描述可清空")
    public void testClearRuleDescription() {
        // 第一次保存，规则描述有值
        testSaveDirectPayCategoryRule();

        // 验证已保存的规则描述
        Response getResponse = given()
                .spec(requestSpec)
                .body(new HashMap<>())
                .when()
                .post(API_PATH + GET_ENDPOINT + "/" + ENTITY_NAME + "/" + ocId)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
                .extract()
                .response();

        String originalDescription = getResponse.jsonPath().getString("data.data.ruleDescription");
        assertNotNull(originalDescription, "原始规则描述应该有值");

        // 第二次保存，规则描述清空
        Map<String, Object> updateData = new HashMap<>(testData);
        updateData.put("ocId", ocId);
        updateData.put("ruleDescription", ""); // 清空规则描述

        SaveRequestDTO saveRequest = new SaveRequestDTO();
        saveRequest.setEntityName(ENTITY_NAME);
        saveRequest.setData(updateData);

        given()
                .spec(requestSpec)
                .body(saveRequest)
                .when()
                .post(API_PATH + SAVE_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS));

        // 验证已清空
        Response updatedResponse = given()
                .spec(requestSpec)
                .body(new HashMap<>())
                .when()
                .post(API_PATH + GET_ENDPOINT + "/" + ENTITY_NAME + "/" + ocId)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
                .extract()
                .response();

        String updatedDescription = updatedResponse.jsonPath().getString("data.data.ruleDescription");
        assertTrue(updatedDescription == null || "".equals(updatedDescription), "规则描述应该已被清空");
    }

    @Test
    @DisplayName("测试批量保存规则")
    public void testBatchSaveRules() {
        // 创建批量测试数据
        List<Map<String, Object>> batchData = createBatchTestData();

        // 构建批量保存请求
        List<SaveRequestDTO> saveRequests = new ArrayList<>();
        for (Map<String, Object> data : batchData) {
            SaveRequestDTO saveRequest = new SaveRequestDTO();
            saveRequest.setEntityName(ENTITY_NAME);
            saveRequest.setData(data);
            saveRequests.add(saveRequest);
        }

        // 发送请求并验证结果
        given()
                .spec(requestSpec)
                .body(saveRequests)
                .when()
                .post(API_PATH + "/batch/save")
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
                .body(FIELD_DATA, notNullValue());
    }

    /**
     * 创建批量测试数据
     */
    private List<Map<String, Object>> createBatchTestData() {
        List<Map<String, Object>> batchData = new ArrayList<>();

        // 创建不同类型的规则数据
        batchData.add(createTestData()); // 直付品类规则
        batchData.add(createDirectPayInstitutionRuleData()); // 直付机构规则
        batchData.add(createBillingRuleData()); // 计费规则

        return batchData;
    }

    @Test
    @DisplayName("测试分页功能")
    public void testPagination() {
        // 创建多条测试数据
        for (int i = 1; i <= 5; i++) {
            Map<String, Object> data = createTestData();
            data.put("ruleName", "分页测试规则_" + i + "_" + UUID.randomUUID().toString().substring(0, 8));

            SaveRequestDTO saveRequest = new SaveRequestDTO();
            saveRequest.setEntityName(ENTITY_NAME);
            saveRequest.setData(data);

            given()
                    .spec(requestSpec)
                    .body(saveRequest)
                    .when()
                    .post(API_PATH + SAVE_ENDPOINT)
                    .then()
                    .statusCode(HttpStatus.OK.value())
                    .body(FIELD_STATUS, equalTo(STATUS_SUCCESS));
        }

        // 第一页查询 - 验证第一页数据
        QueryRequestDTO firstPageRequest = new QueryRequestDTO();
        firstPageRequest.setEntityName(ENTITY_NAME);
        firstPageRequest.setPagination(QueryUtils.page(3, 1));

        Response firstPageResponse = given()
                .spec(requestSpec)
                .body(firstPageRequest)
                .when()
                .post(API_PATH + QUERY_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body(FIELD_PAGINATION_CURRENT, equalTo(1))
                .body(FIELD_PAGINATION_PAGE_SIZE, equalTo(3))
                .extract()
                .response();

        List<Map> firstPageList = firstPageResponse.jsonPath().getList(FIELD_DATA_DATA, Map.class);
        assertTrue(firstPageList.size() <= 3, "第一页应返回不超过3条记录");

        int total = firstPageResponse.jsonPath().getInt("data.pagination.total");
        assertTrue(total >= 5, "总记录数应至少为5");

        // 第二页查询 - 验证第二页数据
        QueryRequestDTO secondPageRequest = new QueryRequestDTO();
        secondPageRequest.setEntityName(ENTITY_NAME);
        secondPageRequest.setPagination(QueryUtils.page(3, 2));

        Response secondPageResponse = given()
                .spec(requestSpec)
                .body(secondPageRequest)
                .when()
                .post(API_PATH + QUERY_ENDPOINT)
                .then()
                .statusCode(HttpStatus.OK.value())
                .body("data.pagination.current", equalTo(2))
                .extract()
                .response();

        List<Map> secondPageList = secondPageResponse.jsonPath().getList(FIELD_DATA_DATA, Map.class);
        assertTrue(secondPageList.size() >= 0, "第二页应返回0条或更多记录");

        // 验证分页数据的完整性 - 确保第一页和第二页的数据没有重复
        if (!firstPageList.isEmpty() && !secondPageList.isEmpty()) {
            List<String> firstPageIds = firstPageList.stream()
                    .map(item -> (String) item.get("ocId"))
                    .filter(Objects::nonNull)
                    .toList();

            List<String> secondPageIds = secondPageList.stream()
                    .map(item -> (String) item.get("ocId"))
                    .filter(Objects::nonNull)
                    .toList();

            // 验证两页数据没有交集
            Set<String> intersection = new HashSet<>(firstPageIds);
            intersection.retainAll(secondPageIds);
            assertTrue(intersection.isEmpty(), "第一页和第二页的数据不应有重复");
        }
    }
