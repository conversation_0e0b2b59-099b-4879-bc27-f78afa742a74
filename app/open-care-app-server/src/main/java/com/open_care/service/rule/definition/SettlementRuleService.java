package com.open_care.service.rule.definition;

import com.open_care.api.common.dto.OcResponse;
import com.open_care.crud_service.huagui.SettlementRuleCrudService;
import com.open_care.dto.huagui.rule.RuleDefinitionDTO;
import com.open_care.validation.group.RuleValidationGroups;
import com.open_care.enums.huagui.RuleTypeEnum;
import com.open_care.huagui.rule.OCSettlementRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 结算规则业务服务
 * 专注于结算规则的特定业务逻辑
 */
@Service
@Slf4j
public class SettlementRuleService extends AbstractRuleService<OCSettlementRule> {

    /**
     * 结算规则保存字段路径常量
     * 包含所有需要保存的实体属性，展开所有嵌套对象字段
     */
    public static final String[] SETTLEMENT_RULE_SAVE_FIELD_PATHS = {
            // 基础字段（继承自OCBase）
            "ocId",
            "active",
            "created",
            "createdBy",
            "createdByName",
            "updated",
            "updatedBy",
            "updatedByName",
            "attributes",
            "dynamicFieldData",
            "version",
            "entityName",
            // 规则基本信息（继承自OCRuleDefinitionBase）
            "ruleName",
            "ruleDescription",
            "ruleType",
            "effectiveTime",
            "expiryTime",
            // 结算规则特有字段
            "settlementCycle",
            "settlementCycleType",
            "baseServiceFeeRate",
            "transactionProcessingFee",
            "platformTechnicalServiceFee",
            "taxRate",
            // 阶梯费率配置对象及其子字段（修正字段名）
            "tieredRateConfigs",
            "tieredRateConfigs.ruleDescription",
            "tieredRateConfigs.matchRule",
            "tieredRateConfigs.matchRule.treeJsonValue",
            "tieredRateConfigs.matchRule.jsonLogicValue",
            "tieredRateConfigs.executeAction",
            "tieredRateConfigs.executeAction.treeJsonValue",
            "tieredRateConfigs.executeAction.jsonLogicValue",
            "tieredRateConfigs.rate"
    };

    @Autowired
    private SettlementRuleCrudService settlementRuleCrudService;

    @Override
    public RuleTypeEnum getSupportedRuleType() {
        return RuleTypeEnum.SETTLEMENT_RULE;
    }

    @Override
    protected Class<?>[] getValidationGroups(RuleDefinitionDTO dto) {
        // 使用继承分组，SettlementRuleGroup自动包含SaveRuleGroup
        return new Class<?>[]{
                RuleValidationGroups.SettlementRuleGroup.class
        };
    }

    @Override
    protected Class<OCSettlementRule> getEntityClass() {
        return OCSettlementRule.class;
    }

    @Override
    protected OCSettlementRule saveEntity(OCSettlementRule entity) {
        return settlementRuleCrudService.save(entity);
    }

    @Override
    protected OCSettlementRule saveEntity(OCSettlementRule entity, String[] saveFieldPaths) {
        return settlementRuleCrudService.save(entity, saveFieldPaths);
    }

    @Override
    protected String getBusinessName() {
        return "结算规则";
    }

    @Override
    public String[] getSaveFieldPaths() {
        return SETTLEMENT_RULE_SAVE_FIELD_PATHS;
    }
} 