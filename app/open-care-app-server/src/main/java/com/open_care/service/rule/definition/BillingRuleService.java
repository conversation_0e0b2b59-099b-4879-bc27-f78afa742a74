package com.open_care.service.rule.definition;

import com.open_care.crud_service.huagui.BillingRuleCrudService;
import com.open_care.dto.huagui.rule.RuleDefinitionDTO;
import com.open_care.validation.group.RuleValidationGroups;
import com.open_care.enums.huagui.RuleTypeEnum;
import com.open_care.huagui.rule.OCBillingRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 计费规则业务服务
 * 专注于计费规则的特定业务逻辑
 */
@Service
@Slf4j
public class BillingRuleService extends AbstractRuleService<OCBillingRule> {
    
    /**
     * 计费规则保存字段路径常量
     * 包含所有需要保存的实体属性，展开所有嵌套对象字段
     */
    public static final String[] BILLING_RULE_SAVE_FIELD_PATHS = {
        // 基础字段（继承自OCBase）
        "ocId",
        "active", 
        "created",
        "createdBy",
        "createdByName",
        "updated", 
        "updatedBy",
        "updatedByName",
        "attributes",
        "dynamicFieldData",
        "version",
        "entityName",
        // 规则基本信息（继承自OCRuleDefinitionBase）
        "ruleName",
        "ruleDescription", 
        "ruleType",
        "effectiveTime",
        "expiryTime",
        // 计费规则特有字段（继承自OCRuleWithMatchActionBase）
        "matchRule",
        "matchRule.jsonLogicValue",
        "matchRule.treeJsonValue",
        "matchRule.description",
        "matchRule.contextClassName",
        "executeAction",
        "executeAction.jsonLogicValue",
        "executeAction.treeJsonValue",
        "executeAction.description",
        "executeAction.contextClassName"
    };
    
    @Autowired
    private BillingRuleCrudService billingRuleCrudService;
    
    @Override
    public RuleTypeEnum getSupportedRuleType() {
        return RuleTypeEnum.BILLING_RULE;
    }
    
    @Override
    protected Class<?>[] getValidationGroups(RuleDefinitionDTO dto) {
        // 使用继承分组，BillingRuleGroup自动包含SaveRuleGroup
        return new Class<?>[] {
            RuleValidationGroups.BillingRuleGroup.class
        };
    }

    @Override
    protected Class<OCBillingRule> getEntityClass() {
        return OCBillingRule.class;
    }
    
    @Override
    protected OCBillingRule saveEntity(OCBillingRule entity) {
        return billingRuleCrudService.save(entity);
    }
    
    @Override
    protected OCBillingRule saveEntity(OCBillingRule entity, String[] saveFieldPaths) {
        return billingRuleCrudService.save(entity, saveFieldPaths);
    }
    
    @Override
    protected String getBusinessName() {
        return "计费规则";
    }
    
    @Override
    public String[] getSaveFieldPaths() {
        return BILLING_RULE_SAVE_FIELD_PATHS;
    }
} 