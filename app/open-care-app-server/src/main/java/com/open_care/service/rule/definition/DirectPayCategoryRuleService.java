package com.open_care.service.rule.definition;

import com.open_care.crud_service.huagui.DirectPayCategoryRuleCrudService;
import com.open_care.dto.huagui.rule.RuleDefinitionDTO;
import com.open_care.validation.group.RuleValidationGroups;
import com.open_care.enums.huagui.RuleTypeEnum;
import com.open_care.huagui.rule.OCDirectPayCategoryRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 直付品类规则业务服务
 * 专注于直付品类规则的特定业务逻辑
 */
@Service
@Slf4j
public class DirectPayCategoryRuleService extends AbstractRuleService<OCDirectPayCategoryRule> {
    
    /**
     * 直付品类规则保存字段路径常量
     * 包含所有需要保存的实体属性，展开所有嵌套对象字段
     */
    public static final String[] DIRECT_PAY_CATEGORY_SAVE_FIELD_PATHS = {
        // 基础字段（继承自OCBase）
        "ocId",
        "active", 
        "created",
        "createdBy",
        "createdByName",
        "updated", 
        "updatedBy",
        "updatedByName",
        "attributes",
        "dynamicFieldData",
        "version",
        "entityName",
        // 规则基本信息（继承自OCRuleDefinitionBase）
        "ruleName",
        "ruleDescription", 
        "ruleType",
        "effectiveTime",
        "expiryTime",
        // 直付品类特有字段（继承自OCRuleWithMatchActionBase）
        "matchRule",
        "matchRule.jsonLogicValue",
        "matchRule.treeJsonValue",
        "matchRule.description",
        "matchRule.contextClassName",
        "executeAction",
        "executeAction.jsonLogicValue", 
        "executeAction.treeJsonValue",
        "executeAction.description",
        "executeAction.contextClassName"
    };
    
    @Autowired
    private DirectPayCategoryRuleCrudService directPayCategoryRuleCrudService;
    
    @Override
    public RuleTypeEnum getSupportedRuleType() {
        return RuleTypeEnum.DIRECT_PAY_CATEGORY;
    }
    
    @Override
    protected Class<?>[] getValidationGroups(RuleDefinitionDTO dto) {
        // 使用继承分组，DirectPayCategoryGroup自动包含SaveRuleGroup
        return new Class<?>[] {
            RuleValidationGroups.DirectPayCategoryGroup.class
        };
    }
    
    @Override
    protected Class<OCDirectPayCategoryRule> getEntityClass() {
        return OCDirectPayCategoryRule.class;
    }
    
    @Override
    protected OCDirectPayCategoryRule saveEntity(OCDirectPayCategoryRule entity) {
        return directPayCategoryRuleCrudService.save(entity);
    }
    
    @Override
    protected OCDirectPayCategoryRule saveEntity(OCDirectPayCategoryRule entity, String[] saveFieldPaths) {
        return directPayCategoryRuleCrudService.save(entity, saveFieldPaths);
    }
    
    @Override
    protected String getBusinessName() {
        return "直付品类规则";
    }
    
    @Override
    public String[] getSaveFieldPaths() {
        return DIRECT_PAY_CATEGORY_SAVE_FIELD_PATHS;
    }
} 