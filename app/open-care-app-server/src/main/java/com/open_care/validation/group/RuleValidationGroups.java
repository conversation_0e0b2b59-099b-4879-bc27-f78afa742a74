package com.open_care.validation.group;

/**
 * 规则验证分组定义
 * 统一管理所有规则相关的验证分组，支持继承关系
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
public class RuleValidationGroups {

    /**
     * 保存规则时的基础验证分组
     */
    public interface SaveRuleGroup {
    }

    /**
     * 直付品类验证分组
     * 继承SaveRuleGroup，自动包含基础保存验证
     */
    public interface DirectPayCategoryGroup extends SaveRuleGroup {
    }

    /**
     * 直付机构基础验证分组
     * 继承SaveRuleGroup，自动包含基础保存验证
     */
    public interface DirectPayInstitutionGroup extends SaveRuleGroup {
    }

    /**
     * 直付机构固定优先级策略验证分组
     * 继承DirectPayInstitutionGroup，自动包含基础直付机构验证和保存验证
     */
    public interface DirectPayInstitutionFixedPriorityGroup extends DirectPayInstitutionGroup {
    }

    /**
     * 直付机构动态分配策略验证分组
     * 继承DirectPayInstitutionGroup，自动包含基础直付机构验证和保存验证
     */
    public interface DirectPayInstitutionDynamicVolumeGroup extends DirectPayInstitutionGroup {
    }

    /**
     * 计费规则验证分组
     * 继承SaveRuleGroup，自动包含基础保存验证
     */
    public interface BillingRuleGroup extends SaveRuleGroup {
    }

    /**
     * 结算规则验证分组
     * 继承SaveRuleGroup，自动包含基础保存验证
     */
    public interface SettlementRuleGroup extends SaveRuleGroup {
    }
}
