package com.open_care.dto.huagui.rule;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.gson.annotations.JsonAdapter;
import com.open_care.adapter.OCDateTypeAdapter;
import com.open_care.annotation.OcColumn;
import com.open_care.dto.BaseEntityDTO;
import com.open_care.enums.huagui.*;
import com.open_care.validation.rule.definition.UniqueRuleName;
import com.open_care.validation.rule.definition.ValidTimeRange;
import com.open_care.validation.group.RuleValidationGroups;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.Date;


/**
 * 统一规则定义DTO
 * 用于所有规则类型的数据传输
 */
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@SuperBuilder
@UniqueRuleName(groups = {RuleValidationGroups.SaveRuleGroup.class})
@ValidTimeRange(groups = {RuleValidationGroups.SaveRuleGroup.class})
public class RuleDefinitionDTO extends BaseEntityDTO {

    // ========== 通用规则属性 ==========
    @NotBlank(message = "规则名称不能为空", groups = {RuleValidationGroups.SaveRuleGroup.class})
    @Size(max = 64, message = "规则名称长度不能超过64个字符", groups = {RuleValidationGroups.SaveRuleGroup.class})
    @OcColumn(title = "规则名称")
    private String ruleName;

    @Size(max = 1024, message = "规则描述长度不能超过1024个字符", groups = {RuleValidationGroups.SaveRuleGroup.class})
    @OcColumn(title = "规则描述")
    private String ruleDescription;

    @NotNull(message = "规则类型不能为空", groups = {RuleValidationGroups.SaveRuleGroup.class})
    @OcColumn(title = "规则类型")
    private RuleTypeEnum ruleType;

    @OcColumn(title = "生效时间")
    @JsonAdapter(OCDateTypeAdapter.class)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "生效开始时间不能为空", groups = {RuleValidationGroups.SaveRuleGroup.class})
    private Date effectiveTime;

    @OcColumn(title = "失效时间")
    @JsonAdapter(OCDateTypeAdapter.class)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "结束时间不能为空", groups = {RuleValidationGroups.SaveRuleGroup.class})
    private Date expiryTime;


    // ========== 特定规则类型属性对象 ==========

    @Valid
    @OcColumn(title = "直付品类规则属性")
    private DirectPayCategoryRuleConfigDTO directPayCategoryConfig;

    @Valid
    @OcColumn(title = "直付机构规则属性")
    private DirectPayInstitutionRuleConfigDTO directPayInstitutionConfig;

    @Valid
    @OcColumn(title = "计费规则属性")
    private BillingRuleConfigDTO billingConfig;

    @Valid
    @OcColumn(title = "结算规则属性")
    private SettlementRuleConfigDTO settlementConfig;


} 