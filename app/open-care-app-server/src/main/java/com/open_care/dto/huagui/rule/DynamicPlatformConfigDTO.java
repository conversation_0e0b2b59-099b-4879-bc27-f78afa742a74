package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import jakarta.validation.constraints.*;
import com.open_care.validation.group.RuleValidationGroups;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 动态平台配置DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class DynamicPlatformConfigDTO implements IBaseDTO {
    
    @NotBlank(message = "平台名称不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    @Size(max = 32, message = "平台名称长度不能超过32个字符", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    @OcColumn(title = "平台名称")
    private String platformName;
    
    @NotNull(message = "目标分配比例不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    @DecimalMin(value = "0.0", message = "目标分配比例不能小于0%", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    @DecimalMax(value = "100.0", message = "目标分配比例不能大于等于100%", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    @OcColumn(title = "目标分配比例（%）")
    private BigDecimal targetRatio;
    
    @NotNull(message = "最小保障比例不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    @DecimalMin(value = "0.0", message = "最小保障比例不能小于0%", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    @DecimalMax(value = "100.0", message = "最小保障比例不能大于等于100%", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    @OcColumn(title = "最小保障比例（%）")
    private BigDecimal minimumRatio;
} 