package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import com.open_care.validation.group.RuleValidationGroups;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 固定优先级配置DTO
 * 列表不能为空，列表中的对象属性都必须有值并且必填
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class FixedPriorityConfigDTO implements IBaseDTO {
    
    @OcColumn(title = "支付平台列表")
    @Valid
    @NotEmpty(message = "支付平台列表不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionFixedPriorityGroup.class
    })
    private List<PaymentPlatformPriorityDTO> platformPriorities;
} 