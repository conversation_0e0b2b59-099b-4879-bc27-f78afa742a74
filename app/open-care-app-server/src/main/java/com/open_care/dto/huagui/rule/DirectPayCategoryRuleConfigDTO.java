package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import com.open_care.dto.rule.query_builder.QueryBuilderRuleExpressionDTO;
import com.open_care.validation.group.query_builder.QueryBuilderValidationGroup;
import com.open_care.validation.group.RuleValidationGroups;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 直付品类规则配置DTO
 * 除了规则内容其他均不能为空
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class DirectPayCategoryRuleConfigDTO implements IBaseDTO {

    @Valid
    @NotNull(message = "匹配规则不能为空", groups = {
        RuleValidationGroups.DirectPayCategoryGroup.class,
        QueryBuilderValidationGroup.SaveRuleGroup.class
    })
    @OcColumn(title = "匹配规则")
    private QueryBuilderRuleExpressionDTO matchRule;

    @Valid
    @NotNull(message = "执行动作不能为空", groups = {
        RuleValidationGroups.DirectPayCategoryGroup.class,
        QueryBuilderValidationGroup.SaveRuleGroup.class
    })
    @OcColumn(title = "执行动作")
    private QueryBuilderRuleExpressionDTO executeAction;
} 