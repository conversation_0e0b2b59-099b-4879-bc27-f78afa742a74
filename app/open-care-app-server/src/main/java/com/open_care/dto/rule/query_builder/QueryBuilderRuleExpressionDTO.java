package com.open_care.dto.rule.query_builder;

import com.open_care.dto.IBaseDTO;
import com.open_care.dto.huagui.rule.RuleDefinitionDTO;
import com.open_care.validation.group.query_builder.QueryBuilderValidationGroup;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询构建器规则表达式DTO
 * 用于规则配置中的规则表达式传输
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryBuilderRuleExpressionDTO implements IBaseDTO {
    
    /**
     * 规则信息前端展示JSON
     */
    @NotBlank(message = "规则前端展示信息不能为空", groups = {QueryBuilderValidationGroup.SaveRuleGroup.class})
    private String treeJsonValue;
    
    /**
     * 规则信息JsonLogic表达式
     */
    @NotBlank(message = "规则JsonLogic表达式不能为空", groups = {QueryBuilderValidationGroup.SaveRuleGroup.class})
    private String jsonLogicValue;
} 