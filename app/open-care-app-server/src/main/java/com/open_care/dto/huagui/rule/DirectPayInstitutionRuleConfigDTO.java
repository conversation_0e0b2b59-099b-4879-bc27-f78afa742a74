package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import com.open_care.enums.huagui.PriorityStrategyEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import com.open_care.validation.group.RuleValidationGroups;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 直付机构规则配置DTO
 * 规则内容选填，如果规则策略为固定优先级，则固定分配策略列表不能为空
 * 如果为动态单量，则动态单量相关字段也必填
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class DirectPayInstitutionRuleConfigDTO implements IBaseDTO {

    @NotNull(message = "规则策略不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionGroup.class
    })
    @OcColumn(title = "规则策略")
    private PriorityStrategyEnum strategy;

    @Valid
    @OcColumn(title = "固定优先级配置")
    private FixedPriorityConfigDTO fixedPriorityConfig;

    @Valid
    @OcColumn(title = "动态单量配置")
    private DynamicVolumeConfigDTO dynamicVolumeConfig;
} 