package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import com.open_care.dto.rule.query_builder.QueryBuilderRuleExpressionDTO;
import com.open_care.validation.group.query_builder.QueryBuilderValidationGroup;
import com.open_care.validation.group.RuleValidationGroups;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 阶梯费率配置DTO
 * 规则描述必须有值
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class TieredRateConfigDTO implements IBaseDTO {
    
    @Valid
    @NotNull(message = "规则表达式不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class,
        QueryBuilderValidationGroup.SaveRuleGroup.class
    })
    @OcColumn(title = "规则表达式")
    private QueryBuilderRuleExpressionDTO matchRule;


    @Valid
    @NotNull(message = "满足规则动作不能为空", groups = {
            RuleValidationGroups.SettlementRuleGroup.class,
            QueryBuilderValidationGroup.SaveRuleGroup.class
    })
    @OcColumn(title = "动作表达式")
    private QueryBuilderRuleExpressionDTO executeAction;
    
    @NotBlank(message = "规则描述不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "规则描述")
    private String ruleDescription;

}