package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import com.open_care.enums.huagui.SettlementCycleEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import com.open_care.validation.group.RuleValidationGroups;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 结算规则配置DTO
 * 规则内容选填，其他的必填，规则列表必须有值，并且规则描述必须有值
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class SettlementRuleConfigDTO implements IBaseDTO {
    
    @NotNull(message = "结算周期不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @Positive(message = "结算周期必须为正整数", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "结算周期")
    private Integer settlementCycle;
    
    @NotNull(message = "结算周期类型不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "结算周期类型")
    private SettlementCycleEnum settlementCycleType;

    @NotNull(message = "基础服务费率不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @DecimalMin(value = "0.0", message = "基础服务费率不能小于0%", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @DecimalMax(value = "100.0", message = "基础服务费率不能大于100%", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "基础服务费率")
    private BigDecimal baseServiceFeeRate;

    @NotNull(message = "交易处理费不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @DecimalMin(value = "0.0", message = "交易处理费不能小于0", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "交易处理费")
    private BigDecimal transactionProcessingFee;

    @NotNull(message = "平台技术服务费不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @DecimalMin(value = "0.0", message = "平台技术服务费不能小于0", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "平台技术服务费")
    private BigDecimal platformTechnicalServiceFee;

    @NotNull(message = "税率不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @DecimalMin(value = "0.0", message = "税率不能小于0", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @OcColumn(title = "税率")
    private BigDecimal taxRate;
    
    @NotEmpty(message = "阶梯费率配置列表不能为空", groups = {
        RuleValidationGroups.SettlementRuleGroup.class
    })
    @Valid
    @OcColumn(title = "阶梯费率配置")
    private List<TieredRateConfigDTO> tieredRateConfigs;
} 