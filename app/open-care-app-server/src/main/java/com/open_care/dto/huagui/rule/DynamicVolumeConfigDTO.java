package com.open_care.dto.huagui.rule;

import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import com.open_care.enums.huagui.AllocationStrategyEnum;
import com.open_care.enums.huagui.StatisticsDimensionEnum;
import com.open_care.enums.huagui.StatisticsPeriodEnum;

import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import com.open_care.validation.group.RuleValidationGroups;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 动态单量配置DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder

public class DynamicVolumeConfigDTO implements IBaseDTO {
    
    @NotNull(message = "分配策略不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    private AllocationStrategyEnum allocationStrategy;


    @NotNull(message = "时间周期不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    private StatisticsPeriodEnum statisticsPeriod;

    @Valid
    @NotEmpty(message = "平台配置列表不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionDynamicVolumeGroup.class
    })
    private List<DynamicPlatformConfigDTO> platformConfigs;
} 