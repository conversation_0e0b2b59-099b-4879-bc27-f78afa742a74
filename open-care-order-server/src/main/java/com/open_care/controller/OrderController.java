package com.open_care.controller;

import com.open_care.api.client.OrderRemoteService;
import com.open_care.api.common.dto.OcResponse;
import com.open_care.api.dto.*;
import com.open_care.payment.OCPay;
import com.open_care.repository.PayRepository;
//import com.open_care.service.OrderService;
import com.open_care.util.GsonUtils;
import com.open_care.util.ResponseUtil;
import com.open_care.util.json.JsonConverter;
import lombok.extern.flogger.Flogger;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * Created by kangpengjie on 2018/5/17.
 */
@RestController
public class OrderController implements OrderRemoteService {
//    @Autowired
//    OrderService orderService;

    @Autowired
    JsonConverter jsonConverter;

    @Autowired
    PayRepository payRepository;

    @Override
    public OcResponse refundOrder(RefundOrderDTO refundOrderDTO) {
//        return orderService.refundOrder(refundOrderDTO);
        return null;
    }

    @Override
    public OcResponse cancelOrder(CancelOrderDTO cancelOrderDTO) {
//        return orderService.cancelOrder(cancelOrderDTO);
        return null;
    }

    @Override
    @Transactional
    public OcResponse batchCancelOrder(BatchCancelOrderDTO batchCancelOrderDTO) {
//        batchCancelOrderDTO.getPartyRoles().stream().forEach(
//                iter -> {
//                    CancelOrderDTO cancelOrderDTO = new CancelOrderDTO();
//                    cancelOrderDTO.setProfitLevel(batchCancelOrderDTO.getProfitLevel());
//                    cancelOrderDTO.setServicePlan(batchCancelOrderDTO.getServicePlan());
//                    cancelOrderDTO.setPartyRole(iter);
//                    cancelOrderDTO.setCheckPay(batchCancelOrderDTO.getCheckPay());
//                    cancelOrderDTO.setDelete(batchCancelOrderDTO.getDelete());
//                    orderService.cancelOrder(cancelOrderDTO);
//                }
//        );
//
//        return ResponseUtil.getResponseSuccess("");
        return null;
    }

    @Override
    @Transactional
    public OcResponse refundOrderItems(RefundOrderDTO refundOrderDTO) {
//        return orderService.refundOrderItems(refundOrderDTO);
        return null;
    }

    @Override
    public OcResponse cancelServiceOrder(CancelServiceOrderDTO cancelServiceOrderDTO) {
//        return orderService.cancelServiceOrder(cancelServiceOrderDTO);
        return null;
    }

    @Override
    public OcResponse getOrderItems(String orderId) {
//        return ResponseUtil.getResponseSuccess(GsonUtils.toJson(orderService.getOrderItems(orderId)), "");
        return null;
    }

    @Override
    public OcResponse getOrderItem(String orderItemId) {
//        return ResponseUtil.getResponseSuccess(GsonUtils.toJson(orderService.getOrderItem(orderItemId)), "");
        return null;
    }

    @Override
    public OcResponse updateOrderAccountItem(OrderAccountItemDTO orderAccountItemDTO) {
//        return ResponseUtil.getResponseSuccess(orderService.updateOrderAccountItem(orderAccountItemDTO), "成功");
        return null;
    }

    @Override
    @Transactional
    public OcResponse exchangeOrderItems(ExchangeOrderDTO exchangeOrderDTO) {
//        return orderService.exchangeOrderItems(exchangeOrderDTO);
        return null;
    }

    @Override
    public OcResponse saveOrderPay(@RequestBody OrderAndPayDTO orderPayDTO) {
//        return orderService.saveOrderPay(orderPayDTO);
        return null;
    }

    @Override
    public OcResponse recoveryCustomerOderData(String customerId) {
//        return orderService.recoveryCustomerOderData(customerId);
        return null;
    }

    @Override
    public OcResponse splitPay(PaymentSplitDTO paymentSplitDTO) {
//        return orderService.splitPay(paymentSplitDTO);
        return null;
    }

    @Override
    public OcResponse makePayBalance(String payId) {
//        OCPay pay = payRepository.getById(payId);
//
//        Integer count = 0;
//
//        while (count < 5) {
//            if (BooleanUtils.isTrue(orderService.makeBalancePaymentOrderItems(pay))) {
//                break;
//            }
//
//            count += 1;
//        }
//
//        return ResponseUtil.getResponseSuccess();
        return null;
    }

}
