/**
 * Copyright 2018-2023 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 */

package com.open_care.application.resourceserver.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.gson.*;
import com.open_care.api.MUI.*;
import com.open_care.api.UI.*;
import com.open_care.application.resourceserver.bo.UserTableConfigContext;
import com.open_care.application.resourceserver.consts.ComponentTypeConsts;
import com.open_care.application.resourceserver.consts.Consts;
import com.open_care.application.resourceserver.consts.UIConsts;
import com.open_care.application.resourceserver.context.AuthorityContext;
import com.open_care.application.resourceserver.context.ComponentContext;
import com.open_care.application.resourceserver.context.ComponentInfo;
import com.open_care.application.resourceserver.dto.UIAuthDTO;
import com.open_care.application.resourceserver.dto.base.FieldDTO;
import com.open_care.application.resourceserver.repository.EntityRepository;
import com.open_care.application.resourceserver.repository.FieldRepository;
import com.open_care.application.resourceserver.repository.ReferenceRepository;
import com.open_care.application.resourceserver.repository.UiRepository;
import com.open_care.application.resourceserver.repository.WindowRepository;
import com.open_care.application.resourceserver.service.auth.AdAppConfigService;
import com.open_care.application.resourceserver.util.AppInfoUtil;
import com.open_care.application.resourceserver.util.ComponentAuthUtil;
import com.open_care.commons.xacml.entitlement.middleware.entity.RoleEntityDTO;
import com.open_care.commons.xacml.entitlement.middleware.entity.RoleEntityFieldDTO;
import com.open_care.configuration.configuration.AdEntity;
import com.open_care.configuration.configuration.AdField;
import com.open_care.configuration.configuration.AdReference;
import com.open_care.configuration.ui.AdWindow;
import com.open_care.constant.EntityBaseProperty;
import com.open_care.constant.GlobalConstant;
import com.open_care.constant.MessageConstant;
import com.open_care.context.AppInfoContext;
import com.open_care.auth.core.context.UserInfoContext;
import com.open_care.sys.AdUserPreferenceType;
import com.open_care.util.InflectorUtil;
import com.open_care.util.ResponseUtil;
import com.open_care.util.StringUtil;
import com.open_care.util.json.JsonConverter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UIService extends AbstractBaseService {

    @Autowired
    WindowRepository windowRepository;

    @Autowired
    FieldRepository fieldRepository;

    @Autowired
    EntityRepository entityRepository;

    @Autowired
    ActionService actionService;

    @Autowired
    JsonConverter jsonConverter;

    @Autowired
    ConfigurationService configurationService;

    @Autowired
    MetaUIService metaUIService;

    @Autowired
    SchemaService schemaService;

    @Autowired
    CacheService cacheUtil;

    @Autowired
    ReferenceRepository referenceRepository;

    @Autowired
    ComponentAuthUtil componentAuthUtil;

    @Value("${authority.schema}")
    public Boolean schemaAuthority;

    @Autowired
    private TableColumnConfigService tableColumnConfigService;

    @Autowired
    private UiRepository uiRepository;

    @Autowired
    private AdAppConfigService appConfigService;

    private static final String JSON_PATH_NAME = "jsonPathName";
    private static final String FIELD_ID = "fieldId";
    public static final String COMPONENT_ID = "componentId";

    private static final String UI_DATA = "UIData";
    private static final String MOBILE_UI_DATA = "mobileUIData";

    private static final String CONSTANTANT_OPERATION = "Operation";

    private static final String TOOLBAR_DEFAULT_BUTTON = "toolbarDefaultBtn";
    private static final String TOOLBAR_DROPDOWN = "toolbarDropdown";
    private static final String TOOLBAR_DROPDOWN_CONTEXT_MENU = "contextMenu";
    private static final String TOOLBAR_OPERATIONS = "toolbarOperations";
    private static final String AD_APP_CONFIG_ID = "aaaabbbbccccdddd";

    @Transactional(readOnly = true)
    public OcWindow getWindowMetadata(String componentName, String mode) {
//        ComponentContext.clearComponentInfo();
//        ComponentContext.setComponentInfo(new ComponentInfo());

        ComponentContext.reCreatComponentInfoAndKeepTopWindowName();
        ComponentContext.getComponentInfo().setMode(mode);

        AdWindow adWindow = getAdWindow(componentName, mode);
        OcWindow window = new OcWindow();
        window.setComponentId(adWindow.getComponentId());
        boolean forceVisible = StrUtil.equals(Consts.WINDOW_TYPE_SURVEY, adWindow.getWindowType());
        try {
            ComponentContext.getComponentInfo().setWindowId(adWindow.getId());
            setTopWindowName(adWindow.getComponentName());
            window.setComponentChildren(getComponent(adWindow.getUiSchema().get("UIData"), forceVisible));
            if (!Consts.PREVIEW_MODE.equals(mode)) {
                window.setEvents(addActionUrl(adWindow.getComponentId(), adWindow.getEvents()));
            }
            window.setComponentInfo(subJsonObject(adWindow.getUiSchema().get("UIData").getAsJsonObject()));
            window.setContext(adWindow.getContext());
            window.setStyles(adWindow.getStyles());
            window.setComponentName(adWindow.getComponentName());
            window.setComponentType(adWindow.getComponentType());
            window.setTitle(adWindow.getTitle());
            window.setUiSchemaDataCache(adWindow.getUiSchemaDataCache());
            window.setComponentDataCache(adWindow.getComponentDataCache());
            List<FieldDTO> fields = new ArrayList();
            getFormFieldSet(window.getComponentChildren(), fields);


            return window;
        } catch (Exception e) {
            e.printStackTrace();
            throw new IllegalStateException("获取窗体：" + adWindow.getTitle() + "出错！" + e.getMessage());
        }
    }

    private void setTopWindowName(String windowName) {
        String topWindowName = ComponentContext.getComponentInfo().getTopWindowName();
        if (Objects.isNull(topWindowName)) {
            ComponentContext.getComponentInfo().setTopWindowName(windowName);
        }
    }

    private JsonElement getMobileUIData(AdWindow window) {
        if (window.getUiSchema().has(MOBILE_UI_DATA)) {
            return window.getUiSchema().get(MOBILE_UI_DATA);
        }

        return window.getUiSchema().get(UI_DATA);
    }

    @Transactional(readOnly = true)
    public OcMWindow getMobileMetadata(String componentName, String mode) {
        ComponentContext.clearComponentInfo();
        ComponentContext.setComponentInfo(new ComponentInfo());

        if (mode.equals("null")) { //临时代码
            mode = Consts.WORKING_MODE;
        }

        ComponentContext.getComponentInfo().setMode(mode);
        List<AdWindow> adWindows = windowRepository.findByComponentNameAndMode(componentName, mode);
        if (CollectionUtils.isEmpty(adWindows)) {
            throw new IllegalStateException("根据'" + componentName + "'未找到对应的页面");
        }

        AdWindow adWindow = adWindows.get(0);

        OcMWindow window = new OcMWindow();
        window.setComponentId(adWindow.getComponentId());
        ComponentContext.getComponentInfo().setWindowId(adWindow.getId());
        window.setComponentChildren(getMobileComponentChildren(adWindow));
        if (mode.equals(Consts.WORKING_MODE)) {
            window.setEvents(addActionUrl(adWindow.getComponentId(), getWindowEvents(adWindow)));
        }
        window.setComponentInfo(subJsonObject(getMobileUIMetadata(adWindow).getAsJsonObject()));
        window.setContext(adWindow.getContext());
        window.setStyles(adWindow.getStyles());
        window.setComponentName(adWindow.getComponentName());
        window.setComponentType(adWindow.getComponentType());
        window.setTitle(adWindow.getTitle());
        window.setUiSchemaDataCache(adWindow.getUiSchemaDataCache());
        window.setComponentDataCache(adWindow.getComponentDataCache());
        List<FieldDTO> fields = new ArrayList();
        getFormFieldSet(window.getComponentChildren(), fields);
        return window;
    }

    private JsonElement getMobileUIMetadata(AdWindow window) {
        if (window.getUiSchema().has(MOBILE_UI_DATA)) {
            return window.getUiSchema().get(MOBILE_UI_DATA);
        }

        return window.getUiSchema().get(UI_DATA);
    }

    private List<OcComponent> getMobileComponentChildren(AdWindow window) {
        if (window.getUiSchema().has(MOBILE_UI_DATA)) {
            return getComponent(window.getUiSchema().get(MOBILE_UI_DATA));
        }

        return getMComponent(window.getUiSchema().get(UI_DATA));
    }

    private JsonElement getWindowEvents(AdWindow window) {
        if (window.getUiSchema().has(MOBILE_UI_DATA)) {
            return window.getUiSchema().get(MOBILE_UI_DATA).getAsJsonObject().getAsJsonArray("events");
        }

        return window.getEvents();
    }

    public JsonElement addActionUrl(String componentId, JsonElement events) {
        if (events == null) {
            return null;
        }

        actionService.handleEvents(events.getAsJsonArray());
        return events;
        /*String windowId = ComponentContext.getComponentInfo().getWindowId();
        JsonElement cacheEvents = cacheService.getEventsByComponent(windowId+componentId);
        if(cacheEvents == null) {
            actionService.handleEvents(events.getAsJsonArray());
            cacheService.setEventsByComponent(windowId + componentId, events);
            return events;
        } else {
            return cacheEvents;
        }*/
    }

    private String[] getAllFormInput() {
        String[] arg = {"Input", "Input.TextArea", "RichEditor", "DatePicker.RangePicker", "DatePicker", "TimePicker", "Select", "Radio", "Checkbox", "RichEditor"};
        return arg;
    }

    private List<OcComponent> getMComponent(JsonElement component) {
        List<OcComponent> components = new ArrayList<>();
        if (!component.getAsJsonObject().has("componentChildren")) {
            return components;
        }

        if (component.getAsJsonObject().get("componentChildren").isJsonNull()) {
            return components;
        }
        component.getAsJsonObject().getAsJsonArray("componentChildren").forEach(iter -> {
            OcComponent ocMComponent;// = new Component();
            if (iter.isJsonObject()) {
                JsonObject cmp = iter.getAsJsonObject();
                if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM)) {
                    ocMComponent = getMForm(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_CHECKBOXGROUP)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_DATEPICKER)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_INPUTITEM)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_PICKER)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_RADIOGROUP)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_SWITCH)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_CHECKBOXAGREEITEM)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_IMAGEPICKER)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_TAGGROUP)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_TEXTAREAITEM)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_MAP)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_FORM_AUDIO)) {
                    ocMComponent = getMFormItemInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_LIST_SUBLIST)) {
                    ocMComponent = getMPanelInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_LIST_SUBLIST)) {
                    ocMComponent = getMPanelInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_LISTVIEW)) {
                    ocMComponent = getMListViewInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_GRID)) {
                    ocMComponent = getMGridInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_LISTVIEW)) {
                    ocMComponent = getMListViewInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_NAVBAR)) {
                    ocMComponent = getMNavBarInfo(cmp);
                } else if (getComponentType(cmp).equals(Consts.COMPONENT_TYPE_M_STEPS)) {
                    ocMComponent = getMStepsInfo(cmp);
                } else {
                    ocMComponent = new OcComponent();
                }
                ocMComponent.setComponentId(getComponentValueByKey(cmp, "componentId"));
                ocMComponent.setComponentType(getComponentValueByKey(cmp, "componentType"));
                ocMComponent.setStyles(getComponentValueByKey(cmp, "styles"));
                ocMComponent.setComponentName(getComponentValueByKey(cmp, "componentName"));
                ocMComponent.setComponentInfo(subJsonObject(cmp));
                if (ComponentContext.getComponentInfo().getMode().equals(Consts.WORKING_MODE)) {
                    ocMComponent.setEvents(addActionUrl(ocMComponent.getComponentId(), cmp.get("events")));
                }
                if (getComponentType(cmp).equals("Column1")) {

                } else {
                    List<OcComponent> componentList = getMComponent(cmp);
                    if (componentList != null && componentList.size() > 0) {
                        ocMComponent.getComponentChildren().addAll(componentList);
                    }

                    if (StringUtils.equalsAny(getComponentType(cmp), "TableForm", "Table") && cmp.has("operation")) { //处理操作列
                        ocMComponent.getComponentChildren().add(getMOperationColumn(cmp.getAsJsonArray("operation")));
                    }
                }
                //                        setComponentVisiable(ocMComponent, cmp);
                boolean isHidden = ocMComponent.getComponentHidden() != null && ocMComponent.getComponentHidden();
                if (!isHidden) {
                    components.add(ocMComponent);
                }

            }
        });
        return components;
    }


    private List<OcComponent> getComponent(JsonElement component) {
        return getComponent(component, false);
    }

    private List<OcComponent> getComponent(JsonElement component, boolean forceVisible) {
        List<OcComponent> components = new ArrayList<>();
        if (!component.getAsJsonObject().has("componentChildren")) {
            return components;
        }
        try {
            //todo: 优化：字符串常量的使用枚举类
            component.getAsJsonObject().getAsJsonArray("componentChildren").forEach(
                    iter -> {
                        OcComponent ocComponent;// = new Component();
                        if (iter.isJsonObject()) {
                            JsonObject cmp = iter.getAsJsonObject();
                            if (getComponentType(cmp).equals("Form")) {
                                ocComponent = getFormInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "TableForm", "Table")) {
                                ocComponent = getTableInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "Input", "InputNumber", "Input.TextArea", "RichEditor", "DatePicker.RangePicker", "TimePicker.RangePicker", "Map", "Image", ComponentTypeConsts.SWITCH)) {
                                ocComponent = getFormItemInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "Cascader")) {
                                ocComponent = getCascaderInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "Upload")) {
                                ocComponent = getFormItemInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "DatePicker")) {
                                ocComponent = getFormItemInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "TimePicker")) {
                                ocComponent = getFormItemInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "RichEditor")) {
                                ocComponent = getFormItemInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "Select", "Radio", "ComplexSelect", "TreeSelect")) {
                                ocComponent = getSelectInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "Checkbox")) {
                                ocComponent = getFormItemInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "CheckboxGroup")) {
                                ocComponent = getSelectInfo(cmp);
                            } else if (getComponentType(cmp).equals("Column")) {
                                ocComponent = getColumnInfo(cmp);
                            } else if (getComponentType(cmp).equals("Calendar")) {
                                ocComponent = getCalendarInfo(cmp);
                            } else if (getComponentType(cmp).equals("Tabs.TabPane")) {
                                ocComponent = getTabPanelInfo(cmp);
                            } else if (getComponentType(cmp).equals("Tabs")) {
                                ocComponent = getTabsInfo(cmp);
                            } else if (getComponentType(cmp).equals("SubForm")) {
                                ocComponent = getSubFormInfo(cmp);
                            } else if (getComponentType(cmp).equals("Tree")) {
                                ocComponent = getTreeInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "Collapse")) {
                                ocComponent = getCollapseInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "Collapse.Panel")) {
                                ocComponent = getCollapsePanelInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "BarChart", "PieChart", "LineChart")) {
                                ocComponent = getChartInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "List")) {
                                ocComponent = getListInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "Dropdown")) {
                                ocComponent = getDropdownInfo(cmp);
                            } else if (StringUtils.equalsAny(getComponentType(cmp), "Steps")) {
                                ocComponent = getStepsInfo(cmp);
                            } else if (getComponentType(cmp).equals("Card")) {
                                ocComponent = getCardInfo(cmp);
                            } else if (getComponentType(cmp).equals("SearchInput")) {
                                ocComponent = getSearchInput(cmp);
                            } else if (getComponentType(cmp).equals("ColorPicker")) {
                                ocComponent = getColorPicker(cmp);
                            } else if (getComponentType(cmp).equals("Rate")) {
                                ocComponent = getFormItemInfo(cmp);
                            } else {
                                ocComponent = new OcComponent();
                            }

                            if (ocComponent != null) {
                                ocComponent.setComponentId(getComponentValueByKey(cmp, "componentId"));
                                ocComponent.setComponentType(getComponentValueByKey(cmp, "componentType"));
                                ocComponent.setStyles(getComponentValueByKey(cmp, "styles"));
                                ocComponent.setComponentName(getComponentValueByKey(cmp, "componentName"));
                                if (ComponentContext.getComponentInfo().getMode().equals(Consts.WORKING_MODE)) {
                                    //判断是否有缓存

                                    ocComponent.setEvents(addActionUrl(ocComponent.getComponentId(), cmp.get("events")));
                                }
                                ocComponent.setComponentInfo(subJsonObject(cmp));

                                if (!StrUtil.equals(getComponentType(cmp), "Column1")) {
                                    List<OcComponent> componentList = getComponent(cmp, forceVisible);
                                    if (componentList != null && componentList.size() > 0) {
                                        ocComponent.getComponentChildren().addAll(componentList);
                                    }

                                    if (StringUtils.equalsAny(getComponentType(cmp), "TableForm", "Table", "Tabs.TabPane")) {

                                        if (cmp.has("operation")) { //处理操作列
                                            ocComponent.getComponentChildren().add(getOperationColumn(cmp.getAsJsonArray("operation")));
                                        }
                                        configTableColumnVisible(ocComponent);
                                    }
                                }

                                setComponentVisible(ocComponent, cmp, forceVisible);
                                boolean isHidden = ocComponent.getComponentHidden() != null && ocComponent.getComponentHidden();
                                //if (!isHidden)
                                components.add(ocComponent);
                                //}

                            }
                        }
                    });
        } catch (Exception e) {
            e.getMessage();
            logger.error("getComponent error: component=" + jsonConverter.toJson(component), e);
        }
        return components;
    }

    private void configTableColumnVisible(OcComponent component) {
        if (CollUtil.isEmpty(component.getComponentChildren())) {
            return;
        }

        List<String> visibleColumnDateIndexes =
                tableColumnConfigService.getUserConfiguredTableColumn(
                        UserTableConfigContext.builder()
                                .windowId(ComponentContext.getComponentInfo().getWindowId())
                                .windowName(ComponentContext.getComponentInfo().getTopWindowName())
                                .componentId(component.getComponentId())
                                .userId(UserInfoContext.getUserId())
                                .userPreferenceType(AdUserPreferenceType.TABLE_COLUMN_CONFIG)
                                .build()
                );

        if (CollUtil.isEmpty(visibleColumnDateIndexes)) {
            return;
        }

        List<OcComponent> components = new ArrayList<>(component.getComponentChildren().size());
        OcComponent[] visibleComponents = new OcComponent[visibleColumnDateIndexes.size()];
        for (OcComponent ocComponent : component.getComponentChildren()) {
            if (!(ocComponent instanceof OcColumn)) {
                components.add(ocComponent);
                continue;
            }
            OcColumn column = (OcColumn) ocComponent;
            if (!StrUtil.equals(column.getComponentType(), Consts.TABLE_COLUMN_COMPONENT_TYPE)) {
                components.add(column);
                continue;
            }

            int i = visibleColumnDateIndexes.indexOf(column.getDataIndex());
            if (i != -1) {
                column.setVisible(Boolean.TRUE);
                visibleComponents[i] = column;
                continue;
            }

            column.setVisible(Boolean.FALSE);
            components.add(column);
        }
        components.addAll(0, Arrays.stream(visibleComponents).filter(ObjUtil::isNotNull).collect(Collectors.toList()));
        component.setComponentChildren(components);
    }

    public OcMNavBar getMNavBarInfo(JsonObject component) {
        OcMNavBar mNavBar = new OcMNavBar();
        JsonObject leftContent = new JsonObject();
        leftContent.add("componentChildren", component.getAsJsonArray("leftContent"));
        mNavBar.setLeftContent(getMComponent(leftContent));

        JsonObject centerContent = new JsonObject();
        centerContent.add("componentChildren", component.getAsJsonArray("centerContent"));
        mNavBar.setCenterContent(getMComponent(centerContent));

        JsonObject rightContent = new JsonObject();
        rightContent.add("componentChildren", component.getAsJsonArray("rightContent"));
        mNavBar.setRightContent(getMComponent(rightContent));

        return mNavBar;
    }

    public OcMSteps getMStepsInfo(JsonObject component) {
        OcMSteps steps = new OcMSteps();
        steps.setDirection(getComponentValueByKey(component, "direction"));
        steps.setSize(getComponentValueByKey(component, "size"));
        steps.setStatus(getComponentValueByKey(component, "status"));
        return steps;
    }


    public List<OcComponent> getTabAndButtonComponentInfo(JsonElement component) {
        return getTabAndButtonComponentInfo(component, true);
    }

    public List<OcComponent> getTabAndButtonComponentInfo(JsonElement component, boolean checkAuthority) {

        JsonObject componentJsonObject = component.getAsJsonObject();

        //todo: warning: visible为false或者visible为空的按钮，也会展示在权限配置页面
        if (!componentJsonObject.has("componentChildren")) {
            return CollUtil.list(false);
        }

        List<OcComponent> components = new ArrayList<>();

        componentJsonObject.getAsJsonArray("componentChildren").forEach(iter -> {
            OcComponent ocComponent;// = new Component();
            if (iter.isJsonObject()) {
                JsonObject cmp = iter.getAsJsonObject();
                if (getComponentType(cmp).equals("Tabs")) {
                    ocComponent = getTabsInfo(cmp);
                } else {
                    ocComponent = new OcComponent();
                }

                if (ocComponent != null) {
                    ocComponent.setComponentId(getComponentValueByKey(cmp, "componentId"));
                    ocComponent.setComponentType(getComponentValueByKey(cmp, "componentType"));
                    ocComponent.setTitle(getComponentTitle(ocComponent.getComponentType(), cmp));
                    ocComponent.setComponentName(getComponentValueByKey(cmp, "componentName"));

                    if (!StrUtil.equals(getComponentType(cmp), "Column1")) {
                        List<OcComponent> componentList = getTabAndButtonComponentInfo(cmp, checkAuthority);
                        if (componentList != null && componentList.size() > 0) {
                            ocComponent.getComponentChildren().addAll(componentList);
                        }

                        List<OcComponent> componentChildren = ocComponent.getComponentChildren();
                        if (cmp.has("operation")) { //处理操作列
                            componentChildren.add(getOperationColumn(cmp.getAsJsonArray("operation"), false));
                        }

                        appendToolbarComponentIfExisted(cmp, componentChildren);
                    }

                    if (checkAuthority) {
                        setComponentVisible(ocComponent, cmp);
                    } else {
                        ocComponent.setVisible(true);
                    }
                    boolean isHidden = ocComponent.getComponentHidden() != null && ocComponent.getComponentHidden();
                    if (!isHidden) {
                        components.add(ocComponent);
                    }
                }
            }
        });
        return components;
    }

    private boolean componentCanVisible(JsonObject component) {

        return component.has(Consts.VISIBLE_CONST)
                &&
                BooleanUtil.isTrue(
                        component.get(Consts.VISIBLE_CONST).getAsBoolean()
                );
    }

    private void appendToolbarComponentIfExisted(JsonObject jsonObject, List<OcComponent> componentChildren) {

        if (!StrUtil.equals(getComponentType(jsonObject), "Table")) {
            return;
        }

        if (jsonObject.has(TOOLBAR_DEFAULT_BUTTON)) {
            componentChildren.add(getToolbarColumn(jsonObject.getAsJsonObject(TOOLBAR_DEFAULT_BUTTON), false));
        }

        if (jsonObject.has(TOOLBAR_DROPDOWN)) {
            JsonObject toolbarDropdown = jsonObject.getAsJsonObject(TOOLBAR_DROPDOWN);
            componentChildren.add(getToolbarColumn(toolbarDropdown, false));
            if (toolbarDropdown.has(TOOLBAR_DROPDOWN_CONTEXT_MENU)) {
                componentChildren.add(getToolbarColumn(toolbarDropdown.getAsJsonArray(TOOLBAR_DROPDOWN_CONTEXT_MENU), false));
            }
        }

        if (jsonObject.has(TOOLBAR_OPERATIONS)) {
            componentChildren.add(getToolbarColumn(jsonObject.getAsJsonArray(TOOLBAR_OPERATIONS), false));
        }
    }

    private String getComponentTitle(String componentType, JsonObject cmp) {
        if (StringUtils.equals(componentType, "Tabs.TabPane")) {//标签页
            return getComponentValueByKey(cmp, "tab");
        }

        return getComponentValueByKey(cmp, "title");
    }

    private void setComponentVisible(OcComponent component, JsonObject cmp) {
        setComponentVisible(component, cmp, false);
    }

    private void setComponentVisible(OcComponent component, JsonObject cmp, boolean forceVisible) {
        // 强制可见
        if (forceVisible) {
            component.setVisible(true);
            return;
        }
        JsonElement jsonElement = cmp.get("visible");
        // 1.系統默认 组件是否可见
        if ("0".equals(AppInfoContext.getApp().getTenantId()) && "0".equals(AppInfoContext.getApp().getAppInstId()) && "0".equals(AppInfoContext.getApp().getAppDefId())) {
            if (jsonElement != null) {
                component.setVisible(jsonElement.getAsBoolean());
            } else {
                component.setVisible(true);
            }
        } else {
            if (jsonElement == null) {
                if (StringUtils.equalsAny(component.getComponentType(), "Column")) {
                    if (component.getVisible() == null) {
                        component.setVisible(true);
                    }
                } else {
                    component.setVisible(false);
                }
            } else {
                if (jsonElement.isJsonNull()) {
                    component.setVisible(false);
                }
                if (jsonElement.isJsonPrimitive()) {
                    boolean componentVisiable = jsonElement.getAsBoolean();
                    if (componentVisiable) {
                        //如果窗口定义可见   但权限不可见 则不可见
                        if (component.getVisible() == null) {
                            component.setVisible(componentVisiable);
                        }
                    } else {
                        //如果窗口直接定义 不可见 不管权限是否可见 都为不可见
                        component.setVisible(componentVisiable);
                    }

                    if (StringUtils.equalsAny(component.getType(), "Operation", "Button")) {
                        component.setComponentHidden(!componentVisiable);
                    }

                }
            }
        }

        //如果为真 则进行 权限控制，如果为假 则默认按照系统默认的是否可见进行设置
        if (BooleanUtil.isTrue(component.getVisible())) {
            componentAuthUtil.setComponentByAuth(component, cmp.get("componentId").getAsString());
        }

        if (StringUtils.equalsAny(component.getComponentType(), "Table", "TableForm")) {
            recursiveSetComponentByAuth(component);
        }

        recursiveSetButtonComponentVisibleByAuth(component, AuthorityContext.getAuthority().getUiAuthDTOs());

        setToolbarAndCollpaseVisible(cmp);

        if (!StringUtils.equalsAny(component.getComponentType(), "Table", "Card", "SearchInput", "Collapse.Panel", "Tabs.TabPane", "Calendar", "SubForm")) {
            component.setVisible(getComponentVisibleByChildren(component));
        }

    }

    private void setToolbarAndCollpaseVisible(JsonObject jsonObject) {

        // 如果是超级管理员 则放开所有权限
        if (AuthorityContext.seeAllComponent()) {
            return;
        }

        List<UIAuthDTO> uiAuthDTOs = AuthorityContext.getAuthority().getUiAuthDTOs();
        if (CollUtil.isEmpty(uiAuthDTOs)) {
            return;
        }

        if (StrUtil.equals(getComponentType(jsonObject), "Collapse.Panel") && jsonObject.has("operation")) {

            Map<String, JsonObject> map = ListUtil.toList(jsonObject.getAsJsonArray("operation"))
                    .stream()
                    .filter(iter -> iter.getAsJsonObject().has(UIConsts.COMPONENTID))
                    .collect(
                            Collectors.toMap(jsonElement -> jsonElement.getAsJsonObject().get(UIConsts.COMPONENTID).getAsString(), jsonElement -> (JsonObject) jsonElement)
                    );

            setJsonObjectVisible(uiAuthDTOs, map);
            return;
        }

        if (StrUtil.equals(getComponentType(jsonObject), "Table")) {
            if (jsonObject.has(TOOLBAR_DEFAULT_BUTTON)) {
                JsonObject asJsonObject = jsonObject.getAsJsonObject(TOOLBAR_DEFAULT_BUTTON);

                if (asJsonObject.has(UIConsts.COMPONENTID)) {
                    setJsonObjectVisible(uiAuthDTOs, MapUtil.of(asJsonObject.get(UIConsts.COMPONENTID).getAsString(), asJsonObject));
                }
            }

            if (jsonObject.has(TOOLBAR_DROPDOWN)) {
                JsonObject toolbarDropdown = jsonObject.getAsJsonObject(TOOLBAR_DROPDOWN);

                if (toolbarDropdown.has(UIConsts.COMPONENTID)) {
                    setJsonObjectVisible(uiAuthDTOs, MapUtil.of(toolbarDropdown.get(UIConsts.COMPONENTID).getAsString(), toolbarDropdown));
                }

                if (toolbarDropdown.has(TOOLBAR_DROPDOWN_CONTEXT_MENU)) {

                    Map<String, JsonObject> map = ListUtil.toList(toolbarDropdown.getAsJsonArray(TOOLBAR_DROPDOWN_CONTEXT_MENU))
                            .stream()
                            .filter(iter -> iter.getAsJsonObject().has(UIConsts.COMPONENTID))
                            .collect(
                                    Collectors.toMap(jsonElement -> jsonElement.getAsJsonObject().get(UIConsts.COMPONENTID).getAsString(), jsonElement -> (JsonObject) jsonElement)
                            );

                    setJsonObjectVisible(uiAuthDTOs, map);
                }
            }

            if (jsonObject.has(TOOLBAR_OPERATIONS)) {
                Map<String, JsonObject> map = ListUtil.toList(jsonObject.getAsJsonArray(TOOLBAR_OPERATIONS))
                        .stream()
                        .filter(iter -> iter.getAsJsonObject().has(UIConsts.COMPONENTID))
                        .collect(
                                Collectors.toMap(jsonElement -> jsonElement.getAsJsonObject().get(UIConsts.COMPONENTID).getAsString(), jsonElement -> (JsonObject) jsonElement)
                        );

                setJsonObjectVisible(uiAuthDTOs, map);
            }
        }
    }

    private static void setJsonObjectVisible(List<UIAuthDTO> uiAuthDTOs, Map<String, JsonObject> map) {
        map.forEach((componentId, json) -> {
            if (json.has("visible") && BooleanUtil.isFalse(json.getAsJsonPrimitive("visible").getAsBoolean())) {
                return;
            }

            json.addProperty("visible", Boolean.FALSE);

            for (UIAuthDTO uiAuthDTO : uiAuthDTOs) {
                Map<String, List<String>> selData = uiAuthDTO.getSelData();
                if (MapUtil.isEmpty(selData)) {
                    continue;
                }
                if (selData.containsKey(componentId)) {
                    List<String> componentAuth = selData.get(componentId);
                    componentAuth.forEach(
                            x -> {
                                if (x.contains("visible")) {
                                    json.addProperty("visible", Boolean.TRUE);
                                }
                            }
                    );
                }
            }
        });
    }

    private static void setJsonObjectVisible(List<UIAuthDTO> uiAuthDTOs, OcComponent component) {

        for (UIAuthDTO uiAuthDTO : uiAuthDTOs) {
            Map<String, List<String>> selData = uiAuthDTO.getSelData();
            if (MapUtil.isEmpty(selData)) {
                continue;
            }
            String componentId = component.getComponentId();
            if (selData.containsKey(componentId)) {
                List<String> componentAuth = selData.get(componentId);
                componentAuth.forEach(
                        x -> {
                            if (x.contains("visible")) {
                                component.setVisible(Boolean.TRUE);
                            }
                        }
                );
            }
        }
    }


    private static boolean checkComponentIdWhetherAllowedVisible(List<UIAuthDTO> uiAuthDTOs, String componentId) {

        return uiAuthDTOs.stream()
                .filter(uiAuthDTO -> ObjUtil.isNotNull(uiAuthDTO) && MapUtil.isNotEmpty(uiAuthDTO.getSelData()))
                .map(UIAuthDTO::getSelData)
                .filter(selData -> selData.containsKey(componentId))
                .anyMatch(selData -> {
                    List<String> auths = selData.get(componentId);
                    return CollUtil.isNotEmpty(auths) && auths.contains("visible");
                });
    }

    private void recursiveSetButtonComponentVisibleByAuth(OcComponent component, List<UIAuthDTO> uiAuthDTOs) {

        // 如果是超级管理员 则放开所有权限
        if (AuthorityContext.seeAllComponent()) {
            return;
        }

        if (ObjUtil.isNull(component) ||
                StrUtil.isBlank(component.getComponentId()) ||
                StrUtil.isBlank(component.getComponentType()) ||
                !BooleanUtil.isTrue(component.getVisible())) {
            return;
        }

        if (StrUtil.equals(component.getComponentType(), "Button")) {
            component.setVisible(checkComponentIdWhetherAllowedVisible(uiAuthDTOs, component.getComponentId()));
        }

        List<OcComponent> componentChildren = component.getComponentChildren();

        if (CollUtil.isEmpty(componentChildren)) {
            return;
        }

        for (OcComponent componentChild : componentChildren) {

            recursiveSetButtonComponentVisibleByAuth(componentChild, uiAuthDTOs);
        }
    }

    private void recursiveSetComponentByAuth(OcComponent component) {
        List<OcComponent> childrenComponent = new ArrayList<>();

        childrenComponent.addAll(component.getComponentChildren());

        if (component instanceof OcCard) {
            childrenComponent.addAll(((OcCard) component).getOperation());
        }

        childrenComponent.stream().forEach(
                iter -> {
                    if (BooleanUtils.isFalse(iter.getVisible())) {
                        return;
                    }

                    if (!StringUtils.equals(CONSTANTANT_OPERATION, iter.getComponentType())) {
                        componentAuthUtil.setComponentByAuth(iter, iter.getComponentId());
                    }
                    recursiveSetComponentByAuth(iter);
                }
        );
    }

    private Boolean getComponentVisibleByChildren(OcComponent component) {
        if (Objects.isNull(component.getComponentChildren()) || component.getComponentChildren().isEmpty()) {
            return component.getVisible();
        }
        return component.getComponentChildren().stream().filter(f -> Objects.nonNull(f.getVisible()) && f.getVisible()).collect(Collectors.toList()).size() > 0;
    }

    public static String getComponentValueByKey(JsonObject jsonObject, String key) {
        if (jsonObject.has(key)) {
            return jsonObject.getAsJsonPrimitive(key).getAsString();
        }
        return null;
    }

    private OcTabs getTabsInfo(JsonObject component) {
        OcTabs tabs = new OcTabs();

        String defaultActiveKey = getComponentValueByKey(component, "defaultActiveKey");
        String tabPosition = getComponentValueByKey(component, "tabPosition");
        String type = getComponentValueByKey(component, "type");
        tabs.setDefaultActiveKey(defaultActiveKey);
        if (Objects.nonNull(tabPosition)) tabs.setTabPosition(tabPosition);
        if (Objects.nonNull(type)) tabs.setType(type);

        return tabs;
    }

    private OcCard getCardInfo(JsonObject component) {
        OcCard card = new OcCard();
        if (component.has("operation")) {
            JsonArray jsonArray = component.getAsJsonArray("operation");
            card.setOperation(handleOperation(jsonArray, "Button"));
        }
        String windowId = getComponentValueByKey(component, "windowId");
        if (StringUtils.isNotEmpty(windowId)) {
            AdWindow adWindow = windowRepository.getOne(windowId);
            card.getComponentChildren().add(getRefComponent(adWindow));
        }

        return card;
    }

    public static String getComponentType(JsonElement jsonElement) {
        if (jsonElement.isJsonObject()) {
            return getComponentValueByKey(jsonElement.getAsJsonObject(), "componentType");
        }

        return null;
    }

    private OcComponent getComponentInfo(JsonObject jsonObject) {
        OcRow row = new OcRow();
        row.setComponentInfo(jsonObject);

        return row;
    }

    private OcTab getTabPanelInfo(JsonObject component) {
        OcTab tab = new OcTab();
        String windowId = getComponentValueByKey(component, "windowId");
        String refComponentName = getComponentValueByKey(component, "windowComponentName");

        if (StringUtils.isNotEmpty(windowId)) {
            AdWindow adWindow = windowRepository.getOne(windowId);
            OcRefComponent refComponent = new OcRefComponent();
            refComponent.setRefComponentName(adWindow.getComponentName());
            refComponent.setComponentName(refComponentName == null ? adWindow.getComponentName() : refComponentName);
            refComponent.setComponentType("Ref");
            if (StringUtils.isNotEmpty(ComponentContext.getComponentInfo().getMode())) {
                refComponent.setRefComponent(getRefWindowMetadata(adWindow));
            }
            tab.getComponentChildren().add(refComponent);
        }

        if ("false".equals(getComponentValueByKey(component, "visible"))) {
            tab.setVisible(false);
        }

        return tab;
    }

    private Map getRefWindowMetadata(AdWindow adWindow) {
        String parentWindowId = ComponentContext.getComponentInfo().getWindowId();
        OcWindow ocWindow = this.getWindowMetadata(adWindow.getComponentName(), ComponentContext.getComponentInfo().getMode());
        Map map = metaUIService.handleComponent(ocWindow, ComponentContext.getComponentInfo().getMode());
        ComponentContext.getComponentInfo().setWindowId(parentWindowId);
        return map;
    }

    private OcColumn getColumnInfo(JsonObject component) {
        OcColumn column = new OcColumn();
        String fieldId = getComponentValueByKey(component, FIELD_ID);

        if (fieldId != null) {
            AdField field = tryGetField(fieldId);

            if (field != null) {
                column.setDataIndex(getFieldName(fieldId));

                trySetFieldOptions(field, column);

                setFieldByAuth(column, field);
            }

        }

        String jsonPath = getComponentValueByKey(component, JSON_PATH_NAME);
        if (StringUtils.isNotEmpty(jsonPath)) {
            column.setDataIndex(replaceDotToPound(jsonPath));
        } else {
            column.setJsonPathName(replacePoundToDot(column.getDataIndex()));
        }

        column.setValueChangeFrom(getValueChangeFormByJsonSchemaData(component));
        column.setDataLinkedComponents(getListDataLinkedComponentsByJsonSchemaData(component));

        AdWindow refWindow = getWindowComponentName(component, "expandPopoverRenderRef");
        if (Objects.nonNull(refWindow)) {
            column.setExpandPopoverRenderRef(refWindow.getComponentName());
            column.getComponentChildren().add(getRefComponent(refWindow));
        }


        return column;
    }

    private AdField tryGetField(String fieldId) {
        AdField field = null;
        if (fieldId.contains(".")) {
            String[] fieldSet = fieldId.split("\\.");
            String entityFieldId = fieldSet[fieldSet.length - 1];
            if (fieldRepository.existsById(entityFieldId)) {
                field = fieldRepository.getOne(fieldId);
            }
        } else {
            if (fieldRepository.existsById(fieldId)) {
                field = fieldRepository.getOne(fieldId);
            }
        }
        return field;
    }

    private void trySetFieldOptions(AdField field, OcColumn column) {
        try {
            if (field.getReference() != null) {
                column.setReferenceId(field.getReference().getId());
                List<Map<String, Object>> refList = configurationService.getValueOptions(field.getReference());
                refList.stream().forEach(iter -> {
                    column.getOptions().add(getValueOptions(iter));
                    //iter.get("children")
                });
            } else if (field.getClassType().toLowerCase().equals("boolean")) { //bool值默认按是否返回Options
                OcValueOptions ocValueOptions = new OcValueOptions();
                ocValueOptions.setLabel("是");
                ocValueOptions.setValue(true);

                OcValueOptions ocValueOptions1 = new OcValueOptions();
                ocValueOptions1.setLabel("否");
                ocValueOptions1.setValue(false);

                column.getOptions().add(ocValueOptions);
                column.getOptions().add(ocValueOptions1);
            }
            // 某些字段 没有引用对象 ，同样需要options
            if (field.getEntity() != null) {
                List<OcValueOptions> listOcValue = getOptionsByEntityNameAndType(field.getEntity().getClassName(), field.getName());
                column.getOptions().addAll(listOcValue);
            }
        } catch (Exception e) {
            logger.warn("设置属性选项错误: {}", e.getMessage());
            return;
        }
    }

    public OcValueOptions getValueOptions(Map map) {

        OcValueOptions valueOptions = new OcValueOptions();
        valueOptions.setLabel(map.get("label"));
        valueOptions.setValue(map.get("value"));
        if (map.containsKey("code")) {
            valueOptions.setCode(map.get("code"));
        }
        if (map.containsKey("extraAttribute")) {
            valueOptions.setExtraAttribute(map.get("extraAttribute"));
        }
        List children = (List) map.get("children");
        if (children != null && children.size() > 0) {
            children.stream().forEach(iter -> {
                Map child = (Map) iter;
                valueOptions.getChildren().add(getValueOptions(child));
            });
        }
        return valueOptions;
    }


    private List<OcValueOptions> getOptionsByEntityNameAndType(String className, String fieldName) {

        List<OcValueOptions> list = new ArrayList<>();
        if ("com.open_care.message.OcMsgConfig".equals(className)) {
            if ("msgType".equals(fieldName)) {

                OcValueOptions valueOption1 = new OcValueOptions();
                valueOption1.setLabel(MessageConstant.MESSAGE_TYPE_ENTITY_TIME_VALUE);
                valueOption1.setValue(MessageConstant.MESSAGE_TYPE_ENTITY_TIME_NAME);
                OcValueOptions valueOption2 = new OcValueOptions();
                valueOption2.setLabel(MessageConstant.MESSAGE_TYPE_FORM_DATA_VALUE);
                valueOption2.setValue(MessageConstant.MESSAGE_TYPE_FORM_DATA_NAME);
                OcValueOptions valueOption3 = new OcValueOptions();
                valueOption3.setLabel(MessageConstant.MESSAGE_TYPE_WORK_FLOW_VALUE);
                valueOption3.setValue(MessageConstant.MESSAGE_TYPE_WORK_FLOW_NAME);
                OcValueOptions valueOption4 = new OcValueOptions();
                valueOption4.setLabel(MessageConstant.MESSAGE_TYPE_DROOL_VALUE);
                valueOption4.setValue(MessageConstant.MESSAGE_TYPE_DROOL_NAME);

                list.add(valueOption1);
                list.add(valueOption2);
                list.add(valueOption3);
                list.add(valueOption4);
            }
        } else {

        }

        return list;

    }

    private OcMForm getMForm(JsonObject component) {
        OcMForm ocMForm = new OcMForm();
        String entityDefId = getComponentValueByKey(component, "entityDefId");
        if (entityDefId != null) {
            AdEntity entity = entityRepository.getOne(entityDefId);
            ocMForm.setEntityName(entity.getClassName());
        }
        ComponentContext.getComponentInfo().setSearchForm(false);
        return ocMForm;
    }

    private OcMInput getMFormItemInfo(JsonObject component) {
        OcMInput formItem = new OcMInput();
        String fieldId = getComponentValueByKey(component, FIELD_ID);

        if (fieldId != null) {
            AdField field = getField(fieldId);
            formItem.setFormItemName(getFieldName(fieldId));
            //AdField field = fieldRepository.getOne(fieldId);
            //field.getEntity().getClassName();
            //formItem.setFormItemName(field.getName());
            formItem.setDefaultValue(schemaService.getFieldDefaultValue(field));
            formItem.setAdditionalProperties(getMFormItemInfo(component.getAsJsonObject("additionalProperties"), field));


            if (field.getReference() != null) {
                List<Map<String, Object>> refList = configurationService.getValueOptions(field.getReference());
                refList.stream().forEach(iter -> {
                    formItem.getOptions().add(getValueOptions(iter));
                    //iter.get("children")
                });
            }
            // 某些字段 没有引用对象 ，同样需要options
            if (field.getEntity() != null) {
                List<OcValueOptions> listOcValue = getOptionsByEntityNameAndType(field.getEntity().getClassName(), field.getName());
                formItem.getOptions().addAll(listOcValue);
            }

            if (component.has("visible")) {
                formItem.setVisible(component.get("visible").getAsJsonPrimitive().getAsBoolean());
            }
            setMFieldByAuth(formItem, field);
        }

        formItem.setDataLinkedComponents(getListDataLinkedComponentsByJsonSchemaData(component));
        return formItem;
    }

    private OcComponent getMPanelInfo(JsonObject component) {
        OcComponent tab = new OcComponent();
        String windowId = getComponentValueByKey(component, "windowId");
        if (StringUtils.isNotEmpty(windowId)) {
            AdWindow adWindow = windowRepository.getOne(windowId);
            OcMRefComponent refComponent = new OcMRefComponent();
            refComponent.setRefComponentName(adWindow.getComponentName());
            refComponent.setComponentName(adWindow.getComponentName());
            refComponent.setComponentType("Ref");
            tab.getComponentChildren().add(refComponent);
        }
        return tab;
    }

    private OcComponent getMGridInfo(JsonObject component) {
        OcMGrid ocMGrid = null;

        if (component.has("data")) {
            ocMGrid = new OcMGrid();
            if (component.get("data").isJsonArray()) {
                JsonArray jsonArray = component.get("data").getAsJsonArray();
                List listData = new ArrayList();
                for (JsonElement jsonElement : jsonArray) {
                    if (jsonElement.isJsonObject() && jsonElement.getAsJsonObject().has("windowId") && jsonElement.getAsJsonObject().get("windowId") != null) {
                        String windowId = jsonElement.getAsJsonObject().get("windowId").getAsString();
                        AdWindow adWindow = windowRepository.getOne(windowId);
                        jsonElement.getAsJsonObject().addProperty("windowComponentName", adWindow.getComponentName());
                    }
                    listData.add(jsonElement);
                }
                ocMGrid.setData(listData);
            }

        }
        return ocMGrid;
    }

    private OcMListView getMListViewInfo(JsonObject component) {

        OcMListView ocMListView = new OcMListView();
        String renderRowTemplate = getComponentValueByKey(component, "renderRowTemplate");
        List<String> listField = StringUtil.getFieldNameByRegex(renderRowTemplate);

        String entityDefId = getComponentValueByKey(component, "entityDefId");

        if (entityDefId != null) {
            AdEntity entity = entityRepository.getOne(entityDefId);
            Map<String, AdField> mapField = new HashMap();
            entity.getFields().stream().forEach(iter -> {
                mapField.put(iter.getName(), iter);
            });

            Map<String, List<OcValueOptions>> mapListOcValue = new HashMap<>();
            listField.stream().forEach(iter -> {
                if (mapField.containsKey(iter)) {
                    AdField field = mapField.get(iter);
                    // 某些字段 没有引用对象 ，同样需要options
                    if (field.getEntity() != null && field.getReference() != null) {
                        List<OcValueOptions> listOcValue = new ArrayList<>();
                        List<Map<String, Object>> refList = configurationService.getValueOptions(field.getReference());
                        refList.stream().forEach(each -> {
                            listOcValue.add(metaUIService.getValueOptions(each));
                        });
                        //List<OcValueOptions> listOcValue = getOptionsByEntityNameAndType(field.getEntity().getClassName(), field.getName());
                        mapListOcValue.put(iter, listOcValue);
                    }
                }
            });
            ocMListView.setOptions(mapListOcValue);
        }

        return ocMListView;
    }

    private OcInput getFormItemInfo(JsonObject component) {
        OcInput formItem = new OcInput();
        String componentId = getComponentValueByKey(component, COMPONENT_ID);
        String fieldId = getComponentValueByKey(component, FIELD_ID);
        String jsonPath = getComponentValueByKey(component, JSON_PATH_NAME);

        if (fieldId != null) {
            AdField field = getField(fieldId);
            formItem.setFormItemName(getFieldName(fieldId));
            formItem.setDefaultValue(schemaService.getFieldDefaultValue(field));
            formItem.setAdditionalProperties(getFormItemInfo(component.getAsJsonObject("additionalProperties"), field));
            setFieldByAuth(formItem, field);
        }

        if (StringUtils.isNotEmpty(jsonPath)) {
            formItem.setFormItemName(replaceDotToPound(jsonPath));
        } else {
            formItem.setJsonPathName(replacePoundToDot(formItem.getFormItemName()));
        }

        /*if(StringUtils.isEmpty(formItem.getFormItemName())) {
            formItem.setFormItemName(componentId);
        }*/

        formItem.setDataLinkedComponents(getListDataLinkedComponentsByJsonSchemaData(component));
        return formItem;
    }

    private String getFieldName(String fieldId) {
        String result = "";
        if (fieldId.contains(".")) {
            String[] fieldSet = fieldId.split("\\.");
            for (int i = 0; i < fieldSet.length; i++) {
                AdField field = fieldRepository.getOne(fieldSet[i]);
                result = result + "#" + field.getName();
            }

            return result.substring(1);
        } else {
            AdField field = fieldRepository.getOne(fieldId);
            return field.getName();
        }
    }

    private OcCascader getCascaderInfo(JsonObject component) {
        OcCascader ocCascader = jsonConverter.fromJson(jsonConverter.toJson(getFormItemInfo(component)), OcCascader.class);
        return ocCascader;
    }

    private OcForm getFormInfo(JsonObject component) {
        OcForm form = new OcForm();
        String entityDefId = getComponentValueByKey(component, "entityDefId");

        if (entityDefId != null) {
            AdEntity entity = entityRepository.getOne(entityDefId);
            form.setEntityName(entity.getClassName());
        }

        if (component.has("searchForm")) {
            form.setSearchForm(component.getAsJsonPrimitive("searchForm").getAsBoolean());
            if (form.getSearchForm()) {
                ComponentContext.getComponentInfo().setSearchForm(true);
            } else {
                ComponentContext.getComponentInfo().setSearchForm(false);
            }
        } else {
            ComponentContext.getComponentInfo().setSearchForm(false);
        }

        if (component.has("formItemLabelWidthAdaptation")) {
            form.setFormItemLabelWidthAdaptation(component.getAsJsonPrimitive("formItemLabelWidthAdaptation").getAsBoolean());
        }

        return form;
    }

    private AdWindow getAdWindow(String componentName, String mode) {
        List<AdWindow> adWindows = windowRepository.findByComponentNameAndMode(componentName, mode);

        if (Consts.PREVIEW_MODE.equals(mode) && adWindows.size() == 0) {
            List<AdWindow> adWindowsTemp = windowRepository.findByComponentNameAndMode(componentName, Consts.WORKING_MODE);
            if (adWindowsTemp.size() > 0) {
                adWindows = adWindowsTemp;
            }
        }

        if (adWindows.size() == 0) {
            throw new IllegalStateException(componentName + "未找到!");
        }
        if (adWindows.size() > 1) {
            throw new IllegalStateException(componentName + "找到多个!");
        }
        return adWindows.get(0);
    }

//    public List<OcTable> getTableColumns(String componentName,String componentId,String mode){
//        AdWindow adWindow = getAdWindow(componentName, mode);
//        getComponent(adWindow.getUiSchema().get("UIData"));
//        JsonElement uiData = adWindow.getUiSchema().get("UIData");
//        JsonObject uiDataAsJsonObject = uiData.getAsJsonObject();
//        LinkedList<JsonObject> stack = new LinkedList<>();
//        do{
//            String componentType = uiDataAsJsonObject.get("componentType").getAsString();
//            String tempComponentId = uiDataAsJsonObject.get("componentId").getAsString();
//            if (StrUtil.equalsAny(componentType, "Table","TableForm") && StrUtil.equals(componentId,tempComponentId)) {
//
//            }

    /// /            StringUtils.equalsAny(getComponentType(cmp), "TableForm", "Table")
//            if(){}
//            if(uiDataAsJsonObject.has("componentChildren")){}
//        }while ()
//
//    }
    private String getChildrenColumnName(JsonObject component) {
        String childrenColumnName = getComponentValueByKey(component, "childrenColumnName");
        if (StringUtils.isNotBlank(childrenColumnName)) {
            return getFieldName(childrenColumnName);
        }

        return null;
    }

    private OcTable getTableInfo(JsonObject component) {
        OcTable table = new OcTable();
        String entityDefId = getComponentValueByKey(component, "entityDefId");
        String jsonPath = getComponentValueByKey(component, JSON_PATH_NAME);

        if (entityDefId != null) {
            AdEntity entity = entityRepository.getOne(entityDefId);
            table.setEntityName(entity.getClassName());
            if (StringUtils.isEmpty(jsonPath)) {
                table.setJsonPathName(InflectorUtil.getInstance().pluralize(entity.getName()));
            }
        }

        table.setDefaultSort(getTableDefaultSortInfoList(component));
        String fieldId = getComponentValueByKey(component, FIELD_ID);
        if (StringUtils.isNotEmpty(fieldId)) {
            AdField field = getField(fieldId);
            table.setFormItemName(getFieldName(fieldId));
            if (field.getRefEntity() != null) {
                table.setEntityName(field.getRefEntity().getClassName());
            }

            if (StringUtils.isNotEmpty(jsonPath)) {
                table.setFormItemName(replaceDotToPound(jsonPath));
            } else if (StringUtils.isNotEmpty(table.getFormItemName())) {
                table.setJsonPathName(replacePoundToDot(table.getFormItemName()));
            }
        }

        AdWindow refWindow = getWindowComponentName(component, "expandRowRenderRef");
        if (Objects.nonNull(refWindow)) {
            table.setExpandRowRenderRef(refWindow.getComponentName());
            table.setSubTableWindowMetaData(getRefWindowMetadata(refWindow));
        }

        table.setChildrenColumnName(getChildrenColumnName(component));
        return table;
    }

    private OCSearchInput getSearchInput(JsonObject component) {
        OCSearchInput searchInput = new OCSearchInput();
        String fieldId = getComponentValueByKey(component, FIELD_ID);
        if (StringUtils.isNotEmpty(fieldId)) {
            searchInput.setFieldId(fieldId);
            searchInput.setFormItemName(getFieldName(fieldId));
        }

        AdWindow refWindow = getWindowComponentName(component, "expandPopoverRenderRef");
        if (Objects.nonNull(refWindow)) {
            searchInput.setExpandPopoverRenderRef(refWindow.getComponentName());
            searchInput.getComponentChildren().add(getRefComponent(refWindow));
        }
        return searchInput;
    }

    private OcRefComponent getRefComponent(AdWindow refWindow) {
        OcRefComponent refComponent = new OcRefComponent();
        refComponent.setRefComponentName(refWindow.getComponentName());
        refComponent.setComponentName(refWindow.getComponentName());
        refComponent.setComponentType("Ref");
        refComponent.setVisible(refWindow.getVisible());
        if (StringUtils.isNotEmpty(ComponentContext.getComponentInfo().getMode())) {
            refComponent.setRefComponent(getRefWindowMetadata(refWindow));
        }
        return refComponent;
    }

    private OCColorPicker getColorPicker(JsonObject component) {
        OCColorPicker colorPicker = new OCColorPicker();
        String fieldId = getComponentValueByKey(component, FIELD_ID);
        if (StringUtils.isNotEmpty(fieldId)) {
            colorPicker.setFieldId(fieldId);
            colorPicker.setFormItemName(getFieldName(fieldId));
        }
        return colorPicker;
    }

    private List getTableDefaultSortInfoList(JsonObject component) {
        List<Map<String, String>> list = new ArrayList();
        if (!component.has("defaultSort")) {
            return list;
        }
        component.get("defaultSort").getAsJsonArray().forEach(each -> {
            Optional<Map<String, String>> fieldDataIndexAndSorter = tryFindFieldDataIndexAndSorter(each);
            fieldDataIndexAndSorter.ifPresent(list::add);
        });
        return list;
    }

    private Optional<Map<String, String>> tryFindFieldDataIndexAndSorter(JsonElement each) {
        Map<String, String> map = null;
        String fieldId = each.getAsJsonObject().get("dataIndex").getAsString();
        if (fieldRepository.existsById(fieldId)) {
            map = new HashMap<>();

            map.put("dataIndex", fieldRepository.getOne(each.getAsJsonObject().get("dataIndex").getAsString()).getName());
            map.put("sorter", each.getAsJsonObject().get("sorter").getAsString());
        }

        return Optional.ofNullable(map);
    }

    private OcFormItem getFormItemInfo(JsonObject jsonObject, AdField adField) {
        OcFormItem formItem = new OcFormItem();
        if (jsonObject != null) {
            formItem.setId("");
            formItem.setLabel(getComponentValueByKey(jsonObject, "label"));
            formItem.setLabelCol(getColSpanInfo(jsonObject.getAsJsonObject("labelCol")));
            formItem.setWrapperCol(getColSpanInfo(jsonObject.getAsJsonObject("wrapperCol")));
            if (adField != null && BooleanUtils.isNotTrue(ComponentContext.getComponentInfo().getSearchForm())) {
                formItem.setRules(getRuleListByElement(adField.getJsonSchemaData(), "rules"));
            }
            formItem.setLabelExtra(getLabelExtraInfos(jsonObject.getAsJsonArray(UIConsts.LABEL_EXTRA)));
        }
        return formItem;
    }

    private OcMFormItem getMFormItemInfo(JsonObject jsonObject, AdField adField) {
        OcMFormItem ocMFormItem = new OcMFormItem();
        if (jsonObject != null) {

            ocMFormItem.setLabel(getComponentValueByKey(jsonObject, "label"));
            ocMFormItem.setLabelCol(getColSpanInfo(jsonObject.getAsJsonObject("labelCol")));
            ocMFormItem.setWrapperCol(getColSpanInfo(jsonObject.getAsJsonObject("wrapperCol")));
            boolean isTrue = adField != null && ComponentContext.getComponentInfo() != null && ComponentContext.getComponentInfo().getSearchForm() != null && !ComponentContext.getComponentInfo().getSearchForm();
            if (isTrue) {
                ocMFormItem.setRules(getRuleListByElement(adField.getJsonSchemaData(), "rules"));
            }
        }
        return ocMFormItem;
    }

    private ColSpan getColSpanInfo(JsonObject jsonObject) {
        if (jsonObject == null) {
            return null;
        }

        ColSpan colSpan = new ColSpan();
        colSpan.setSpan(jsonObject.has("span") ? jsonObject.getAsJsonPrimitive("span").getAsInt() : 8);
        return colSpan;
    }

    private List<LabelExtra> getLabelExtraInfos(JsonArray jsonArray) {
        if (jsonArray == null) {
            return null;
        }

        List<LabelExtra> labelExtras = new ArrayList<>();
        jsonArray.forEach(iter -> {
            labelExtras.add(jsonConverter.fromJson(jsonConverter.toJson(iter), LabelExtra.class));
        });

        return labelExtras;
    }

    private List<Rule> getRuleListByElement(JsonObject jsonObject, String element) {
        List<Rule> ruleList = new ArrayList<>();
        if (jsonObject == null || StringUtils.isEmpty(element)) {
            return ruleList;
        }

        JsonElement jsonElement = jsonObject.get(element);
        if (Objects.isNull(jsonElement)) return ruleList;
        JsonArray asJsonArray = jsonElement.getAsJsonArray();
        asJsonArray.forEach(iter -> {
            Rule rule = jsonConverter.fromJson(jsonConverter.toJson(iter), Rule.class);
            ruleList.add(rule);
        });
        return ruleList;
    }

    private OcList getListInfo(JsonObject component) {
        OcList list = new OcList();
        //list.getActions().addAll(handleOperation(component.get("contextMenu").getAsJsonArray(), "button"));
        String winId = getComponentValueByKey(component, "windowId");//component.getJsonSchemaData().get("windowId").getAsString();
        if (StringUtils.isNotEmpty(winId)) {
            Optional<AdWindow> optional = windowRepository.findById(winId);
            AdWindow adWindow = optional.get();
            list.setRefComponentName(adWindow.getComponentName());
        }
        return list;
    }

    private OcDropdown getDropdownInfo(JsonObject component) {
        OcDropdown dropdown = new OcDropdown();
        dropdown.getContextMenu().addAll(handleOperation(component.get("contextMenu").getAsJsonArray(), "Menu"));
        return dropdown;
    }

    private OcSteps getStepsInfo(JsonObject component) {
        OcSteps steps = new OcSteps();
        return steps;
    }

    private OcCalendar getCalendarInfo(JsonObject component) {
        OcCalendar calendar = new OcCalendar();
        String entityDefId = getComponentValueByKey(component, "entityDefId");

        if (entityDefId != null) {
            AdEntity entity = entityRepository.getOne(entityDefId);
            calendar.setEntityName(entity.getClassName());
        }

        calendar.setEventTitle(getFieldName(component, "eventTitle"));
        calendar.setStartDate(getFieldName(component, "startDate"));
        calendar.setEndDate(getFieldName(component, "endDate"));

        AdWindow refWindow = getWindowComponentName(component, "windowId");
        if (Objects.nonNull(refWindow)) {
            calendar.setCellRefWindowName(refWindow.getComponentName());
        }

        calendar.setCellDateFieldName(getFieldName(component, "cellDateFieldName"));
        calendar.setShowView(getJsonObjectStringListResult(component, "showView"));

        calendar.setCellDateFieldName(getFieldName(component, "cellDateFieldName"));
        calendar.setShowView(getJsonObjectStringListResult(component, "showView"));
        if (component.has("windowId")) {
            calendar.setCellRefWindowName(windowRepository.getOne(component.get("windowId").getAsString()).getComponentName());
        }
        AdField field = getAdFieldBy(component);
        setFieldByAuth(calendar, field);
        calendar.getContextMenu().addAll(handleOperation(component.get("contextMenu").getAsJsonArray(), "menu"));
        return calendar;
    }

    private List<String> getJsonObjectStringListResult(JsonObject jsonObject, String key) {
        String result = getComponentValueByKey(jsonObject, key);
        if (StringUtils.isNotEmpty(result)) {
            return Arrays.asList(result.split(","));
        }
        return null;
    }

    private OcTree getTreeInfo(JsonObject component) {
        OcTree tree = new OcTree();
        String entityDefId = getComponentValueByKey(component, "entityDefId");

        if (entityDefId != null) {
            AdEntity entity = entityRepository.getOne(entityDefId);
            tree.setEntityName(entity.getClassName());
        }

        tree.setKeyColumnName(getFieldName(component, "keyColumnName"));
        tree.setTitleColumnName(getFieldName(component, "titleColumnName"));
        tree.setChildrenColumnName(getFieldName(component, "childrenColumnName"));

        tree.getContextMenu().addAll((List) handleOperation(component.get("contextMenu").getAsJsonArray(), "menu"));
        tree.getToolbar().addAll((List) handleOperation(component.get("toolbar").getAsJsonArray(), "Button"));
        return tree;
    }

    private JsonPrimitive getFieldNameAsJsonPrimitive(JsonObject jsonObject, String key) {
        if (jsonObject.has(key)) {
            return jsonObject.getAsJsonPrimitive(key);
        }
        return null;
    }

    //    private List<> getFieldNameAsJsonArray(JsonObject jsonObject, String key) {
    //        if (jsonObject.has(key)) {
    //            return jsonObject.getAsJsonPrimitive(key).getAsJsonArray();
    //        }
    //        return null;
    //    }
    private Boolean getFieldNameAsBoolean(JsonObject jsonObject, String key) {
        if (jsonObject.has(key)) {
            return jsonObject.getAsJsonPrimitive(key).getAsBoolean();
        }
        return null;
    }

    private Integer getFieldNameAsInt(JsonObject jsonObject, String key) {
        if (jsonObject.has(key)) {
            return jsonObject.getAsJsonPrimitive(key).getAsInt();
        }
        return null;
    }

    private String getFieldName(JsonObject jsonObject, String key) {
        String fieldId = getComponentValueByKey(jsonObject, key);
        if (StringUtils.isNotEmpty(fieldId)) {
            AdField field = fieldRepository.getOne(fieldId);
            if (field != null) {
                return field.getName();
            }
        }

        return null;
    }

    public AdWindow getWindowComponentName(JsonObject jsonObject, String key) {
        String windowId = getComponentValueByKey(jsonObject, key);
        if (windowId != null) {
            AdWindow adWindow = windowRepository.getOne(windowId);
            if (adWindow != null) {
                //return adWindow.getComponentName();
                return adWindow;
            }
        }

        return null;
    }

    private OcSubForm getSubFormInfo(JsonObject component) {
        OcSubForm form = new OcSubForm();
        //        if (component.getJsonSchemaData() != null && component.getJsonSchemaData().has("windowId")) {
        String winId = getComponentValueByKey(component, "windowId");//component.getJsonSchemaData().get("windowId").getAsString();
        Optional<AdWindow> optional = windowRepository.findById(winId);
        AdWindow adWindow = optional.get();
        form.setRefComponentName(adWindow.getComponentName());

        String parentWindowId = ComponentContext.getComponentInfo().getWindowId();
        OcWindow ocWindow = this.getWindowMetadata(adWindow.getComponentName(), ComponentContext.getComponentInfo().getMode());
        Map map = metaUIService.handleComponent(ocWindow, ComponentContext.getComponentInfo().getMode());
        form.setRefComponent(map);
        ComponentContext.getComponentInfo().setWindowId(parentWindowId);

        form.setIcon("");
        String fieldId = getComponentValueByKey(component, FIELD_ID);
        if (StringUtils.isNotEmpty(fieldId)) {
            AdField adField = getField(fieldId);

            form.setFormItemName(getFieldName(fieldId));
            form.setLabel(adWindow.getTitle());
            if (adField.getCollection()) {
                form.setRelation("OneToMany");
            } else {
                form.setRelation("OneToOne");
            }
        }

        String jsonPath = getComponentValueByKey(component, JSON_PATH_NAME);
        if (StringUtils.isNotEmpty(jsonPath)) {
            form.setFormItemName(replaceDotToPound(jsonPath));
        } else {
            form.setJsonPathName(replaceDotToPound(form.getFormItemName()));
        }
        return form;
    }

    private AdField getField(String fieldId) {
        fieldId = StrUtil.contains(fieldId,StrUtil.DOT) ? StrUtil.subAfter(fieldId, StrUtil.DOT, true) : fieldId;
        return fieldRepository.getOne(fieldId);
    }

    /**
     * radio组件initOption为null
     *
     * @param component
     * @return
     */
    private OcSelect getSelectInfo(JsonObject component) {
        OcSelect select = new OcSelect();
        String componentId = getComponentValueByKey(component, COMPONENT_ID);
        String fieldId = getComponentValueByKey(component, FIELD_ID);
        String initOptionValue = getComponentValueByKey(component, "initOption");
        Boolean initOption = StringUtils.isEmpty(initOptionValue) ? null : Boolean.valueOf(getComponentValueByKey(component, "initOption"));

        select.setValueChangeFrom(getValueChangeFormByJsonSchemaData(component));
        select.setDataLinkedComponents(getListDataLinkedComponentsByJsonSchemaData(component));

        if (fieldId != null) {
            AdField field = getField(fieldId);
            if (field != null) {
                select.setFormItemName(getFieldName(fieldId));
                select.setAdditionalProperties(getFormItemInfo(component.getAsJsonObject("additionalProperties"), field));
                setFieldByAuth(select, field);
                if (BooleanUtils.isNotFalse(initOption)) {
                    if (field.getReference() != null) {
                        List<Map<String, Object>> refList = configurationService.getValueOptions(field.getReference());
                        refList.stream().forEach(iter -> {
                            select.getOptions().add(metaUIService.getValueOptions(iter));
                        });
                        if (field.getCollection() != null && field.getCollection()) {
                            select.setMode("multiple");
                        }

                        select.setReferenceId(field.getReference().getId());
                        /*if (CollectionUtils.isNotEmpty(field.getReference().getRefItems())) {
                            select.setRefreshMode("remote");
                        }*/
                    } else if (field.getClassType().toLowerCase().equals("boolean")) { //bool值默认按是否返回Options
                        OcValueOptions ocValueOptions = new OcValueOptions();
                        ocValueOptions.setLabel("是");
                        ocValueOptions.setValue(true);

                        OcValueOptions ocValueOptions1 = new OcValueOptions();
                        ocValueOptions1.setLabel("否");
                        ocValueOptions1.setValue(false);

                        select.getOptions().add(ocValueOptions);
                        select.getOptions().add(ocValueOptions1);
                    }
                }
            }
        }

        String jsonPath = getComponentValueByKey(component, JSON_PATH_NAME);
        if (StringUtils.isNotEmpty(jsonPath)) {
            select.setFormItemName(replaceDotToPound(jsonPath));
        } else {
            select.setJsonPathName(replaceDotToPound(select.getFormItemName()));
        }

        /*if (StringUtils.isEmpty(select.getFormItemName())) {
            select.setFormItemName(componentId);
        }*/

        if (component.has("windowId")) {
            String winId = getComponentValueByKey(component, "windowId");//component.getJsonSchemaData().get("windowId").getAsString();
            Optional<AdWindow> optional = windowRepository.findById(winId);
            AdWindow adWindow = optional.get();
            select.setRefComponentName(adWindow.getComponentName());
        }

        if (CollectionUtils.isEmpty(select.getOptions())) {
            select.setOptions(null);
        }
        return select;
    }


    private List getValueChangeFormByJsonSchemaData(JsonObject jsonSchemaData) {
        JsonArray valueChangeForm = getJsonSchemaDataJsonArrayResult("valueChangeFrom", jsonSchemaData);
        if (Objects.nonNull(valueChangeForm)) {
            List list = new ArrayList<>();
            valueChangeForm.forEach(each -> {
                JsonObject jsonObject = each.getAsJsonObject();
                String componentId = jsonObject.get("component").getAsString();
                Map map = new HashMap();
                map.put("ctxType", "cmp");
                map.put("ctxValue", actionService.getComponentName(componentId));
                Map result = new HashMap();
                result.put("component", map);
                result.put("relationFieldName", jsonObject.get("relationFieldName").getAsString());
                list.add(result);
            });
            return list;
        }
        return null;
    }

    private List<String> getListDataLinkedComponentsByJsonSchemaData(JsonObject jsonSchemaData) {
        String dataLinkedComponents = getComponentValueByKey(jsonSchemaData, "dataLinkedComponents");
        if (!StringUtils.isEmpty(dataLinkedComponents)) {
            return Arrays.asList(dataLinkedComponents.split(","));
        }
        return null;
    }

    private AdField getAdFieldBy(JsonObject component) {
        String fieldId = getComponentValueByKey(component, FIELD_ID);

        if (fieldId != null && fieldRepository.existsById(fieldId)) {
            AdField field = fieldRepository.getOne(fieldId);
            return field;
        }
        return null;
    }

    private OcColumn getOperationColumn(JsonElement jsonElement) {
        OcColumn column = new OcColumn();
        column.setDataIndex("Edit");
        column.setComponentType("Operation");
        column.setTitle("操作");
        column.getComponentChildren().addAll(handleOperation(jsonElement, "Button"));
        return column;
    }

    private OcMColumn getMOperationColumn(JsonArray jsonArray) {
        OcMColumn column = new OcMColumn();
        column.setComponentType("Operation");
        column.getComponentChildren().addAll(handleMOperation(jsonArray, "Button"));
        return column;
    }

    private OcColumn getOperationColumn(JsonArray jsonArray, Boolean getEvents) {

        return getGenericColumn(jsonArray, getEvents, "Operation");
    }

    private OcColumn getToolbarColumn(JsonElement jsonElement, Boolean getEvents) {

        return getGenericColumn(jsonElement, getEvents, "Toolbar");
    }

    private OcColumn getGenericColumn(JsonElement jsonElement, Boolean getEvents, String columnType) {
        if (getEvents) {
            return getOperationColumn(jsonElement);
        }
        OcColumn column = new OcColumn();
        column.setDataIndex("Edit");
        column.setComponentType(columnType);
        column.setTitle("操作");
        column.getComponentChildren().addAll(handleOperation(jsonElement, "Button", false));
        return column;
    }

    private List<OcComponent> handleMOperation(JsonArray jsonArray, String componentType) {
        List<OcComponent> componentList = new ArrayList<>();
        jsonArray.forEach(iter -> {
            if (iter.isJsonObject()) {
                OcComponent ocComponent = new OcComponent();
                ocComponent.setComponentId(getComponentValueByKey(iter.getAsJsonObject(), "componentId"));
                ocComponent.setComponentType(componentType);
                ocComponent.setComponentName(getComponentValueByKey(iter.getAsJsonObject(), "componentName"));
                ocComponent.setComponentInfo(subJsonObject(iter.getAsJsonObject()));
                componentList.add(ocComponent);
            }
        });

        return componentList;
    }

//    private List<OcComponent> handleOperation(JsonArray jsonArray, String componentType) {
//        List<OcComponent> componentList = new ArrayList<>();
//        jsonArray.forEach(iter -> {
//            if (iter.isJsonObject()) {
//                OcComponent ocComponent = new OcComponent();
//                ocComponent.setComponentId(getComponentValueByKey(iter.getAsJsonObject(), "componentId"));
//                ocComponent.setComponentType(componentType);
//                ocComponent.setType(getComponentValueByKey(iter.getAsJsonObject(), "type"));
//                ocComponent.setComponentName(getComponentValueByKey(iter.getAsJsonObject(), "componentName"));
//                ocComponent.setEvents(addActionUrl(ocComponent.getComponentId(), iter.getAsJsonObject().get("events")));
//                ocComponent.setComponentInfo(subJsonObject(iter.getAsJsonObject()));
//                if (iter.getAsJsonObject().has("title")) {
//                    ocComponent.setTitle(getComponentValueByKey(iter.getAsJsonObject(), "title"));
//                } else {
//                    ocComponent.setTitle(getComponentValueByKey(iter.getAsJsonObject(), "name"));
//                }
//
//                ocComponent.setIcon(getComponentValueByKey(iter.getAsJsonObject(), "icon"));
//                ocComponent.setVisible(true);
//                componentList.add(ocComponent);
//            }
//        });
//
//        return componentList;
//    }

    private List<OcComponent> handleOperation(JsonElement jsonElement, String componentType) {

        if (jsonElement instanceof JsonObject) {
            return ListUtil.toList(makeOcComponentByJsonObject((JsonObject) jsonElement, componentType));
        }

        if (jsonElement instanceof JsonArray) {
            return ListUtil.toList(((JsonArray) jsonElement).iterator())
                    .stream()
                    .filter(JsonElement::isJsonObject)
                    .map(x -> makeOcComponentByJsonObject((JsonObject) x, componentType))
                    .collect(Collectors.toList());
        }

        return CollUtil.list(false);
    }

    private OcComponent makeOcComponentByJsonObject(JsonObject jsonObject, String componentType) {

        OcComponent ocComponent = new OcComponent();
        ocComponent.setComponentId(getComponentValueByKey(jsonObject, "componentId"));
        ocComponent.setComponentType(componentType);
        ocComponent.setType(getComponentValueByKey(jsonObject, "type"));
        ocComponent.setComponentName(getComponentValueByKey(jsonObject, "componentName"));
        ocComponent.setEvents(addActionUrl(ocComponent.getComponentId(), jsonObject.get("events")));
        ocComponent.setComponentInfo(subJsonObject(jsonObject));
        if (jsonObject.has("title")) {
            ocComponent.setTitle(getComponentValueByKey(jsonObject, "title"));
        } else {
            ocComponent.setTitle(getComponentValueByKey(jsonObject, "name"));
        }

        ocComponent.setIcon(getComponentValueByKey(jsonObject, "icon"));
        ocComponent.setVisible(true);

        return ocComponent;
    }

    private List<OcComponent> handleOperation(JsonElement jsonElement, String componentType, Boolean getEvents) {
        if (getEvents) {
            return handleOperation(jsonElement, componentType);
        }

        if (jsonElement instanceof JsonObject) {
            return ListUtil.toList(makeSimpleOcComponentByJsonObject((JsonObject) jsonElement, componentType));
        }

        if (jsonElement instanceof JsonArray) {
            return ListUtil.toList(((JsonArray) jsonElement).iterator())
                    .stream()
                    .filter(JsonElement::isJsonObject)
                    .map(x -> makeSimpleOcComponentByJsonObject((JsonObject) x, componentType))
                    .collect(Collectors.toList());
        }

        return CollUtil.list(false);
    }

    //TODO：方法命名有歧义，需要修正
    private OcComponent makeSimpleOcComponentByJsonObject(JsonObject jsonObject, String componentType) {
        OcComponent ocComponent = new OcComponent();
        ocComponent.setComponentId(getComponentValueByKey(jsonObject, "componentId"));
        ocComponent.setComponentType(componentType);
        ocComponent.setComponentName(getComponentValueByKey(jsonObject, "componentName"));
        ocComponent.setTitle(getComponentValueByKey(jsonObject, "name"));
        return ocComponent;
    }

    public static JsonObject subJsonObject(JsonObject jsonObject) {
        JsonObject out = new JsonObject();

        jsonObject.entrySet().stream().forEach(iter -> {
            if (!StringUtils.equalsAny(iter.getKey(), "componentId", "componentType", "componentChildren")) {
                out.add(iter.getKey(), iter.getValue());
            }
        });
        return out;
    }

    private OcCollapse getCollapseInfo(JsonObject component) {
        OcCollapse ocCollapse = new OcCollapse();
        ocCollapse.setBordered(getJsonSchemaDataBooleanResult("bordered", component));
        //ocCollapse.setDefaultActiveKey(getComponentValueByKey(component, "defaultActiveKey"));
        ocCollapse.setAccordion(getJsonSchemaDataBooleanResult("accordion", component));
        ocCollapse.setExpandIcon(getComponentValueByKey(component, "expandIcon"));
        return ocCollapse;
    }

    private OcCollapsePanel getCollapsePanelInfo(JsonObject component) {

        OcCollapsePanel ocCollapsePanel = new OcCollapsePanel();
        ocCollapsePanel.setKey(getComponentValueByKey(component, "key"));
        ocCollapsePanel.setTab(getComponentValueByKey(component, "tan"));
        ocCollapsePanel.setShowArrow(getJsonSchemaDataBooleanResult("showArrow", component));
        String windowId = getComponentValueByKey(component, "windowId");
        if (StringUtils.isNotEmpty(windowId)) {
            AdWindow adWindow = windowRepository.getOne(windowId);
            OcRefComponent ocRefComponent = getOcRefComponentInfo(adWindow, jsonConverter.fromJson(getComponentValueByKey(component, "jsonSchemaData"), JsonObject.class));
            if (Objects.nonNull(ocRefComponent)) {
                ocCollapsePanel.getComponentChildren().add(ocRefComponent);
            }
        }
        return ocCollapsePanel;
    }

    private OcChart getChartInfo(JsonObject component) {
        OcChart ocChart = new OcChart();
        String entityDefId = getComponentValueByKey(component, "entityDefId");

        if (entityDefId != null) {
            AdEntity entity = entityRepository.getOne(entityDefId);
            ocChart.setEntityName(entity.getClassName());
        }
        ocChart.setSeries(setFieldNameByType(component, "series"));
        ocChart.setClassify(setFieldNameByType(component, "classify"));
        return ocChart;
    }

    private JsonArray setFieldNameByType(JsonObject jsonObject, String type) {
        jsonObject.getAsJsonArray(type).forEach(
                iter -> {
                    if (iter.isJsonObject() && iter.getAsJsonObject().has(FIELD_ID)) {
                        AdField adField = fieldRepository.getOne(iter.getAsJsonObject().get("fieldId").getAsString());
                        iter.getAsJsonObject().addProperty("fieldName", adField.getName());
                    }
                }
        );
        return jsonObject.getAsJsonArray(type);
    }

    private OcRefComponent getOcRefComponentInfo(AdWindow adWindow, JsonObject jsonObject) {
        OcRefComponent ocRefComponent = new OcRefComponent();
        if (Objects.isNull(adWindow)) {
            return null;
        }
        ocRefComponent.setRefComponentName(adWindow.getComponentName());
        ocRefComponent.setComponentName(adWindow.getComponentName());
        ocRefComponent.setComponentType("Ref");
        return ocRefComponent;
    }

    private final String SPLIT_STR = "\\.";
    private final String STR = ".";
    //    private void getFormFieldSetByMap(List<OcComponent> ocComponents, Map<String,Object> fields) {
    //        String result = "";
    //        for (OcComponent component : ocComponents) {
    //            String fieldName = component.getFormItemName();
    //            if (component.getComponentType().equals("Form")) {
    //                if (component.getComponentChildren().size() > 0) {
    //                    if(fields==null){
    //                        fields = new HashMap<>();
    //                    }
    //                    getFormFieldSetByMap(component.getComponentChildren(), fields);
    //                    ((OcForm) component).setFieldSet(transFieldToTreeByMap(fields));
    //                }
    //            } else if (component.getComponentType().equals("SubForm")) {
    //
    //                Map mapInfo = jsonConverter.fromJson(component.getComponentInfo().toString(), Map.class);
    //                String winId = mapInfo.get("windowId").toString();
    //                Optional<AdWindow> optional = windowRepository.findById(winId);
    //                AdWindow adWindow = optional.get();
    //                String refComponentName = adWindow.getComponentName();
    //                OcWindow window = getWindowMetadata(refComponentName);
    //                String fieldId = mapInfo.get("fieldId").toString();
    //                AdField adField = fieldRepository.getOne(fieldId);
    //                Map map = new HashMap();
    //                getFormFieldSetByMap(window.getComponentChildren(), map);
    //                fields.put(adField.getName(),map);
    //            } else if (component.getComponentType().equals("TableForm")) {
    //                Map map = new HashMap();
    //                getFormFieldSetByMap(component.getComponentChildren(), map);
    //                fields.put(fieldName,map);
    //            } else if (component.getFormItemName() != null) {
    //
    //                if(fieldName.contains(SPLIT_STR)){
    //                    String[] arg = fieldName.split(SPLIT_STR);
    //                    setFieldSetByPoint(fields,arg);
    //                }else{
    //                    fields.put(fieldName,"1");
    //                }
    //            } else if (component.getComponentType().equals("Column")) {
    //                String key = ((OcColumn) component).getDataIndex();
    //                if(key.contains(SPLIT_STR)){
    //                    String[] arg = key.split(SPLIT_STR);
    //                    setFieldSetByPoint(fields,arg);
    //                }else{
    //                    fields.put(key,"1");
    //                }
    //            } else {
    //                getFormFieldSetByMap(component.getComponentChildren(), fields);
    //            }
    //        }
    //    }

    private String transFieldToTree(List<FieldDTO> fieldDTOS) {
        Map<String, Object> map = new HashMap<>();
        transFieldToTreeMap(fieldDTOS, map);
        return transFieldToTreeByMap(map);
    }

    private void transFieldToTreeMap(List<FieldDTO> fieldDTOS, Map<String, Object> map) {
        for (FieldDTO fieldDTO : fieldDTOS) {
            if (fieldDTO.getChildren().size() > 0) {
                Map mapChild = new HashMap();

                if (fieldDTO.getFieldName().contains(STR)) {

                    transFieldToTreeMap(fieldDTO.getChildren(), mapChild);
                    //fildName =a.b   name,age
                    String arg[] = fieldDTO.getFieldName().split(SPLIT_STR);
                    Map mapChildChild;
                    if (map.containsKey(arg[0]) && map.get(arg[0]) instanceof Map) {
                        mapChildChild = map;
                    } else {
                        mapChildChild = new HashMap();
                    }
                    setFieldSetByPoint(mapChildChild, fieldDTO.getFieldName().split(SPLIT_STR), mapChild);

                    map.put(arg[0], mapChildChild.get(arg[0]));

                } else {
                    transFieldToTreeMap(fieldDTO.getChildren(), mapChild);
                    map.put(fieldDTO.getFieldName(), mapChild);
                }
            } else {
                if (fieldDTO.getFieldName() != null) {
                    if (fieldDTO.getFieldName().contains(STR)) {
                        Map mapChild;
                        String arg[] = fieldDTO.getFieldName().split(SPLIT_STR);
                        if (map.containsKey(arg[0]) && map.get(arg[0]) instanceof Map) {
                            mapChild = map;
                        } else {
                            mapChild = new HashMap();
                        }
                        setFieldSetByPoint(mapChild, fieldDTO.getFieldName().split(SPLIT_STR));

                        map.put(arg[0], mapChild.get(arg[0]));
                    } else {
                        map.put(fieldDTO.getFieldName(), "1");
                    }
                }
            }

        }
    }

    private void setFieldSetByPoint(Map<String, Object> fields, String[] arg, Map<String, Object> mapChild) {
        if (arg.length > 0) {
            String[] argChild = new String[arg.length - 1];
            for (int i = 0; i < argChild.length; i++) {
                argChild[i] = arg[i + 1];
            }
            if (argChild.length > 0) {
                Object obj;
                if (fields.containsKey(arg[0]) && (fields.get(arg[0]) instanceof Map)) {
                    obj = fields.get(arg[0]);
                } else {
                    obj = new HashMap<>();
                }
                setFieldSetByPoint((Map<String, Object>) obj, argChild, mapChild);
                fields.put(arg[0], obj);
            } else {
                if (!fields.containsKey(arg[0])) {
                    if (mapChild == null) {
                        fields.put(arg[0], "1");
                    } else {
                        fields.put(arg[0], mapChild);
                    }

                }
            }

        }
    }

    private void setFieldSetByPoint(Map<String, Object> fields, String[] arg) {
        setFieldSetByPoint(fields, arg, null);
    }

    private String transFieldToTreeByMap(Map<String, Object> fieldDTOS) {
        StringBuilder result = new StringBuilder("{");

        Iterator iterator = fieldDTOS.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry entry = (Map.Entry) iterator.next();
            if (entry.getValue() instanceof Map) {
                result.append(entry.getKey() + transFieldToTreeByMap((Map) entry.getValue()) + ",");
            } else {
                result.append(entry.getKey() + ",");
            }
        }
        if (result.indexOf(",") > 0) {
            result.deleteCharAt(result.lastIndexOf(","));
        }
        result.append("}");
        return result.toString();
    }

    private void getFormFieldSet(List<OcComponent> ocComponents, List<FieldDTO> fields) {
        for (OcComponent component : ocComponents) {
            if (StringUtils.equalsAny(component.getComponentType(), "Form")) {
                if (component.getComponentChildren().size() > 0) {
                    getFormFieldSet(component.getComponentChildren(), fields);
                    ((OcForm) component).setFieldSet(transFieldToTree(fields));
                }
            } else if (component.getComponentType().equals("Table")) {
                if (component.getComponentChildren().size() > 0) {
                    fields = new ArrayList();
                    getFormFieldSet(component.getComponentChildren(), fields);
                    ((OcTable) component).setFieldSet(transFieldToTree(fields));
                }
            } else if (StringUtils.equalsAny(component.getComponentType(), "MForm")) {
                if (component.getComponentChildren().size() > 0) {
                    getFormFieldSet(component.getComponentChildren(), fields);
                    ((OcMForm) component).setFieldSet(transFieldToTree(fields));
                }
            } else if (component.getComponentType().equals("SubForm")) {
                Map mapInfo = jsonConverter.fromJson(component.getComponentInfo().toString(), Map.class);
                String winId = mapInfo.get("windowId").toString();
                Optional<AdWindow> optional = windowRepository.findById(winId);
                AdWindow adWindow = optional.get();
                String refComponentName = adWindow.getComponentName();
                OcWindow window = getWindowMetadata(refComponentName, Consts.WORKING_MODE);

                String fieldId = MapUtils.getString(mapInfo, FIELD_ID);
                if (StringUtils.isNotEmpty(fieldId)) {
                    AdField adField = getField(fieldId);
                    FieldDTO fieldDTO = new FieldDTO(getFieldName(fieldId));
                    fieldDTO.setFieldId(adField.getId());
                    fieldDTO.setFieldTitle(adField.getTitle());
                    fieldDTO.setFieldType(adField.getType());
                    getFormFieldSet(window.getComponentChildren(), fieldDTO.getChildren());
                    fields.add(fieldDTO);
                }
            } else if (StringUtils.equalsAny(component.getComponentType(), "TableForm", "MTable")) {
                FieldDTO fieldDTO = new FieldDTO(component.getFormItemName());
                getFormFieldSet(component.getComponentChildren(), fieldDTO.getChildren());
                fields.add(fieldDTO);
            } else if (component.getFormItemName() != null) {
                fields.add(new FieldDTO(component.getFormItemName()));
            } else if (StringUtils.equalsAny(component.getComponentType(), "Column", "MColumn")) {
                fields.add(new FieldDTO(((OcColumn) component).getDataIndex()));
            } else {
                getFormFieldSet(component.getComponentChildren(), fields);
            }
        }
    }


    //    private String transFieldToTree1(List<FieldDTO> fieldDTOS) {
    //        StringBuilder result = new StringBuilder("{");
    //        for (FieldDTO fieldDTO : fieldDTOS) {
    //            if (fieldDTO.getChildren().size() > 0) {
    //                result.append(fieldDTO.getFieldName() + transFieldToTree1(fieldDTO.getChildren()) + ",");
    //            } else {
    //                if(fieldDTO.getFieldName() != null) {
    //                    result.append(fieldDTO.getFieldName() + ",");
    //                }
    //            }
    //        }
    //        if(result.indexOf(",") > 0) {
    //            result.deleteCharAt(result.lastIndexOf(","));
    //        }
    //        result.append("}");
    //        return result.toString();
    //    }


    public void setMFieldByAuth(OcComponent component, AdField field) {
        if (component.getVisible() != null && !component.getVisible()) {
            return;
        }
        if (AuthorityContext.seeAllComponent()) {
            component.setVisible(true);
            return;
        }
        if (AppInfoUtil.isBaseApp()) {
            component.setVisible(true);
            return;
        }
        if (field == null || field.getEntity() == null) {
            return;
        }

        if (EntityBaseProperty.getDefaultReturnProperty().containsKey(field.getName())) {
            return;
        }

        Optional<RoleEntityDTO> roleEntityDTO = AuthorityContext.getAuthority().getRoleEntityDTOs().stream().filter(iter -> iter.entityDefId.equals(field.getEntity().getId())).findFirst();

        if (component.getVisible() != null && component.getVisible()) {
            component.setVisible(false);
        }
        Optional<RoleEntityFieldDTO> roleEntityFieldDTO = roleEntityDTO.get().fieldAuth.stream().filter(fieldDTO -> field.getId().equals(fieldDTO.fieldId)).findFirst();

        //        if (!roleEntityFieldDTO.isPresent() || StringUtils.isEmpty(roleEntityFieldDTO.get().auth)) {
        //            component.setVisible(false);
        //            component.setComponentHidden(true);
        //        }

        //        if (!roleEntityFieldDTO.isPresent() || roleEntityFieldDTO.get().auth.contains("visible")) {

        //实体不允许查看 直接返回。
        if (!roleEntityDTO.get().is_canAccess()) {
            component.setVisible(false);
            //            component.setComponentHidden(true);
            return;
        }
        // 实体允许查看，字段是否允许查看单独做判断
        if (roleEntityFieldDTO.isPresent() && roleEntityFieldDTO.get().is_canAccess()) {

            //        if (roleEntityFieldDTO.get().is_canAccess()) {
            component.setVisible(true);
            //            component.setComponentHidden(false);
            //可编辑的 必须是可见的
            if (roleEntityFieldDTO.get().is_canEdit()) {
                component.setDisabled(false);
                //                component.setComponentHidden(false);
            }
        } else {
            component.setVisible(false);
            //            component.setComponentHidden(true);
        }
    }

    public void setFieldByAuth(OcComponent component, AdField field) {

        if (AuthorityContext.seeAllComponent()) {
            component.setVisible(true);
            return;
        }
        if (AppInfoUtil.isBaseApp()) {
            component.setVisible(true);
            return;
        }

        if (!schemaAuthority) {
            component.setVisible(true);
            return;
        }

        if (field == null || field.getEntity() == null) {
            return;
        }

        //无需权限校验
        if (!BooleanUtils.isTrue(field.getEntity().getAuthority())) {
            component.setVisible(true);
            return;
        }

        if (EntityBaseProperty.getDefaultReturnProperty().containsKey(field.getName())) {
            return;
        }

        List<RoleEntityDTO> roleEntityDTOs = AuthorityContext.getAuthority().getRoleEntityDTOs().
                stream().filter(iter -> iter.entityDefId.equals(field.getEntity().getId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(roleEntityDTOs)) {
            if (StringUtils.equalsAny(component.getTitle(), "应用名称", "描述")) {
                component.setVisible(true);
            } else {
                component.setVisible(false);
            }
            return;
        }

        if (component.getVisible() != null && component.getVisible()) {
            component.setVisible(false);
        }
        //Optional<RoleEntityFieldDTO> roleEntityFieldDTO = roleEntityDTO.get().fieldAuth.stream().filter(fieldDTO -> field.getId().equals(fieldDTO.fieldId)).findFirst();

        //实体不允许查看 直接返回。
        /*if (!roleEntityDTO.get().is_canAccess()) {
            component.setVisible(false);
            return;
        }*/
        // 实体允许查看，字段是否允许查看单独做判断
        component.setVisible(false);
        component.setDisabled(false);

        roleEntityDTOs.stream().map(RoleEntityDTO::getFieldAuth).flatMap(Collection::stream)
                .filter(fieldDTO -> field.getId().equals(fieldDTO.fieldId))
                .forEach(
                        iter -> {
                            if (iter.is_canAccess()) {
                                component.setVisible(true);
                            }

                            if (iter.is_canEdit()) {
                                component.setDisabled(false);
                            }
                        }
                );
        /*if (roleEntityFieldDTO.isPresent() && roleEntityFieldDTO.get().is_canAccess()) {
            component.setVisible(true);
            //可编辑的 必须是可见的
            if (roleEntityFieldDTO.get().is_canEdit()) {
                component.setDisabled(false);
            }
        } else {
            component.setVisible(false);
        }*/
    }

    private int getJsonSchemaDataIntegerResult(String name, JsonObject jsonSchemaData) {
        if (jsonSchemaData.has(name)) {
            return jsonSchemaData.get(name).getAsInt();
        }
        return 0;
    }

    private Boolean getJsonSchemaDataBooleanResult(String name, JsonObject jsonSchemaData) {
        if (jsonSchemaData.has(name)) {
            return jsonSchemaData.get(name).getAsBoolean();
        }

        return null;
    }

    private JsonArray getJsonSchemaDataJsonArrayResult(String name, JsonObject jsonSchemaData) {
        if (jsonSchemaData.has(name)) {
            return jsonSchemaData.get(name).getAsJsonArray();
        }
        return null;
    }

    public String previewWindowMetaData(String client, String content) {
        metaUIService.saveUIMetadataToDB(content, null, Consts.PREVIEW_MODE);
        return jsonConverter.toJson(ResponseUtil.getResponseSuccess(""));
        /*JsonObject jsonObject = new JsonParser().parse(content).getAsJsonObject();
        JsonObject window = jsonObject.get("UIData").getAsJsonObject();
        ComponentContext.clearComponentInfo();
        ComponentContext.setComponentInfo(new ComponentInfo());
        ComponentContext.getComponentInfo().setMode(Consts.PREVIEW_MODE);

        if (client.equals(Consts.PC_MODE)) {
            OcWindow ocWindow = new OcWindow();
            ocWindow.setComponentChildren(getComponent(window));
            ocWindow.setStyles(window.get("styles").getAsString());
            ocWindow.setComponentName(window.get("componentName").getAsString());
            ocWindow.setComponentType(window.get("componentType").getAsString());
            ocWindow.setTitle(window.get("title").getAsString());
            return jsonConverter.toJson(metaUIService.handleComponent(ocWindow, Consts.PREVIEW_MODE));
        } else {
            OcMWindow ocMWindow = new OcMWindow();
            ocMWindow.setComponentChildren(getMComponent(window));
            ocMWindow.setStyles(window.get("styles").getAsString());
            ocMWindow.setComponentName(window.get("componentName").getAsString());
            ocMWindow.setComponentType(window.get("componentType").getAsString());
            ocMWindow.setTitle(window.get("title").getAsString());
            return jsonConverter.toJson(metaUIService.handleMComponent(ocMWindow, Consts.PREVIEW_MODE));
        }*/

    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void setWindowCache(AdWindow adWindow) {
        adWindow.setUiSchemaDataCache(true);
        windowRepository.save(adWindow);
    }

    public String handleWindowMetadataWithOption(Map map) {
        JsonObject jsonObject = (new JsonParser().parse(jsonConverter.toJson(map)).getAsJsonObject());
        handleFormItemWithOption(jsonObject);
        logger.error("handleWindowMetadataWithOption end");
        return jsonConverter.toJson(jsonObject);
    }

    public void handleFormItemWithOption(JsonElement jsonElement) {
        if (jsonElement instanceof JsonObject && jsonElement.getAsJsonObject().has("options")
                && jsonElement.getAsJsonObject().has(FIELD_ID) && jsonElement.getAsJsonObject().has("referenceId")) {

            //针对column 特殊处理
            String componentType = jsonElement.getAsJsonObject().getAsJsonPrimitive("componentType").getAsString();
            if (jsonElement.getAsJsonObject().getAsJsonArray("options").size() == 0 && "Column".equals(componentType)) {
                return;
            }

            //refreshMode = remote的作处理
            if (!jsonElement.getAsJsonObject().has("refreshMode") || !"remote".equals(jsonElement.getAsJsonObject().getAsJsonPrimitive("refreshMode").getAsString())) {
                return;
            }

            String fieldId = jsonElement.getAsJsonObject().get(FIELD_ID).getAsString();
            String referenceId = jsonElement.getAsJsonObject().get("referenceId").getAsString();
            setFormItemOptionWithReference(jsonElement.getAsJsonObject(), referenceId);
        } else if (jsonElement instanceof JsonArray) {
            jsonElement.getAsJsonArray().forEach(
                    iter -> {
                        handleFormItemWithOption(iter);
                    }
            );
        } else if (jsonElement instanceof JsonObject) {
            jsonElement.getAsJsonObject().entrySet().forEach(
                    iter -> {
                        handleFormItemWithOption(iter.getValue());
                    }
            );
        }
    }

    private void setFormItemOption(JsonObject jsonObject, String fieldId) {
        AdField field = getField(fieldId);
        if (Objects.nonNull(field.getReference())) {
            logger.error(field.getName() + " refresh option");
            jsonObject.add("options", new JsonParser().parse(jsonConverter.toJson(configurationService.getValueOptions(field.getReference()))));
        }
    }

    private void setFormItemOptionWithReference(JsonObject jsonObject, String referenceId) {
        AdReference reference = referenceRepository.getOne(referenceId);
        logger.error(reference.getName() + " refresh option");
        jsonObject.add("options", new JsonParser().parse(jsonConverter.toJson(configurationService.getValueOptions(reference))));
    }

    private String replaceDotToPound(String jsonPathName) {
        if (StringUtils.isEmpty(jsonPathName)) {
            return null;
        }
        return jsonPathName.replace(GlobalConstant.DOT_SIGN, GlobalConstant.POUND_SIGN);
    }

    private String replacePoundToDot(String fieldItemName) {
        if (StringUtils.isNotEmpty(fieldItemName)) {
            return fieldItemName.replace(GlobalConstant.POUND_SIGN, GlobalConstant.DOT_SIGN);
        }

        return null;
    }

    public void switchUIMetadataCache(boolean enable) {
        uiRepository.findByComponentType(Consts.GLOBAL_META_DATA_PC).stream().findFirst().ifPresent(adUI -> {
            JsonObject jsonObject = adUI.getTheme();
            if (ObjUtil.isNull(jsonObject) && !jsonObject.has(Consts.UI_BASIC_LAYOUT_THEME_ENABLE_UI_METADATA_CACHE)) {
                return;
            }
            jsonObject.addProperty(Consts.UI_BASIC_LAYOUT_THEME_ENABLE_UI_METADATA_CACHE, enable);
            uiRepository.save(adUI);
        });
    }

    public void switchRefreshWhenSwitchWinTab(boolean enable) {
        appConfigService.getById(AD_APP_CONFIG_ID).ifPresent(adAppConfig -> {
            JsonObject jsonSchemaData = ObjUtil.defaultIfNull(adAppConfig.getJsonSchemaData(), new JsonObject());
            jsonSchemaData.addProperty(Consts.AD_APP_CONFIG_JSON_SCHEMA_DATA_ENABLE_REFRESH_WHEN_SWITCH_WIN_TAB, enable);
            appConfigService.save(adAppConfig);
        });
    }
}
