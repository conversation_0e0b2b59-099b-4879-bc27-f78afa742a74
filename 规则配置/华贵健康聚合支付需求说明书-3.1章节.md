# 华贵健康聚合支付需求说明书 - 3.1 章节

## 文档背景

### 业务概述

本文档描述保险公司"健康聚合支付"系统的规则配置功能。健康聚合支付是保险公司为企业员工提供的综合福利保障计划中的核心支付服务，主要针对企业员工的住院、门诊、购药等方面的支出进行报销。

### 核心业务场景

**线下购药典型流程：**

1. 客户到合约药店选择药品
2. 客户通过保险公司手机 App 获取"支付二维码"（对应某外部支付平台）
3. 药店收银员扫码，后台系统进行划价，区分保险报销部分和客户自付部分
4. 客户支付自付费部分（现金/微信/支付宝等）
5. 保险报销部分由系统记账，定期由保险公司支付给外部支付平台，再由外部支付平台与药店分账

### 核心概念定义

| 术语                 | 定义                                                                                                      |
| -------------------- | --------------------------------------------------------------------------------------------------------- |
| **健康聚合支付系统** | 保险公司内部建设的聚合支付系统，对接多个外部支付平台，支持健康直付业务                                    |
| **本系统**           | 指健康聚合支付系统                                                                                        |
| **外部支付平台**     | 保险公司外部的支付平台，与本系统对接，配合完成健康直付业务。类似第三方/第四方支付平台，但需与多家药店签约 |
| **健康直付**         | 保险公司直接向药店或外部支付平台支付保险报销费用，客户只需支付自付部分的支付方式                          |
| **直付规则**         | 定义哪些情况下可以使用直付服务、如何计算报销金额、选择哪个支付平台等的业务规则集合                        |

### 系统架构关系

```
保险公司App ←→ 健康聚合支付系统 ←→ 外部支付平台 ←→ 药店
     ↓                ↓
保险核心系统    ←→  规则配置模块
```

### 规则配置的作用

规则配置是健康聚合支付系统的核心功能，通过配置不同类型的规则来：

1. **控制业务范围** - 定义哪些药品可以直付、哪些客户可以享受服务
2. **智能路由决策** - 在多个外部支付平台中自动选择最优平台
3. **费用计算标准** - 确定报销比例、起付线、封顶金额等
4. **资金结算方式** - 定义与外部平台的结算周期和规则

---

# 3.1 规则配置

健康管理系统完成健康直付规则配置，包括：直付品类规则、直付机构规则、医疗机构分配规则、结算规则。

健康管理系统在完成对应配置后，同步信息至核心系统。

## 3.1.1 直付品类规则

### 3.1.1.1 规则定义

直付品类规则用于定义哪些药品或医疗服务品类符合直付报销条件。它直接影响计费和报销金额的计算，是系统正确执行直付业务的基础。

直付品类规则和计费规则相结合实现了医保直付交易的精准识别与计算，确保了合规品类能正确享受保险直付并准确计算报销金额。本节描述的是直付品类规则，关于计费规则，参见章节：3.1.4 计费规则

### 3.1.1.2 规则说明

**功能用途**

- 确定哪些品类的药品可以使用直付服务
- 控制企业福利计划中可直付的药品范围

**配置内容**

- 药品分类定义：支持医保目录（甲类/乙类/丙类）/商保目录
- 品类准入控制：明确哪些药品类别可使用直付服务
- 特殊品类管理：如保健品、营养品、医疗器械等非常规药品的处理规则
- 药品编码匹配：支持国家药品编码（如：国家药品监督管理局标准码（NMPA））等多种标准

**规则层级**

- 系统级规则：适用于全部客户的基础规则
- 企业级规则：特定企业客户的定制品类规则
- 员工群体级规则：针对特定员工群体的差异化规则

**规则优先级**
员工群体级 > 企业级 > 系统级

**药品信息对接规范**

**数据来源与对接方式**

- **医保药品信息**：严格遵循国家药品监督管理局的药品分类及编码规则，支持通过接口实时获取或批量导入
- **商业保险药品信息**：本系统提供专用接口，供外部支付平台获取定制化药品目录及对应规则
- **数据一致性要求**：外部支付平台需采用与本系统一致的药品编码标准（如医保编码、商保扩展编码），确保数据准确匹配

**合规性说明**
所有药品数据交互均需符合国家医疗保障信息标准及商业健康保险相关规范，确保数据安全与业务合规

### 3.1.1.3 规则样例

**样例一：基础药品分类规则**

规则名称：基础药品品类直付规则

规则内容：

- **甲类药品**：
  - 定义：医保目录甲类药品，基本医疗所必须
  - 直付资格：允许直付
- **乙类药品**：
  - 定义：医保目录乙类药品，可替代性药品
  - 直付资格：允许直付
- **丙类药品**：
  - 定义：医保目录丙类药品，非基本医疗用药
  - 直付资格：允许直付
- **保健品/营养品**：
  - 定义：保健品、膳食补充剂等
  - 直付资格：不允许直付

**样例二：企业定制药品品类规则**

规则名称：华瑞科技企业定制药品品类规则

规则内容：

- **标准药品类别**：遵循基础药品分类规则
- **企业福利扩展药品**：
  - 抗过敏药物：氯雷他定、西替利嗪等
  - 胃肠道用药：奥美拉唑、雷贝拉唑等
  - 眼科用药：人工泪液、抗菌眼药水等
  - 直付资格：所有扩展药品允许直付
  - 适用人群：仅限华瑞科技企业员工

### 3.1.1.4 规则配置 UI

**菜单路径：** 系统设置 > 规则管理

**直付品类规则配置界面**

![直付品类规则](图3-1)

**界面整体布局：**

界面采用左右分栏设计，左侧为规则列表区域，右侧为详细配置区域，提供清晰的操作流程和直观的配置体验。

**左侧规则列表区域：**

1. **规则搜索与筛选**

   - 搜索框：支持按规则名称、描述关键词快速搜索
   - 状态筛选：可按启用/禁用状态筛选规则
   - 层级筛选：可按系统级/企业级/员工群体级筛选
   - 创建时间筛选：支持按创建时间范围筛选规则

2. **规则列表展示**

   - 规则卡片：每个规则以卡片形式展示，包含规则名称、状态标识、适用范围
   - 状态指示器：通过颜色标识规则状态（绿色-启用，灰色-禁用，橙色-待审核）
   - 快速操作：每个卡片提供启用/禁用、编辑、删除等快速操作按钮
   - 分页控制：支持分页浏览，每页显示 10-20 条规则

3. **批量操作工具栏**
   - 新建规则：点击创建新的直付品类规则
   - 批量启用/禁用：支持选中多个规则进行批量状态变更
   - 导入/导出：支持 Excel 格式的规则批量导入导出
   - 规则模板：提供常用的规则配置模板

**右侧详细配置区域：**

1. **基本信息配置面板**

   - 规则名称：必填字段，支持中英文，最大长度 50 字符
   - 规则编码：系统自动生成或手动输入，用于 API 调用标识
   - 规则描述：多行文本框，详细描述规则用途和适用场景，最大 500 字符
   - 创建人信息：显示规则创建者和创建时间
   - 最后修改信息：显示最后修改人和修改时间

2. **适用范围配置面板**

   - 规则层级：单选按钮组（系统级/企业级/员工群体级）
   - 企业选择器：当选择企业级时显示，支持多选企业
   - 员工群体选择器：当选择员工群体级时显示，支持按部门、职级等维度选择
   - 生效时间：日期时间选择器，设置规则生效的起始时间
   - 失效时间：可选字段，设置规则自动失效时间

3. **药品分类配置面板**

   - 分类标准选择：单选按钮（医保目录/商保目录/自定义目录）
   - 医保目录配置：
     - 甲类药品：勾选框 + 详细说明文本
     - 乙类药品：勾选框 + 详细说明文本
     - 丙类药品：勾选框 + 详细说明文本
   - 商保目录配置：
     - 基础保障药品：勾选框 + 药品清单链接
     - 扩展保障药品：勾选框 + 药品清单链接
     - 特殊保障药品：勾选框 + 药品清单链接

4. **品类控制详细配置**

   - 药品类别树形结构：
     - 一级分类：按治疗领域分类（心血管、消化系统、呼吸系统等）
     - 二级分类：按药理作用分类
     - 三级分类：具体药品名称
   - 批量操作：
     - 全选/全不选：快速选择所有品类
     - 按分类批量设置：支持按一级或二级分类批量设置直付权限
   - 例外管理：
     - 黑名单：明确禁止直付的特定药品清单
     - 白名单：强制允许直付的特定药品清单

5. **高级配置选项**

   - 优先级设置：数字输入框，设置规则执行优先级（1-100）
   - 冲突处理策略：下拉选择（覆盖/合并/拒绝）
   - 审核流程：勾选框，是否需要审核后生效
   - 日志记录：勾选框，是否记录详细的规则执行日志

6. **预览与验证面板**
   - 配置预览：实时显示当前配置的规则摘要
   - 影响范围预估：显示规则影响的用户数量和药品数量
   - 配置验证：实时检查配置的完整性和合规性
   - 冲突检测：检测与现有规则的潜在冲突

**操作按钮区域：**

1. **主要操作按钮**

   - 保存：保存当前配置，位于右下角，蓝色主按钮
   - 保存并启用：保存配置并立即启用规则
   - 取消：放弃当前修改，返回规则列表

2. **辅助操作按钮**
   - 预览效果：在新窗口中预览规则配置效果
   - 测试验证：使用测试数据验证规则配置的正确性
   - 复制规则：基于当前规则创建副本
   - 导出配置：将当前规则配置导出为 JSON 或 Excel 格式

**交互体验设计：**

1. **响应式设计**

   - 支持不同屏幕尺寸的自适应布局
   - 在小屏幕设备上自动切换为上下布局

2. **实时反馈**

   - 配置项修改时提供实时的格式验证
   - 保存操作提供进度指示和结果反馈
   - 错误信息以红色文本和图标形式明确提示

3. **操作引导**

   - 首次使用时提供操作向导
   - 关键配置项提供帮助提示和示例
   - 复杂配置提供分步骤引导

4. **数据安全**
   - 重要操作前提供二次确认弹窗
   - 支持配置草稿保存，避免意外丢失
   - 提供配置历史版本查看和回滚功能

**使用说明：**

管理员通过此界面可以高效地管理直付品类规则：

1. **新建规则流程**：点击"新建规则" → 填写基本信息 → 设置适用范围 → 配置药品分类 → 预览验证 → 保存启用

2. **修改规则流程**：在左侧列表选择规则 → 在右侧修改配置 → 验证配置 → 保存更新

3. **批量管理流程**：使用筛选条件定位目标规则 → 批量选择 → 执行批量操作

系统会根据规则优先级自动应用最合适的规则，并提供详细的执行日志供管理员监控和调试。

## 3.1.2 直付机构规则

### 3.1.2.1 规则定义

直付机构规则定义了系统如何在多个可用的外部支付平台中进行智能路由，确保交易能够高效、平衡地分配给合适的外部支付平台。

### 3.1.2.2 规则说明

**功能用途**

- 设置不同外部支付平台的优先级和选择策略
- 实现多平台间的负载均衡或业务分流
- 最大化资源利用效率和降低运营成本

**配置内容**

- 优先级策略：支持固定优先级或动态用单量两种模式

**规则类型**

- 固定优先级规则：基于预设的固定顺序选择平台
- 动态用单量规则：基于历史交易量（订单量或交易金额）动态调整优先级

**规则层级**

- 系统级规则：适用于全部客户的基础规则
- 药店级规则：适用于某药店/某类药店的直付机构规则
- 企业级规则：特定企业客户的直付机构规则

**规则优先级**
企业级 > 药店级 > 系统级

**使用场景**

- 客户使用保险公司移动 App 搜索附近的药店
- 选择药店后，移动 App 调用本系统接口获取此药店签约的外部支付平台
- 本系统按照固定优先级或动态规则对外部支付平台进行排序，返回给移动 App

### 3.1.2.3 规则样例

**样例一：基础固定优先级规则**

规则名称：标准固定优先级直付机构规则

规则内容：

- **优先级策略**：固定优先级
- **平台优先级排序**：
  1. 普跃（首选平台）
  2. 亿保
- **选择逻辑**：
  - 系统严格按照优先级顺序选择可用平台
  - 当高优先级平台不可用时，自动尝试下一优先级平台
  - 所有平台不可用时，交易失败并记录日志
- **适用范围**：全部药店和交易场景
- **例外情况**：特殊活动期间可临时调整优先级
- **使用场景**：保险公司与普跃健康有战略合作关系，希望优先使用该平台处理直付交易。系统会首先尝试通过普跃支付平台进行处理，仅当普跃支付平台不可用或返回错误时，才会依次尝试后续平台

**样例二：全局动态用单量分配规则**

规则名称：交易量平衡分配规则

规则内容：

- **优先级策略**：动态用单量（订单量）
- **参与平台**：
  - 普跃
  - 亿保
- **分配目标**：
  - 普跃：月交易量目标 60%
  - 亿保：月交易量目标 40%
- **调整机制**：
  - 计算周期：每天凌晨计算前一天各平台实际用单比例
  - 自动调整：如实际比例偏离目标超过 5%，系统自动调整当天分配权重
  - 最小保障：每个平台保证至少 10%的交易量
- **适用范围**：全部外部支付平台
- **使用场景**：保险公司与多家外部支付平台都有合约最低交易量要求，需要确保交易量按照约定比例分配。系统会持续监控各平台的实际交易量，并动态调整分配策略，确保月度统计结果接近目标比例

### 3.1.2.4 规则配置 UI

**菜单路径：** 系统设置 > 规则管理

**界面整体设计：**

直付机构规则配置界面采用左右分栏设计，左侧为规则基础信息配置和策略类型选择，右侧为具体的配置参数面板。界面整体布局清晰，支持固定优先级和动态单量两种策略类型的无缝切换。

#### 直付机构规则配置界面

![直付机构规则配置界面](图3-2)

**界面布局结构：**

界面采用左右分栏设计，左侧为规则基础信息和策略类型选择，右侧为对应策略的详细配置参数，底部为操作按钮区域。

**左侧配置面板：**

1. **规则基础信息配置**

   - 规则名称：必填字段，支持中英文，最大长度 50 字符，实时验证重名
   - 规则编码：系统自动生成或手动输入，用于 API 调用和系统集成
   - 规则描述：多行文本框，详细说明规则用途，最大 500 字符
   - 适用范围：下拉选择（系统级/药店级/企业级）
   - 生效时间：日期时间选择器，支持立即生效或定时生效
   - 失效时间：可选字段，支持永久有效或定时失效

2. **策略类型选择**

   - **固定优先级策略**：
     - 单选按钮：选择固定优先级模式
     - 说明文字：支付平台按照固定的优先级顺序进行选择
     - 适用场景：适合有明确平台优先级偏好的业务场景

   - **动态单量策略**：
     - 单选按钮：选择动态单量模式
     - 说明文字：根据实际交易量动态调整平台选择权重
     - 适用场景：适合需要平衡各平台交易量的业务场景

3. **适用对象配置**（根据适用范围显示）

   - 系统级配置：适用于所有药店和企业，无需额外选择
   - 药店级配置：
     - 药店多选器：支持按地区、药店名称、药店编码搜索和选择
     - 已选药店列表：显示已选择的药店，支持单个移除
   - 企业级配置：
     - 企业多选器：支持按企业名称、编码搜索和选择
     - 已选企业列表：显示已选择的企业，支持单个移除

**右侧配置参数面板：**

根据左侧选择的策略类型，右侧面板会动态显示对应的配置参数界面。

### **固定优先级配置面板**

当选择固定优先级策略时，右侧显示以下配置选项：

1. **支付平台列表配置**

   - 平台选择表格：
     - 平台名称：显示可用的外部支付平台名称
     - 平台状态：实时显示平台的在线/离线状态指示器
     - 优先级设置：数字输入框，设置平台优先级（数字越小优先级越高）
     - 是否启用：开关控件，控制平台是否参与路由选择
     - 操作按钮：删除按钮，移除不需要的平台

   - 平台管理操作：
     - 添加平台：从可用平台列表中选择并添加新的支付平台
     - 批量操作：支持批量设置优先级、批量启用/禁用
     - 拖拽排序：支持鼠标拖拽调整平台优先级顺序

2. **优先级规则设置**

   - 排序规则：严格按照优先级数值从小到大进行平台选择
   - 故障转移：当高优先级平台不可用时，自动选择下一优先级平台
   - 权重分配：相同优先级的平台支持权重分配

### **动态单量配置面板**

当选择动态单量策略时，右侧显示以下配置选项：

1. **分配策略配置**

   - 分配策略选择：
     - 比例分配：按照设定的目标比例进行分配
     - 轮询分配：按照轮询方式平均分配
     - 加权随机：根据权重进行随机分配

   - 统计维度设置：
     - 订单数量：以交易订单数量作为统计基准
     - 交易金额：以交易金额作为统计基准

   - 统计周期设置：
     - 小时、天、周、月等不同统计周期选择

2. **平台分配配置表格**

   - 平台配置表格：
     - 平台名称：显示外部支付平台名称
     - 平台状态：实时显示平台状态
     - 目标分配比例：设置该平台的目标交易分配比例
     - 最小保障比例：设置该平台的最低保障分配比例  
     - 最大限制比例：设置该平台的最高限制分配比例
     - 权重：设置平台权重值
     - 是否启用：控制平台是否参与动态分配
     - 操作：添加、删除平台操作

3. **动态调整参数**

   - 重新平衡阈值：设置触发重新平衡的偏差阈值（如10%）
   - 调整频率：设置系统自动调整的频率
   - 平滑参数：设置调整的平滑度，避免频繁波动

**高级配置选项：**

1. **故障转移策略**

   - 转移模式：
     - 顺序转移：严格按优先级顺序转移
     - 智能转移：结合平台状态和历史成功率
     - 并行尝试：同时尝试多个平台（高级功能）
   - 重试机制：
     - 重试次数：设置单个平台的最大重试次数
     - 重试间隔：设置重试的时间间隔
     - 超时设置：设置单次请求的超时时间
   - 失败处理：
     - 全部失败时的处理策略
     - 错误通知机制配置
     - 降级服务配置

2. **监控和告警配置**
   - 性能监控：
     - 响应时间阈值设置
     - 成功率阈值设置
     - 并发量监控配置
   - 告警规则：
     - 平台故障告警
     - 性能异常告警
     - 流量异常告警
   - 通知方式：
     - 邮件通知配置
     - 短信通知配置
     - 系统内消息通知

**底部操作区域：**

位于界面底部的统一操作按钮区域，提供规则配置的各种操作功能：

1. **主要操作按钮**

   - 保存：保存当前规则配置信息
   - 保存并启用：保存配置并立即生效
   - 取消：放弃当前修改，返回上一页面
   - 重置：重置表单到初始状态

2. **辅助功能按钮**

   - 预览配置：预览当前规则配置的完整信息
   - 测试规则：使用测试数据验证规则配置的正确性
   - 导出配置：将当前规则配置导出为 JSON 或 Excel 格式
   - 复制规则：基于当前规则创建副本进行修改

3. **配置验证提示**

   - 实时验证：配置过程中实时验证参数的有效性
   - 错误提示：配置错误时显示具体的错误信息和修改建议
   - 完整性检查：保存前检查配置的完整性和一致性

#### 界面交互说明

**动态界面切换：**

根据用户在左侧选择的策略类型，右侧配置面板会动态切换显示对应的配置选项，实现无缝的用户体验。

**使用说明：**

管理员可以通过以下流程高效配置直付机构规则：

1. **固定优先级规则配置流程**：

   - 填写基础信息 → 选择适用范围 → 选择固定优先级策略 → 配置平台优先级 → 设置高级选项 → 预览测试 → 保存启用

2. **动态单量规则配置流程**：

   - 填写基础信息 → 选择适用范围 → 选择动态单量策略 → 配置分配策略 → 设置平台分配参数 → 配置调整机制 → 测试验证 → 保存启用

3. **规则维护管理流程**：
   - 定期检查规则状态 → 分析平台选择效果 → 优化配置参数 → 处理异常情况 → 更新规则配置

系统提供完整的策略配置和管理功能，支持固定优先级和动态单量两种智能路由策略，确保各外部支付平台的选择既符合业务优先级要求，又能实现合理的负载分配。

## 3.1.3 医疗机构分配规则

### 3.1.3.1 规则定义

医疗机构分配规则专注于建立和管理药店与外部支付平台之间的签约映射关系，它决定了每个药店可以使用哪些外部支付平台进行直付交易。

### 3.1.3.2 规则说明

**功能用途**

- 建立药店与外部支付平台之间的映射关系
- 确定每个药店可使用的外部支付平台范围
- 在客户选择药店后，提供可用的外部支付平台选项
- 结合直付机构规则进行智能路由决策

**配置内容**

- 药店签约平台：设置每个药店已签约的外部支付平台列表，且支持配置优先级

**应用场景**
客户在药店消费时，系统根据规则提供最佳外部支付平台选择

### 3.1.3.3 规则样例

**样例一：标准医疗机构分配规则**

规则名称：标准药店平台映射规则

规则内容：

- **药店配置**：
  - 同仁堂（北京朝阳门店）：已签约平台：普跃、亿保
  - 国大药房（上海南京路店）：已签约平台：普跃
- **应用逻辑**：
  - 客户 App 显示药店时，同时显示该药店已签约平台
  - 客户选择药店后，可选择该药店支持的任一平台
  - 系统根据配置的优先级，推荐默认平台
- **使用场景**：林先生在保险公司 App 上根据客户当前所在位置搜索附近药店，看到同仁堂北京朝阳门店支持普跃、亿保两个支付平台。进入药店后，系统默认推荐使用普跃支付平台，但王先生可以根据个人偏好选择其他已签约平台

### 3.1.3.4 规则配置 UI

**菜单路径：** 供应商管理 > 药店管理

**界面整体设计：**

医疗机构分配规则配置界面采用主从结构设计，左侧为药店管理区域，右侧为平台映射配置区域，界面布局清晰，支持高效的批量配置操作。

**医疗机构分配规则配置界面**

![医疗机构分配规则](图3-4)

**界面布局结构：**

界面采用左右分栏设计，左侧为药店信息管理面板，右侧为平台映射配置面板，顶部提供全局操作工具栏，底部为批量操作区域。

**顶部全局工具栏：**

1. **快速操作区域**

   - 全局搜索：支持跨药店和平台的全文搜索
   - 筛选器：按地区、药店类型、签约状态等维度筛选
   - 视图切换：支持列表视图、卡片视图、地图视图切换
   - 导入导出：批量导入配置文件，导出当前配置
   - 新建配置：快速创建新的药店-平台映射关系

2. **统计概览面板**
   - 总药店数：显示系统中已注册的药店总数
   - 已配置数：显示已完成平台映射配置的药店数量
   - 平台覆盖率：显示各外部支付平台的药店覆盖情况
   - 配置完成度：显示整体配置进度和待处理项目

**左侧药店信息管理面板：**

1. **药店搜索和筛选区域**

   - 智能搜索框：
     - 支持按药店名称、编码、地址快速搜索
     - 提供搜索建议和自动补全功能
     - 支持拼音首字母搜索
     - 历史搜索记录保存
   - 高级筛选器：
     - 地区筛选：省市区三级联动选择
     - 药店类型：连锁药店、单体药店、医院药房等
     - 签约状态：已签约、未签约、部分签约
     - 营业状态：正常营业、暂停营业、关闭
     - 注册时间：按注册时间范围筛选

2. **药店列表展示区域**

   - 列表视图：
     - 药店名称：显示完整的药店名称
     - 药店编码：系统内唯一标识码
     - 所在地区：省市区详细地址
     - 药店类型：连锁/单体/医院等类型标识
     - 签约平台数：显示已签约的平台数量
     - 配置状态：完整/部分/未配置状态指示
     - 最后更新：显示最后配置更新时间
   - 卡片视图：
     - 药店基本信息卡片化展示
     - 签约平台图标快速预览
     - 配置完成度进度条显示
   - 地图视图：
     - 在地图上标注药店位置
     - 不同颜色表示配置状态
     - 支持地图缩放和区域选择

3. **药店详情信息面板**
   - 基本信息：
     - 药店全称：完整的药店名称
     - 药店编码：系统内部编码
     - 统一社会信用代码：企业法定标识
     - 药店类型：连锁、单体、医院药房等
     - 经营范围：药品经营许可范围
   - 联系信息：
     - 详细地址：完整的地理位置信息
     - 联系电话：主要联系方式
     - 负责人：药店负责人信息
     - 邮箱地址：官方邮箱
   - 经营信息：
     - 营业时间：日常营业时间安排
     - 营业状态：当前营业状态
     - 服务范围：覆盖的服务区域
     - 特色服务：提供的特殊服务项目

**右侧平台映射配置面板：**

1. **当前配置概览区域**

   - 配置摘要：
     - 已签约平台：显示当前药店已签约的平台列表
     - 配置完成度：显示配置的完整程度
     - 最后更新：显示最近的配置更新时间
     - 配置状态：草稿/已保存/已生效状态
   - 快速统计：
     - 签约平台数量：数字化显示签约平台总数
     - 优先级分布：显示各优先级的平台分布
     - 状态分布：显示各状态的映射关系分布

2. **已签约平台管理区域**

   - 平台列表表格：
     - 平台名称：外部支付平台的完整名称
     - 平台编码：平台在系统中的唯一标识
     - 签约状态：已签约/待签约/已终止
     - 优先级：数字显示推荐优先级（1-10）
     - 生效时间：签约关系的生效日期
     - 失效时间：签约关系的失效日期
     - 签约类型：直付/退款/对账等业务类型
     - 操作按钮：编辑/删除/暂停等操作
   - 优先级调整工具：
     - 拖拽排序：支持鼠标拖拽调整优先级顺序
     - 数字输入：直接输入优先级数值
     - 批量调整：选择多个平台批量设置优先级
     - 智能推荐：基于历史数据推荐最优优先级

3. **可选平台添加区域**
   - 平台选择器：
     - 可用平台列表：显示系统中可用但未签约的平台
     - 平台筛选：按平台类型、服务能力筛选
     - 平台详情：显示平台的详细信息和能力
     - 批量选择：支持多选添加多个平台
   - 签约配置：
     - 签约类型选择：选择支持的业务类型
     - 生效时间设置：设置签约关系的生效时间
     - 失效时间设置：设置签约关系的失效时间
     - 优先级设置：为新添加的平台设置初始优先级

**高级配置选项面板：**

1. **签约条件配置**

   - 业务范围限制：
     - 支持的药品类别：限制可使用的药品范围
     - 金额限制：设置单笔/日/月交易限额
     - 时间限制：设置可使用的时间段
   - 特殊规则：
     - 节假日规则：节假日期间的特殊处理
     - 紧急情况规则：紧急情况下的备用方案
     - 维护期规则：平台维护期间的处理方式

2. **风险控制配置**
   - 监控规则：
     - 交易频率监控：异常交易频率告警
     - 金额异常监控：异常金额交易告警
     - 失败率监控：交易失败率告警阈值
   - 应急处理：
     - 自动切换：平台故障时的自动切换机制
     - 人工干预：需要人工确认的情况设置
     - 通知机制：异常情况的通知方式配置

**底部批量操作区域：**

1. **批量导入功能**

   - 模板下载：
     - 标准模板：提供标准的 Excel 导入模板
     - 示例数据：包含示例数据的模板文件
     - 字段说明：详细的字段填写说明文档
   - 文件上传：
     - 文件选择：支持 Excel、CSV 格式文件
     - 格式验证：自动验证文件格式和数据完整性
     - 预览功能：上传前预览导入数据
     - 错误检查：自动检查数据冲突和错误
   - 导入执行：
     - 分批导入：大文件分批处理
     - 进度显示：实时显示导入进度
     - 结果报告：详细的导入结果报告
     - 回滚功能：支持导入失败时的数据回滚

2. **批量修改功能**

   - 选择操作：
     - 全选功能：一键选择所有药店
     - 条件选择：按筛选条件批量选择
     - 手动选择：逐个选择需要修改的药店
   - 批量操作：
     - 批量添加平台：为选中药店批量添加平台
     - 批量删除平台：为选中药店批量删除平台
     - 批量调整优先级：批量调整平台优先级
     - 批量设置时间：批量设置生效失效时间

3. **配置模板管理**
   - 模板创建：
     - 基于现有配置创建模板
     - 自定义模板参数设置
     - 模板命名和描述
   - 模板应用：
     - 选择药店应用模板
     - 模板参数自定义调整
     - 批量应用确认
   - 模板管理：
     - 模板列表查看
     - 模板编辑修改
     - 模板删除和备份

**状态管理和审核流程：**

1. **状态管理系统**

   - 配置状态：
     - 草稿状态：配置未完成或未保存
     - 待审核状态：配置完成等待审核
     - 已生效状态：配置已审核通过并生效
     - 已暂停状态：配置暂时停用
     - 已失效状态：配置已过期或被删除
   - 状态转换：
     - 自动状态转换：基于时间和条件的自动转换
     - 手动状态转换：管理员手动调整状态
     - 状态历史：记录所有状态变更历史

2. **审核流程配置**
   - 审核规则：
     - 审核层级：设置多级审核流程
     - 审核权限：不同角色的审核权限设置
     - 审核条件：触发审核的条件设置
   - 审核操作：
     - 审核提交：提交配置变更审核申请
     - 审核处理：审核人员处理审核申请
     - 审核结果：通过/拒绝/退回修改
     - 审核记录：完整的审核过程记录

**数据验证和质量控制：**

1. **实时验证机制**

   - 数据完整性验证：
     - 必填字段检查：确保关键信息完整
     - 格式验证：验证数据格式正确性
     - 逻辑验证：检查数据逻辑一致性
   - 业务规则验证：
     - 重复性检查：避免重复配置
     - 冲突检查：检查配置冲突
     - 依赖性检查：验证配置依赖关系

2. **质量控制工具**
   - 配置检查报告：
     - 完整性报告：配置完整性统计
     - 一致性报告：配置一致性检查
     - 异常报告：异常配置识别
   - 优化建议：
     - 配置优化建议：基于最佳实践的建议
     - 性能优化建议：提升系统性能的建议
     - 维护建议：日常维护的建议

**交互体验设计：**

1. **响应式设计**

   - 自适应布局：支持不同屏幕尺寸的自适应显示
   - 移动端优化：针对移动设备的界面优化
   - 触控支持：支持触控操作和手势

2. **用户体验优化**

   - 操作引导：首次使用的操作引导
   - 快捷键支持：常用操作的快捷键
   - 操作历史：记录用户操作历史
   - 个性化设置：支持界面个性化配置

3. **性能优化**
   - 懒加载：大数据量的分页和懒加载
   - 缓存机制：常用数据的缓存优化
   - 异步处理：耗时操作的异步处理

**使用说明：**

管理员可以通过以下流程高效管理药店与外部支付平台的签约关系：

1. **单个药店配置流程**：

   - 搜索选择药店 → 查看当前配置 → 添加/删除平台 → 设置优先级 → 配置签约条件 → 保存并提交审核

2. **批量配置流程**：

   - 准备配置数据 → 下载导入模板 → 填写配置信息 → 上传文件 → 验证数据 → 执行导入 → 检查结果

3. **配置维护流程**：

   - 定期检查配置状态 → 处理异常配置 → 优化配置参数 → 更新签约信息 → 生成维护报告

4. **应急处理流程**：
   - 监控异常告警 → 快速定位问题 → 临时调整配置 → 通知相关人员 → 制定长期解决方案

系统提供完整的配置管理和质量控制功能，确保药店与外部支付平台的映射关系准确可靠，为客户提供最佳的支付体验。配置完成后，客户在对应药店消费时将只能看到已配置的支付平台选项，系统会根据优先级推荐最佳支付方式。

## 3.1.4 计费规则

### 3.1.4.1 规则定义

计费规则是定义如何计算医药费用报销金额的基础规则集，主要来源于保险核心系统，本系统亦可增加计费规则。它为直付交易中的费用拆分提供了标准算法和参数，是整个直付业务的计算基础。

直付品类规则和计费规则相结合实现了医保直付交易的精准识别与计算，确保了合规品类能正确享受保险直付并准确计算报销金额。本节描述的是计费规则，关于直付品类规则，参见章节：3.1.1 直付品类规则

### 3.1.4.2 规则说明

**功能用途**

- 定义不同药品类别的基础报销比例
- 设置适用于不同保险计划的计费标准
- 提供费用拆分的计算依据（自付部分与报销部分）
- 作为健康聚合支付系统核查计费结果的标准

**配置内容**

- 药品分类计费标准：按医保目录或自定义分类设置基础报销比例
- 免赔额设置：定义不同药品或服务类别的起付线
- 封顶金额：设置单次/日/月/年度最高报销限额

**规则分类**

- 基础计费规则：一般适用的药品分类计费标准
- 特殊药品规则：针对特定疾病或特殊药品的计费规则

**规则层级**

- 系统级规则：适用于全部客户的计费规则
- 企业级规则：适用于企业级的计费规则
- 员工群体级规则：针对特定员工群体的差异化计费规则

**规则优先级**

- 核心系统推送的计费规则优先级最高
- 如果未能匹配核心系统计费规则，按照如下优先级：员工群体级 > 企业级 > 系统级

### 3.1.4.3 规则样例

**样例一：药品类别基础计费规则**

规则描述：根据国家医保目录的药品分类，为不同类别的药品设定基础报销比例和起付标准。这是直付系统中最基础的计费规则，所有药品报销都将首先参考此规则。

配置内容：

- 甲类药品：基础报销比例 90%，无起付线
- 乙类药品：基础报销比例 70%，起付线 50 元
- 丙类药品：基础报销比例 50%，起付线 100 元
- 非医保药品：基础报销比例 30%，起付线 200 元
- 保健品：不予报销

使用场景：王先生是某企业员工，在签约药店购买了 3 种药品：甲类药品 120 元、乙类药品 200 元、保健品 80 元。系统根据此规则计算：

- 甲类药品报销：120 元 ×90%=108 元（企业支付）
- 乙类药品报销：(200-50)×70%=105 元（企业支付）
- 保健品：0 元（不予报销）
- 王先生自付：120-108+200-105+80=187 元
- 企业直付：108+105=213 元

**样例二：特殊疾病药品计费规则**

规则描述：针对特定慢性病或重大疾病，设定专项的药品报销政策，通常提供比普通药品更高的报销比例和更高的年度限额，但需要提供相关疾病诊断证明

配置内容：

- 糖尿病：相关药品（胰岛素、二甲双胍等）报销比例 95%
- 高血压：相关药品（氨氯地平、卡托普利等）报销比例 95%
- 恶性肿瘤：所有抗肿瘤药物报销比例 98%

使用场景：张女士被诊断为糖尿病，她在药店购买了胰岛素和血糖测试仪，合计 1200 元。系统应用特殊疾病规则：

- 胰岛素（属于糖尿病药品）：1000 元 ×95%=950 元（企业支付）
- 血糖测试仪（非药品）：按普通医疗器械计算，200 元 ×70%=140 元（企业支付）
- 张女士自付：1000-950+200-140=110 元
- 企业直付：950+140=1090 元

### 3.1.4.4 规则配置 UI

**菜单路径：** 系统设置 > 规则管理

**界面整体设计：**

计费规则配置界面采用标签页设计，支持基础计费规则和特殊疾病计费规则的分类管理。界面整体布局清晰，功能模块划分明确，为管理员提供高效的规则配置和管理体验。

**布局结构：**

- **顶部导航区域**：规则类型切换标签、全局操作按钮
- **左侧规则管理面板**：规则列表、搜索筛选、快速操作
- **右侧配置详情面板**：详细配置表单、参数设置、预览验证
- **底部操作区域**：保存、取消、预览、批量操作等功能按钮

**药品类别基础计费规则配置界面**

![药品类别基础计费规则](图3-5)

**顶部规则管理区域：**

1. **规则类型切换**

   - 基础计费规则：药品类别基础计费配置
   - 特殊疾病规则：特殊疾病药品计费配置
   - 企业定制规则：企业级个性化计费规则
   - 规则模板：预设的规则模板管理

2. **全局操作工具**
   - 新建规则：创建新的计费规则
   - 批量导入：从 Excel 文件批量导入规则
   - 规则复制：基于现有规则创建副本
   - 规则导出：导出规则配置到 Excel 文件
   - 规则同步：与保险核心系统同步规则

**左侧规则管理面板：**

1. **搜索和筛选功能**

   - 规则名称搜索：支持模糊搜索规则名称
   - 状态筛选：按生效状态筛选（生效中/待生效/已失效）
   - 优先级筛选：按规则优先级筛选（系统级/企业级/群体级）
   - 适用范围筛选：按适用对象筛选规则
   - 创建时间筛选：按创建时间范围筛选

2. **规则列表展示**

   - 规则基本信息：名称、描述、优先级、状态
   - 适用范围：显示规则的适用对象和范围
   - 生效时间：规则的生效和失效时间
   - 操作按钮：编辑、复制、删除、启用/禁用
   - 排序功能：支持按多个字段排序

3. **快速操作功能**
   - 批量选择：支持多选规则进行批量操作
   - 批量启用/禁用：批量修改规则状态
   - 批量删除：批量删除选中规则
   - 批量导出：批量导出选中规则配置

**右侧配置详情面板：**

1. **基础信息配置面板**

   - 规则标识：
     - 规则名称：输入计费规则的标识名称（必填）
     - 规则编码：系统自动生成或手动输入的唯一编码
     - 规则描述：详细说明规则的适用场景和计算方式
     - 规则版本：支持规则版本管理和历史追溯
   - 优先级设置：
     - 规则层级：系统级/企业级/员工群体级
     - 优先级数值：数值越大优先级越高
     - 冲突处理：规则冲突时的处理策略
   - 适用范围：
     - 适用对象：全部客户/特定企业/特定群体
     - 地域限制：可选择特定地区适用
     - 时间限制：设置规则的有效期限

2. **药品分类计费配置面板**

   - 医保目录分类：
     - 甲类药品：基础报销比例设置（0-100%）
     - 乙类药品：基础报销比例设置（0-100%）
     - 丙类药品：基础报销比例设置（0-100%）
     - 非医保药品：基础报销比例设置（0-100%）
   - 起付线配置：
     - 甲类起付线：设置甲类药品的免赔额度
     - 乙类起付线：设置乙类药品的免赔额度
     - 丙类起付线：设置丙类药品的免赔额度
     - 非医保起付线：设置非医保药品的免赔额度
   - 封顶金额设置：
     - 单次限额：单次购药的最高报销金额
     - 日限额：每日最高报销金额
     - 月限额：每月最高报销金额
     - 年限额：每年最高报销金额

3. **特殊配置项面板**

   - 非药品处理：
     - 保健品策略：设置保健品、营养品的报销策略
     - 医疗器械策略：配置血糖仪、血压计等的报销规则
     - 化妆品策略：设置化妆品类商品的处理方式
   - 例外清单管理：
     - 特殊商品清单：维护特殊处理的商品清单
     - 例外规则：设置特殊情况下的例外处理规则
     - 黑名单管理：维护不予报销的商品黑名单
   - 计算规则配置：
     - 计算精度：设置金额计算的小数位数
     - 舍入规则：设置金额舍入的处理方式
     - 最小报销金额：设置触发报销的最小金额

4. **生效管理面板**
   - 时间设置：
     - 生效时间：设置规则的生效日期和时间
     - 失效时间：设置规则的失效日期和时间
     - 时区设置：选择适用的时区
   - 适用对象管理：
     - 企业选择：选择适用的企业列表
     - 群体选择：选择适用的员工群体
     - 排除设置：设置不适用的对象
   - 生效策略：
     - 立即生效：保存后立即生效
     - 定时生效：按设定时间自动生效
     - 手动生效：需要管理员手动激活

**特殊疾病药品计费规则配置界面**

![特殊疾病药品计费规则](图3-6)

**疾病分类配置面板：**

1. **疾病信息管理**

   - 疾病基础信息：
     - 疾病名称：输入疾病的标准名称
     - 疾病代码：关联标准疾病编码（ICD-10 等）
     - 疾病分类：按疾病类型进行分类管理
     - 疾病描述：详细描述疾病的特征和范围
   - 疾病等级设置：
     - 慢性病：糖尿病、高血压等慢性疾病
     - 重大疾病：恶性肿瘤、器官移植等
     - 罕见病：罕见疾病的特殊处理
     - 职业病：职业相关疾病的特殊政策

2. **药品清单管理**
   - 关联药品维护：
     - 药品搜索：支持按药品名称、编码搜索
     - 药品添加：将药品添加到疾病关联清单
     - 药品分类：按治疗作用对药品进行分类
     - 药品描述：添加药品的使用说明和注意事项
   - 清单管理功能：
     - 批量导入：从 Excel 文件批量导入药品清单
     - 清单导出：导出当前疾病的药品清单
     - 清单复制：从其他疾病复制药品清单
     - 清单同步：与医保目录同步更新

**特惠计费参数面板：**

1. **报销比例配置**

   - 特殊报销比例：
     - 基础比例：设置高于普通药品的报销比例
     - 分级比例：按药品重要性设置不同比例
     - 阶梯比例：按消费金额设置阶梯报销比例
   - 比例调整机制：
     - 动态调整：根据使用情况动态调整比例
     - 季节性调整：按季节或时期调整比例
     - 个性化调整：针对特定患者的个性化比例

2. **限额管理配置**

   - 年度限额设置：
     - 疾病年度限额：配置特殊疾病的年度报销上限
     - 药品年度限额：单个药品的年度使用限额
     - 总体年度限额：患者总体的年度报销限额
   - 限额监控：
     - 实时监控：实时监控限额使用情况
     - 预警设置：接近限额时的预警机制
     - 超额处理：超出限额时的处理策略

3. **诊断要求配置**
   - 诊断证明要求：
     - 诊断书要求：设置享受特惠政策所需的诊断证明
     - 医院等级要求：限定出具诊断的医院等级
     - 医生资质要求：限定出具诊断的医生资质
   - 证明文件管理：
     - 文件上传：支持诊断证明文件上传
     - 文件验证：自动验证文件的真实性
     - 文件存档：长期保存诊断证明文件

**审核流程配置面板：**

1. **预授权机制**

   - 授权流程设置：
     - 自动授权：符合条件的自动授权
     - 人工审核：需要人工审核的情况
     - 分级审核：按金额或风险等级分级审核
   - 授权时限管理：
     - 审核时限：配置审核的时间要求
     - 超时处理：审核超时的处理机制
     - 加急处理：紧急情况的快速审核通道

2. **申请材料管理**
   - 材料清单配置：
     - 必需材料：定义必须提交的证明文件
     - 可选材料：定义可选提交的补充文件
     - 材料模板：提供标准的申请材料模板
   - 材料审核标准：
     - 完整性检查：检查材料的完整性
     - 真实性验证：验证材料的真实性
     - 时效性检查：检查材料的有效期

**监控预警面板：**

1. **用量监控配置**

   - 异常用药监控：
     - 用量阈值：设置异常用药量的预警阈值
     - 频次监控：监控用药频次的异常情况
     - 组合用药：监控药品组合使用的合理性
   - 预警机制：
     - 实时预警：实时监控并发出预警
     - 定期报告：定期生成用药监控报告
     - 风险评估：评估用药风险等级

2. **费用预警配置**

   - 费用监控设置：
     - 超额预警：配置超额消费的提醒机制
     - 趋势分析：分析费用增长趋势
     - 异常检测：检测异常的费用支出模式
   - 预警响应：
     - 自动限制：超出阈值时的自动限制措施
     - 人工干预：需要人工干预的情况设置
     - 通知机制：异常情况的通知方式配置

3. **定期复查配置**
   - 复查周期设置：
     - 疾病状态复查：设置疾病状态的定期确认要求
     - 诊断更新：定期更新诊断证明的要求
     - 治疗方案调整：根据复查结果调整治疗方案
   - 复查管理：
     - 复查提醒：自动提醒患者进行复查
     - 复查记录：记录复查的详细信息
     - 复查结果处理：根据复查结果调整计费策略

**底部操作区域：**

1. **基础操作按钮**

   - 保存草稿：保存当前配置为草稿状态
   - 保存并生效：保存配置并立即生效
   - 预览效果：预览规则配置的效果
   - 取消操作：取消当前的配置操作
   - 重置表单：重置表单到初始状态

2. **高级操作功能**

   - 规则测试：
     - 测试用例：创建测试用例验证规则
     - 模拟计算：模拟实际场景的计费计算
     - 结果对比：对比新旧规则的计算结果
   - 规则验证：
     - 逻辑验证：验证规则配置的逻辑正确性
     - 冲突检测：检测与其他规则的冲突
     - 完整性检查：检查配置的完整性

3. **批量操作功能**
   - 批量配置：
     - 模板应用：应用预设的配置模板
     - 批量修改：批量修改多个规则的参数
     - 批量生效：批量设置规则的生效时间
   - 数据管理：
     - 数据导入：从外部文件导入规则配置
     - 数据导出：导出规则配置到文件
     - 数据备份：备份当前的规则配置

**交互体验设计：**

1. **响应式设计**

   - 自适应布局：支持不同屏幕尺寸的自适应显示
   - 移动端优化：针对移动设备的界面优化
   - 触控支持：支持触控操作和手势

2. **用户体验优化**

   - 智能提示：
     - 输入提示：智能提示输入内容
     - 错误提示：实时显示输入错误信息
     - 操作引导：首次使用的操作引导
   - 快捷操作：
     - 快捷键支持：常用操作的快捷键
     - 右键菜单：提供便捷的右键操作菜单
     - 拖拽操作：支持拖拽方式的操作

3. **性能优化**
   - 数据加载：
     - 懒加载：大数据量的分页和懒加载
     - 缓存机制：常用数据的缓存优化
     - 异步处理：耗时操作的异步处理
   - 界面响应：
     - 加载动画：操作过程中的加载动画
     - 进度显示：长时间操作的进度显示
     - 操作反馈：及时的操作结果反馈

**使用说明：**

管理员可以通过以下流程高效管理计费规则配置：

1. **基础计费规则配置流程**：

   - 选择规则类型 → 填写基础信息 → 配置药品分类计费参数 → 设置特殊配置项 → 配置生效管理 → 预览测试 → 保存生效

2. **特殊疾病规则配置流程**：

   - 选择疾病类型 → 配置疾病信息 → 维护药品清单 → 设置特惠参数 → 配置审核流程 → 设置监控预警 → 测试验证 → 保存生效

3. **规则维护流程**：

   - 定期检查规则状态 → 分析规则使用效果 → 优化规则参数 → 处理异常情况 → 更新规则配置 → 生成维护报告

4. **规则测试流程**：
   - 创建测试用例 → 模拟计费场景 → 验证计算结果 → 对比历史数据 → 确认规则正确性 → 正式发布规则

系统提供完整的计费规则配置和管理功能，确保医药费用报销计算的准确性和合规性。配置完成后，系统将根据规则自动进行费用拆分计算，为客户提供准确的报销服务。所有规则变更都有完整的审计日志，确保配置过程的可追溯性和安全性。

## 3.1.5 结算规则

### 3.1.5.1 规则定义

结算规则是配置与外部支付平台的结算周期、方式的核心规则集。它定义了本系统如何与各合作伙伴进行财务结算，确保资金流转的准确性和高效性。

### 3.1.5.2 规则说明

**功能用途**

- 定义与外部支付平台的结算周期
- 确定结算对象和资金流向

**配置内容**

- 结算周期：定义结算频率（日结/周结/月结/季度结算）
- 结算类型：保险理赔结算，服务费结算
- 结算对象：确定结算资金支付给哪些主体。保险理赔结算由保险公司向外部支付平台支付费用，服务费结算由外部支付平台向保险公司支付费用。

**规则分类**

- 周期性规则：基于固定时间周期的常规结算规则，适用于所有结算类型
- 阶梯式结算规则：基于交易量设置的梯度结算策略，只适用于服务费结算类型

**规则层级**

- 系统级规则：适用于全部支付平台的基础规则
- 平台级规则：特定外部支付平台的结算规则

**规则优先级**
平台级 > 系统级

### 3.1.5.3 规则样例

**样例一：基础月保险理赔结算规则**

规则名称：标准月度保险理赔结算规则

规则内容：

- **结算周期**：自然月，每月结算一次
- **结算期间**：上月 1 日 0 时至月末 24 时的所有交易
- **结算对象**：外部支付平台，保险公司向外部支付平台支付保险理赔费用
- **结算账户信息**：与平台签约时登记的银行账户
- **结算规则**：
  - 结算金额：客户订单日期在结算期间内的保险理赔金额之和
  - 税费处理：服务费含税

使用场景：健康聚合支付系统与普跃健康进行 6 月份的结算。月末普跃健康平台生成结算单，包含 6 月 1 日至 30 日期间的全部交易：

- 订单交易总额：500 万元
- 保险理赔金额：300 万元
- 结算总额：300 万元

**样例二：阶梯式服务费结算规则**

规则名称：交易量阶梯服务费结算规则

规则内容：

- **结算周期**：自然月，每月结算一次
- **结算时间要求**：同标准月度结算规则
- **结算对象**：保险公司，外部支付平台向保险公司支付服务费
- **服务费阶梯**：
  - 月交易额 < 100 万元：服务费率 2.5%
  - 月交易额 100-300 万元：服务费率 2.0%
  - 月交易额 300-500 万元：服务费率 1.8%
  - 月交易额 500-1000 万元：服务费率 1.5%
  - 月交易额 > 1000 万元：服务费率 1.2%

使用场景：普跃支付平台 7 月份交易额达到 780 万元，适用 1.5%的服务费率

- 服务费：780 万元 × 1.5% = 11.7 万元
- 实际结算金额：11.7 万元

### 3.1.5.4 规则配置 UI

**菜单路径：** 系统设置 > 规则管理

**界面整体设计：**

结算规则配置界面采用标签页设计，支持基础月保险理赔结算规则和阶梯式服务费结算规则两种配置模式。界面整体布局清晰，功能分区明确，为管理员提供高效的结算规则配置和管理工具。

**布局结构：**

- **顶部规则管理区域**：规则类型选择、快速操作工具栏
- **左侧规则列表面板**：已配置规则的管理和展示
- **右侧配置详情面板**：具体规则的详细配置界面
- **底部操作区域**：保存、预览、测试等操作按钮

**详细功能模块：**

**1. 顶部规则管理区域**

_规则类型选择器_

- 标签页切换：「基础月保险理赔结算规则」「阶梯式服务费结算规则」
- 规则状态统计：显示各类型规则的数量和状态分布
- 全局搜索框：支持按规则名称、平台、状态等条件快速搜索

_快速操作工具栏_

- 新建规则：快速创建新的结算规则
- 批量操作：支持批量启用/禁用、批量导出等操作
- 规则模板：提供常用结算规则模板，快速创建标准配置
- 数据同步：与外部平台同步最新的结算参数

**2. 左侧规则列表面板**

_搜索筛选区域_

- 高级筛选：按平台、结算类型、状态、创建时间等条件筛选
- 排序选项：支持按创建时间、更新时间、规则名称等排序
- 快速筛选标签：常用筛选条件的快捷按钮

_规则列表展示_

- 规则卡片：显示规则名称、类型、适用平台、状态等关键信息
- 状态指示器：通过颜色和图标直观显示规则状态（生效中/已停用/待审核）
- 快速操作：编辑、复制、删除、启用/禁用等快捷操作
- 规则预览：鼠标悬停显示规则的详细摘要信息

**3. 右侧配置详情面板**

_基础月保险理赔结算规则配置_

- **基础信息配置**

  - 规则名称：输入规则的标识名称，支持中英文和特殊字符
  - 规则描述：详细描述规则的用途和适用场景
  - 适用平台：多选下拉框，选择规则适用的外部支付平台
  - 规则优先级：设置规则的执行优先级（系统级/平台级）

- **结算周期配置**

  - 结算频率：单选按钮组（日结/周结/月结/季度结算）
  - 结算时间：设置具体的结算执行时间（如每月 3 日执行）
  - 结算期间：定义结算数据的统计时间范围
  - 节假日处理：配置遇到节假日时的结算时间调整规则

- **结算对象配置**

  - 资金流向：选择结算资金的支付方向（保险公司 → 外部平台）
  - 结算账户：配置收付款的银行账户信息
  - 账户验证：实时验证账户信息的有效性
  - 备用账户：配置备用结算账户，确保结算的连续性

- **结算规则配置**
  - 结算金额计算：配置保险理赔金额的计算规则
  - 税费处理：设置税费的计算和处理方式
  - 汇率处理：配置多币种结算时的汇率计算规则
  - 精度设置：设置金额计算的小数位精度

_阶梯式服务费结算规则配置_

- **基础配置**

  - 规则信息：同基础月保险理赔结算规则的基础信息配置
  - 结算类型：固定为服务费结算
  - 计算基数：选择以订单量或交易金额作为阶梯计算依据

- **阶梯费率配置表**

  - 动态表格：支持添加、删除、排序阶梯配置行
  - 交易额区间：设置不同的月交易额范围，支持自定义区间
  - 服务费率：为每个区间配置对应的费率百分比
  - 费率验证：实时验证费率设置的合理性和一致性
  - 阶梯预览：图表形式展示阶梯费率的分布情况

- **高级配置**
  - 封顶金额：设置服务费的最高限额
  - 保底金额：设置服务费的最低保障金额
  - 特殊处理：配置特殊情况下的费率调整规则
  - 试算功能：输入交易额进行费率试算和验证

**4. 审核与监控配置**

_审核流程配置_

- 审核节点：配置结算数据的多级审核流程
- 审核权限：设置不同角色的审核权限和范围
- 审核时限：设置各审核节点的时间要求
- 异常升级：配置审核异常时的升级处理机制

_监控预警设置_

- 异常监控：配置结算异常的自动检测规则
- 预警阈值：设置各类异常情况的预警阈值
- 通知方式：配置邮件、短信、系统消息等通知渠道
- 处理流程：定义异常发现后的标准处理流程

**5. 数据管理与分析**

_历史数据管理_

- 数据查询：支持按时间、平台、金额等多维度查询历史结算数据
- 数据导出：提供 Excel、PDF 等格式的数据导出功能
- 对账功能：支持与外部平台进行自动和手动数据对账
- 数据归档：配置历史数据的归档和清理规则

_报表与分析_

- 结算报表：自动生成各类结算统计报表
- 趋势分析：展示结算数据的趋势变化和异常波动
- 效益分析：分析不同结算规则的成本效益
- 对比分析：支持不同时期、不同平台的数据对比

**6. 底部操作区域**

_基础操作_

- 保存草稿：保存当前配置为草稿状态
- 预览配置：预览完整的规则配置信息
- 规则测试：使用历史数据测试规则的正确性
- 提交审核：将配置提交审核流程

_高级操作_

- 规则复制：基于现有规则创建新规则
- 批量导入：通过 Excel 模板批量导入规则配置
- 配置对比：对比不同版本规则配置的差异
- 回滚操作：回滚到历史版本的规则配置

**交互体验设计：**

_响应式设计_

- 自适应布局：支持不同屏幕尺寸的自适应显示
- 移动端优化：提供移动端的简化操作界面
- 快捷键支持：提供常用操作的键盘快捷键

_用户体验优化_

- 智能提示：提供配置过程中的智能提示和建议
- 进度指示：显示复杂配置流程的完成进度
- 自动保存：定期自动保存配置进度，防止数据丢失
- 操作撤销：支持配置操作的撤销和重做

_性能优化_

- 懒加载：大数据量时采用分页和懒加载技术
- 缓存机制：缓存常用配置数据，提升响应速度
- 异步处理：复杂计算采用异步处理，避免界面卡顿

**使用说明：**

**1. 基础月保险理赔结算规则配置流程**

- 选择规则类型 → 填写基础信息 → 配置结算周期 → 设置结算对象 → 配置结算规则 → 设置审核监控 → 预览测试 → 提交审核 → 生效执行

**2. 阶梯式服务费结算规则配置流程**

- 选择阶梯规则 → 填写基础配置 → 配置阶梯费率表 → 设置高级参数 → 配置审核流程 → 试算验证 → 预览确认 → 提交审核 → 生效执行

**3. 规则维护管理流程**

- 定期检查规则状态 → 分析结算数据效果 → 优化规则参数 → 处理异常情况 → 更新规则配置 → 生成维护报告

**4. 数据对账流程**

- 选择对账时间范围 → 导出系统结算数据 → 获取外部平台数据 → 执行自动对账 → 处理差异数据 → 生成对账报告 → 确认结算结果

系统提供完整的结算规则配置和管理功能，确保与外部支付平台结算的准确性和高效性。配置完成后，系统将根据规则自动执行结算流程，生成结算单据，并提供完整的审计跟踪记录。所有规则变更都有完整的版本管理和审批流程，确保结算配置的安全性和合规性。
