# RuleDefinitionDTO API测试文档

## 1. 概述

本文档描述了RuleDefinitionDTO API的完整测试方案，基于`com.open_care.dto.huagui.rule.RuleDefinitionDTO`实体进行测试。测试类继承`BaseRestAssuredTest`，使用RestAssured框架进行API测试，使用基于真实数据库规则配置的mock数据。

### 测试覆盖范围

- 基本CRUD操作（创建、查询、获取详情、删除）
- 字段验证和校验注解测试（@NotBlank, @NotNull, @Size等）
- 枚举值校验测试
- 业务规则验证（优先级、时间范围等）
- 分页、排序、过滤功能
- 批量操作和异常处理
- 基于真实数据库规则配置的mock数据测试

测试数据基于真实的数据库规则配置，包括直付品类规则、计费规则、结算规则等。

## 2. 测试实体结构

### 2.1 RuleDefinitionDTO字段说明

| 字段名 | 类型 | 说明 | 示例值 | 校验注解 |
|--------|------|------|--------|----------|
| ruleName | String | 规则名称 | 直付品类规则_abc123 | @NotBlank, @Size(max=64) |
| ruleDescription | String | 规则描述 | 基于药品类别的直付判断规则 | @Size(max=1024) |
| ruleType | RuleTypeEnum | 规则类型 | DIRECT_PAY_CATEGORY | @NotNull |
| ruleExpression | OCQueryBuilderRuleExpression | 规则表达式 | 复杂的查询构建器表达式对象 | - |
| priority | Integer | 优先级 | 1 | @Min(0) |
| effectiveTime | String | 生效时间 | 2024-01-01 00:00:00 | - |
| expiryTime | String | 失效时间 | 2025-12-31 23:59:59 | - |
| status | RuleStatusEnum | 规则状态 | ACTIVE | @NotNull |
| scope | RuleScopeEnum | 适用范围 | SYSTEM | @NotNull |
| directPayCategoryConfig | DirectPayCategoryRuleConfigDTO | 直付品类规则配置 | 包含允许的药品类别等 | @Valid |
| billingConfig | BillingRuleConfigDTO | 计费规则配置 | 包含覆盖率、计费方式等 | @Valid |
| settlementConfig | SettlementRuleConfigDTO | 结算规则配置 | 包含服务费率、结算方式等 | @Valid |

### 2.2 OCQueryBuilderRuleExpression字段说明

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| description | String | 规则描述 | 药品直付品类判断规则 |
| contextClassName | String | 上下文类名 | com.open_care.huagui.drug.DrugCategory |
| treeJsonValue | String | 前端展示的树形JSON | 复杂的规则树结构JSON |
| jsonLogicValue | String | JsonLogic表达式 | {"and":[{"in":[{"var":"drugCategory"},["CLASS_A","CLASS_B","CLASS_C"]]},{"==":[{"var":"isDirectPayAllowed"},true]}]} |

## 3. 测试数据来源

测试数据基于数据库中真实的规则配置表，通过MCP查询获取示例数据：

### 3.1 直付品类规则 (ocdirect_pay_category_rule)

**规则表达式示例：**
```
drugCategory IN ['CLASS_A', 'CLASS_B', 'CLASS_C'] AND isDirectPayAllowed = true
```

**JSON逻辑值：**
```json
{
  "and": [
    {
      "in": [
        {"var": "drugCategory"},
        ["CLASS_A", "CLASS_B", "CLASS_C"]
      ]
    },
    {
      "==": [
        {"isDirectPayAllowed": ["true"]},
        true
      ]
    }
  ]
}
```

该规则用于判断药品类别是否允许直付，支持甲类、乙类、丙类药品的直付判断。

### 3.2 计费规则 (ocbilling_rule)

**规则表达式示例：**
```
drugNumber = '132312建军' AND updateCoverageRatio(101) = true
```

**JSON逻辑值：**
```json
{
  "and": [
    {
      "==": [
        {"var": "drugNumber"},
        "132312建军"
      ]
    },
    {
      "==": [
        {"updateCoverageRatio": [101]},
        true
      ]
    }
  ]
}
```

该规则用于根据药品编号更新覆盖率，当药品编号匹配时执行覆盖率更新操作。

### 3.3 结算规则 (ocsettlement_rule)

**规则表达式示例：**
```
transactionVolume = 123 AND updateServiceRate(11) = true
```

**JSON逻辑值：**
```json
{
  "and": [
    {
      "==": [
        {"var": "transactionVolume"},
        123
      ]
    },
    {
      "==": [
        {"updateServiceRate": [11]},
        true
      ]
    }
  ]
}
```

该规则用于根据交易量更新服务费率，当月交易总额为123时，设置服务费率为11。

## 4. 测试用例详细说明

### 4.1 基本CRUD操作测试

| 测试方法 | 测试目的 | 验证点 | 预期结果 |
|----------|----------|--------|----------|
| testSaveRuleDefinition | 测试保存RuleDefinition | 1. 保存成功<br>2. 返回数据正确<br>3. 关联实体数据正确 | HTTP 200, status=success, 返回完整实体数据 |
| testQueryRuleDefinitionList | 测试查询RuleDefinition列表 | 1. 查询成功<br>2. 分页信息正确<br>3. 过滤条件生效<br>4. 关联实体数据正确 | 返回分页数据，过滤条件生效 |
| testGetRuleDefinition | 测试获取RuleDefinition详情 | 1. 获取成功<br>2. 返回数据正确<br>3. 字段值匹配<br>4. 关联实体数据正确 | 返回指定ID的完整实体数据 |
| testDeleteRuleDefinition | 测试删除RuleDefinition | 1. 删除成功<br>2. 删除后无法查询 | 删除成功，再次查询返回错误 |

### 4.2 字段验证和校验注解测试

| 测试方法 | 测试目的 | 验证点 | 预期结果 |
|----------|----------|--------|----------|
| testSaveRuleDefinitionValidation | 测试必填字段校验 | 1. 缺少必填字段时保存失败<br>2. 返回相应错误信息 | HTTP 200, status=error, 返回错误信息 |
| testFieldLengthBoundaryValidation | 测试字段长度边界值校验 | 1. 边界值数据保存成功<br>2. 超出边界值时保存失败 | 边界值成功，超出边界失败 |
| testEnumValidation | 测试枚举值校验 | 1. 有效枚举值保存成功<br>2. 无效枚举值保存失败 | 有效枚举成功，无效枚举失败 |
| testBusinessRuleValidation | 测试业务规则校验 | 1. 优先级不能为负数<br>2. 生效时间不能晚于失效时间 | 业务规则校验生效 |
| testClearField | 测试字段可清空校验 | 1. 字段先有值，再清空后保存成功<br>2. 查询详情确认字段已清空 | 字段成功清空，更新后查询为空 |
| testBatchSaveRuleDefinition | 测试批量保存 | 1. 批量保存成功<br>2. 返回数据正确 | 批量保存多条记录成功 |

### 4.3 查询功能测试

| 测试方法 | 测试目的 | 验证点 | 预期结果 |
|----------|----------|--------|----------|
| testRuleTypeQuery | 测试规则类型查询功能 | 1. 通过规则类型字段查询<br>2. 查询结果正确<br>3. 关联属性正确加载 | 按规则类型过滤成功 |
| testSorting | 测试排序功能 | 1. 升序排序正确<br>2. 降序排序正确 | 排序功能正常，结果按预期排列 |
| testPagination | 测试分页功能 | 1. 分页数据正确<br>2. 分页元数据正确 | 分页查询正常，数据不重复 |

## 5. 测试数据构造方法

### 5.1 createRuleDefinitionTestData() - 直付品类规则数据

该方法创建基于真实RuleDefinitionDTO字段的直付品类规则测试数据：

- **规则名称：** 直付品类规则_[唯一ID]
- **规则描述：** 基于药品类别的直付判断规则，支持甲类、乙类、丙类药品的直付判断
- **规则类型：** DIRECT_PAY_CATEGORY（枚举值）
- **规则表达式：** 使用真实的OCQueryBuilderRuleExpression对象，包含完整的treeJsonValue和jsonLogicValue
- **优先级：** 1
- **生效时间：** 2024-01-01 00:00:00
- **失效时间：** 2025-12-31 23:59:59
- **状态：** ACTIVE（枚举值）
- **适用范围：** SYSTEM（枚举值）
- **直付品类配置：** 包含allowedCategories、restrictedCategories、defaultAllowed等属性

### 5.2 createBillingRuleTestData() - 计费规则数据

该方法创建基于计费规则的测试数据：

- **规则名称：** 计费规则_[唯一ID]
- **规则描述：** 基于药品编号的覆盖率更新规则
- **规则类型：** BILLING_RULE（枚举值）
- **规则表达式：** 使用真实的OCQueryBuilderRuleExpression对象
- **计费配置：** 包含coverageRatio、drugNumber、billingMethod等属性

### 5.3 createSettlementRuleTestData() - 结算规则数据

该方法创建基于结算规则的测试数据：

- **规则名称：** 结算规则_[唯一ID]
- **规则描述：** 基于交易量的服务费率更新规则
- **规则类型：** SETTLEMENT_RULE（枚举值）
- **规则表达式：** 使用真实的OCQueryBuilderRuleExpression对象
- **结算配置：** 包含serviceRate、transactionVolume、settlementMethod等属性

### 5.4 createInvalidRuleDefinitionData() - 无效数据构造

该方法创建用于校验注解测试的无效数据：

- **空规则名称：** 测试@NotBlank校验
- **空规则类型：** 测试@NotNull校验
- **超长描述：** 测试@Size校验（超过1024字符）
- **负优先级：** 测试业务规则校验
- **无效时间格式：** 测试时间格式校验

### 5.5 createBoundaryRuleDefinitionData() - 边界值数据构造

该方法创建用于边界值测试的数据：

- **规则名称：** 正好64个字符（边界值）
- **规则描述：** 正好1024个字符（边界值）
- **最小优先级：** 0（边界值）

## 6. 测试执行说明

### 6.1 测试环境要求

- Spring Boot应用正常启动
- 数据库连接正常
- RestAssured测试框架配置正确
- BaseRestAssuredTest基类可用
- 相关DTO类和工具类已加载

### 6.2 执行命令

**执行单个测试方法：**
```bash
./gradlew test --tests "com.open_care.test.api.RuleDefinitionDTOApiTest.testSaveRuleDefinition"
```

**执行整个测试类：**
```bash
./gradlew test --tests "com.open_care.test.api.RuleDefinitionDTOApiTest"
```

**执行所有API测试：**
```bash
./gradlew test --tests "com.open_care.test.api.*"
```

### 6.3 注意事项

- 测试运行时间可能较长，需要启动完整的Spring Boot应用上下文
- 确保数据库中有足够的测试数据空间
- 某些测试方法之间存在依赖关系，建议按顺序执行
- 测试数据使用唯一ID生成，避免数据冲突
- 测试完成后会自动清理创建的测试数据

## 7. 测试覆盖范围总结

本测试套件全面覆盖了RuleDefinitionDTO的API功能：

✅ **基本CRUD操作**（创建、读取、更新、删除）
✅ **校验注解测试**（@NotBlank、@NotNull、@Size、@Valid等）
✅ **字段长度边界值测试**（64字符、1024字符等边界值）
✅ **枚举值校验测试**（RuleTypeEnum、RuleStatusEnum、RuleScopeEnum）
✅ **业务规则验证**（优先级范围、时间逻辑等）
✅ **复杂对象处理**（OCQueryBuilderRuleExpression、配置DTO等）
✅ **查询功能**（过滤、排序、分页）
✅ **批量操作支持**（多种规则类型的批量保存）
✅ **异常情况处理**（无效数据、边界条件等）

### 测试数据特点

- **基于真实配置**：使用MCP查询的真实数据库规则配置
- **多种规则类型**：覆盖直付品类、计费、结算三种主要规则类型
- **完整字段覆盖**：包含所有RuleDefinitionDTO字段的测试
- **校验注解全覆盖**：测试所有相关的Bean Validation注解
- **边界值测试**：包含字段长度、数值范围等边界条件测试

测试数据基于真实的数据库规则配置，确保测试的有效性和实用性。所有测试用例都遵循项目的测试规范，使用RestAssured框架进行API测试，提供了完整的验证覆盖。

---

## 附录：测试代码关键片段

### A.1 测试类声明

```java
/**
 * RuleDefinitionDTO API测试类
 * 基于com.open_care.dto.huagui.rule.RuleDefinitionDTO进行测试
 * 使用基于真实数据库规则配置的mock数据
 */
@DisplayName("RuleDefinitionDTO API测试类")
public class RuleDefinitionDTOApiTest extends BaseRestAssuredTest {

    private String ocId;
    private Map<String, Object> testData;
    private static final String ENTITY_NAME = "com.open_care.dto.huagui.rule.RuleDefinitionDTO";

    // 基于MCP查询的真实数据库规则配置
    private static final List<Map<String, Object>> REAL_RULE_CONFIGS = Arrays.asList(
        createDirectPayCategoryConfig(),
        createBillingRuleConfig(),
        createSettlementRuleConfig()
    );

    @BeforeEach
    public void setup() {
        prepareTestData();
    }
}
```

### A.2 保存测试示例

```java
@Test
@DisplayName("测试保存RuleDefinition")
public void testSaveRuleDefinition() {
    // 构建保存请求
    SaveRequestDTO saveRequest = new SaveRequestDTO();
    saveRequest.setEntityName(ENTITY_NAME);
    saveRequest.setData(testData);
    saveRequest.setEagerProperties("weightedRelations");

    // 发送请求并验证结果
    Response response = given()
            .spec(requestSpec)
            .body(saveRequest)
            .when()
            .post(API_PATH + SAVE_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
            .body(FIELD_DATA, notNullValue())
            .body("data.ruleName", equalTo(testData.get("ruleName")))
            .body("data.ruleType", equalTo(testData.get("ruleType")))
            .body("data.ruleDescription", equalTo(testData.get("ruleDescription")))
            .body("data.priority", equalTo(testData.get("priority")))
            .body("data.status", equalTo(testData.get("status")))
            .body("data.scope", equalTo(testData.get("scope")))
            .body("data.ruleExpression", notNullValue())
            .body("data.directPayCategoryConfig", notNullValue())
            .extract()
            .response();

    // 保存创建的ID，用于后续测试
    ocId = response.jsonPath().getString("data.ocId");
    assertNotNull(ocId, "创建的RuleDefinition ID不应为空");
}
```

### A.3 查询测试示例

```java
@Test
@DisplayName("测试查询RuleDefinition列表")
public void testQueryRuleDefinitionList() {
    // 构建查询请求
    QueryRequestDTO queryRequest = new QueryRequestDTO();
    queryRequest.setEntityName(ENTITY_NAME);

    // 设置过滤条件
    List<FieldData> filters = new ArrayList<>();
    FieldData ruleTypeFilter = new FieldData();
    ruleTypeFilter.setFieldName("ruleType");
    ruleTypeFilter.setOperator("equal");
    ruleTypeFilter.setFieldValue(testData.get("ruleType"));
    filters.add(ruleTypeFilter);
    queryRequest.setFilters(filters);

    // 设置分页信息
    queryRequest.setPagination(QueryUtils.page(DEFAULT_PAGE_SIZE, DEFAULT_PAGE_NUM));

    // 设置排序
    Map<String, String> sorter = new HashMap<>();
    sorter.put("ocId", "descend");
    queryRequest.setSorter(sorter);

    // 设置关联属性
    queryRequest.setEagerProperties("ruleExpression,directPayCategoryConfig");

    // 发送请求并验证结果
    given()
        .spec(requestSpec)
        .body(queryRequest)
        .when()
        .post(API_PATH + QUERY_ENDPOINT)
        .then()
        .statusCode(HttpStatus.OK.value())
        .body(FIELD_STATUS, equalTo(STATUS_SUCCESS))
        .body(FIELD_DATA_DATA, notNullValue())
        .body(FIELD_DATA_PAGINATION, notNullValue())
        .body("data.data[0].ruleName", equalTo(testData.get("ruleName")))
        .body("data.data[0].ruleExpression", notNullValue());
}
```

### A.4 测试数据构造示例

```java
/**
 * 创建RuleDefinition测试数据
 * 基于真实的RuleDefinitionDTO字段和数据库规则配置
 */
private Map<String, Object> createRuleDefinitionTestData() {
    Map<String, Object> data = new HashMap<>();

    // 生成唯一标识
    String uniqueId = UUID.randomUUID().toString().substring(0, 8);

    // ========== 通用规则属性 ==========
    data.put("ruleName", "直付品类规则_" + uniqueId);
    data.put("ruleDescription", "基于药品类别的直付判断规则，支持甲类、乙类、丙类药品的直付判断");
    data.put("ruleType", "DIRECT_PAY_CATEGORY"); // 使用枚举值

    // 使用真实的OCQueryBuilderRuleExpression数据
    Map<String, Object> directPayConfig = REAL_RULE_CONFIGS.get(0);
    data.put("ruleExpression", directPayConfig.get("ruleExpression"));

    data.put("priority", 1);
    data.put("effectiveTime", "2024-01-01 00:00:00");
    data.put("expiryTime", "2025-12-31 23:59:59");
    data.put("status", "ACTIVE"); // 使用枚举值
    data.put("scope", "SYSTEM"); // 使用枚举值

    // ========== 特定规则类型属性对象 ==========
    // 直付品类规则属性
    Map<String, Object> directPayCategoryConfig = new HashMap<>();
    directPayCategoryConfig.put("allowedCategories", Arrays.asList("CLASS_A", "CLASS_B", "CLASS_C"));
    directPayCategoryConfig.put("restrictedCategories", Arrays.asList("CLASS_D"));
    directPayCategoryConfig.put("defaultAllowed", true);
    data.put("directPayCategoryConfig", directPayCategoryConfig);

    return data;
}
```

### A.5 校验注解测试示例

```java
/**
 * 测试字段长度边界值校验
 */
@Test
@DisplayName("测试字段长度边界值校验")
public void testFieldLengthBoundaryValidation() {
    // 测试边界值数据 - 应该成功
    Map<String, Object> boundaryData = createBoundaryRuleDefinitionData();

    SaveRequestDTO saveRequest = new SaveRequestDTO();
    saveRequest.setEntityName(ENTITY_NAME);
    saveRequest.setData(boundaryData);

    given()
            .spec(requestSpec)
            .body(saveRequest)
            .when()
            .post(API_PATH + SAVE_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body(FIELD_STATUS, equalTo(STATUS_SUCCESS));

    // 测试超出边界值的数据 - 应该失败
    Map<String, Object> oversizeData = createBoundaryRuleDefinitionData();
    oversizeData.put("ruleName", "a".repeat(65)); // 超过64字符限制

    saveRequest.setData(oversizeData);

    given()
            .spec(requestSpec)
            .body(saveRequest)
            .when()
            .post(API_PATH + SAVE_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body(FIELD_STATUS, equalTo(STATUS_ERROR))
            .body(FIELD_MSG, containsString("长度"));
}

/**
 * 测试枚举值校验
 */
@Test
@DisplayName("测试枚举值校验")
public void testEnumValidation() {
    // 测试无效的规则类型枚举值
    Map<String, Object> invalidEnumData = createRuleDefinitionTestData();
    invalidEnumData.put("ruleType", "INVALID_RULE_TYPE");

    SaveRequestDTO saveRequest = new SaveRequestDTO();
    saveRequest.setEntityName(ENTITY_NAME);
    saveRequest.setData(invalidEnumData);

    given()
            .spec(requestSpec)
            .body(saveRequest)
            .when()
            .post(API_PATH + SAVE_ENDPOINT)
            .then()
            .statusCode(HttpStatus.OK.value())
            .body(FIELD_STATUS, equalTo(STATUS_ERROR));
}
```

---

## 总结

本测试文档详细描述了RuleDefinitionDTO API的完整测试方案，包含了13个主要测试方法，覆盖了以下方面：

### 主要测试方法

1. **testSaveRuleDefinition** - 基本保存功能测试
2. **testQueryRuleDefinitionList** - 列表查询功能测试
3. **testGetRuleDefinition** - 详情获取功能测试
4. **testDeleteRuleDefinition** - 删除功能测试
5. **testSaveRuleDefinitionValidation** - 必填字段校验测试
6. **testFieldLengthBoundaryValidation** - 字段长度边界值校验测试
7. **testEnumValidation** - 枚举值校验测试
8. **testBusinessRuleValidation** - 业务规则校验测试
9. **testBatchSaveRuleDefinition** - 批量保存功能测试
10. **testClearField** - 字段清空功能测试
11. **testRuleTypeQuery** - 规则类型查询功能测试

### 测试特色

- **真实数据驱动**：基于MCP查询的真实数据库规则配置
- **完整校验覆盖**：包含所有Bean Validation注解的测试
- **多规则类型支持**：覆盖直付品类、计费、结算三种规则类型
- **边界值测试**：包含字段长度、数值范围等边界条件
- **业务规则验证**：验证优先级、时间逻辑等业务约束

测试数据基于真实的数据库规则配置，确保了测试的实用性和有效性。所有测试用例都遵循项目的测试规范，使用RestAssured框架进行API测试。

**文档创建时间：** 2025年1月25日
**作者：** 刘雄风
**测试类路径：** `com.open_care.test.api.RuleDefinitionDTOApiTest`
**实体路径：** `com.open_care.dto.huagui.rule.RuleDefinitionDTO`
