/**
 * Copyright 2018-2024 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
import axios from './axios';
import store from '../store';
const PRODUCTION = process.env.NODE_ENV === 'production';
const API_PATH = '/ui-designer';
// const proxyURL = '';
const proxyURL = PRODUCTION ? API_PATH : 'http://192.168.1.3:8301';
// const proxy = '/api';
const proxy = PRODUCTION ? `${API_PATH}/api` : 'http://192.168.1.3:8301/api';
const access_token = `eyJhbGciOiJSUzI1NiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************.L_pl9hwCxEVA8tjm09QMxHhwRVhMXN3Tze5UHZy1pAHU-9w2u31OQE9_xHxf2NwokhA-7q1_VlEKe-wiWBr7rdCxjDPSQjoa-w8ePHurvnvc8uXsL8ohPrKbzYTdAAkJoSjT08Lzgi0Kb1qnLuXBqq8ZdUJZb8-EM6HPnujWsZLbUR3BV5o8LokZ1i2AF8SRAXIshpu8b9C93N7lFkEEE5Pz1NKghg0Xd2BIawkGBJeuhxXUCEl1YrPBEOCF87w8DkuusW4bnVx9nsgAW4liXrJ5QKx03Uk77sSrDBc7LHpjalWTS-HWhNfO6ZZ2Atic7Lfemzp4VFnW4DcEZtQYvw`;
// const access_token = `?access_token=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`; 
const AuthStr = 'Bearer '.concat(access_token);
// const Authorization = PRODUCTION ? {} : { Authorization: AuthStr }
const Authorization = {  }

const isDevelopment = process.env.NODE_ENV === 'development';

export async function listAllEntityAndFields() {
    const { appContext, isQuestionnaireApp } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    if (!isDevelopment && !isQuestionnaireApp) {
        return await axios.get(`${proxy}/listAllEntityAndFields`, { headers , responseType: 'json' });
    }

    // mock 测试数据
    return {
        data: {
            "msg": "",
            "data": [
                {
                    "key": "cefef6cf-34ef-45f4-975a-efcb91e00ac3",
                    "value": "cefef6cf-34ef-45f4-975a-efcb91e00ac3",
                    "label": "参考列表",
                    "children": [
                        {
                            "key": "325ddc65-538b-426a-b492-008f84b243ca",
                            "value": "325ddc65-538b-426a-b492-008f84b243ca",
                            "label": "client",
                            "fieldType": "Input"
                        },
                        {
                            "key": "381f3ed9-4dde-4748-bb1b-65d08acb3549",
                            "value": "381f3ed9-4dde-4748-bb1b-65d08acb3549",
                            "label": "created",
                            "fieldType": "Input",
                            "fieldTitle": "创建时间"
                        },
                        {
                            "key": "41e124d0-6102-4401-9d4d-fc58917d9070",
                            "value": "41e124d0-6102-4401-9d4d-fc58917d9070",
                            "label": "createdby",
                            "fieldType": "Input",
                            "fieldTitle": "创建人"
                        },
                        {
                            "key": "bbf15d0a-520e-4a6c-8f1f-fa8b7c2c2283",
                            "value": "bbf15d0a-520e-4a6c-8f1f-fa8b7c2c2283",
                            "label": "id",
                            "fieldType": "Input"
                        },
                        {
                            "key": "73becee5-d1bd-412b-9b84-9b025714598e",
                            "value": "73becee5-d1bd-412b-9b84-9b025714598e",
                            "label": "jsonSchemaData",
                            "fieldType": "Input"
                        },
                        {
                            "key": "14bbf93d-69d5-4bae-ac6d-055bd6eb713f",
                            "value": "14bbf93d-69d5-4bae-ac6d-055bd6eb713f",
                            "label": "key",
                            "fieldType": "Input",
                            "fieldTitle": "关键字"
                        },
                        {
                            "key": "f12cbb0f-ac75-4dcb-adbb-3c09805b84d5",
                            "value": "f12cbb0f-ac75-4dcb-adbb-3c09805b84d5",
                            "label": "org",
                            "fieldType": "Input"
                        },
                        {
                            "key": "85457810-fe9f-472c-87b4-5277673b32b3",
                            "value": "85457810-fe9f-472c-87b4-5277673b32b3",
                            "label": "pid",
                            "fieldType": "Input"
                        },
                        {
                            "key": "369e20a2-d090-4df7-9c07-b972982c54f0",
                            "value": "369e20a2-d090-4df7-9c07-b972982c54f0",
                            "label": "reference",
                            "fieldType": "Input"
                        },
                        {
                            "key": "2a023f5e-e677-48ff-9c3d-3610215e3a7a",
                            "value": "2a023f5e-e677-48ff-9c3d-3610215e3a7a",
                            "label": "value",
                            "fieldType": "Input",
                            "fieldTitle": "值"
                        }
                    ]
                },
                {
                    "key": "0a85c9cb-bb88-4b5b-9914-db120d37cefa",
                    "value": "0a85c9cb-bb88-4b5b-9914-db120d37cefa",
                    "label": "用户",
                    "children": [
                        {
                            "key": "b304a163-7683-40e6-8f96-61c26324c19e",
                            "value": "b304a163-7683-40e6-8f96-61c26324c19e",
                            "label": "client",
                            "fieldType": "Input"
                        },
                        {
                            "key": "b56a4aa5-9fb2-44cb-a100-0a0b3fae397122",
                            "value": "b56a4aa5-9fb2-44cb-a100-0a0b3fae397122",
                            "label": "others",
                            "fieldTitle": "其他",
                            "children": [
                                {
                                    // "key": "b56a4aa5-9fb2-44cb-a100-0a0b3fae3971",
                                    "value": "b56a4aa5-9fb2-44cb-a100-0a0b3fae3971",
                                    "label": "contacts",
                                    "fieldType": "Select",
                                    "fieldTitle": "联系方式",
                                    "children": [
                                        {
                                            "key": "d49d1709-28f6-4aad-a134-8482ffc3850c",
                                            "value": "d49d1709-28f6-4aad-a134-8482ffc3850c",
                                            "label": "jsonSchemaData",
                                            "fieldType": "Input"
                                        },
                                        {
                                            "key": "dc25cf2c-587b-4ae6-b12d-15a7ebc2314a",
                                            "value": "dc25cf2c-587b-4ae6-b12d-15a7ebc2314a",
                                            "label": "type",
                                            "fieldType": "Select"
                                        },
                                        {
                                            "key": "239d6d70-e8d4-472d-9e10-c3b7b3b8c871",
                                            "value": "239d6d70-e8d4-472d-9e10-c3b7b3b8c871",
                                            "label": "value",
                                            "fieldType": "Input"
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "key": "c4437c7a-9797-4348-b80b-a3f037099a7e",
                            "value": "c4437c7a-9797-4348-b80b-a3f037099a7e",
                            "label": "created",
                            "fieldType": "Input"
                        },
                        {
                            "key": "a96099ad-0ac7-47d9-9604-29aa63f0e28e",
                            "value": "a96099ad-0ac7-47d9-9604-29aa63f0e28e",
                            "label": "createdby",
                            "fieldType": "Input"
                        },
                        {
                            "key": "c4437c7a-9797-4348-b80b-a3f037099a7r",
                            "value": "c4437c7a-9797-4348-b80b-a3f037099a7r",
                            "label": "email",
                            "fieldType": "Input",
                            "fieldTitle": "邮箱"
                        },
                        {
                            "key": "4eef211f-34c3-4eec-a400-b96a45b5fb28",
                            "value": "4eef211f-34c3-4eec-a400-b96a45b5fb28",
                            "label": "id",
                            "fieldType": "Input"
                        },
                        {
                            "key": "9f8f69a8-9ebb-4742-abee-1161300602c5",
                            "value": "9f8f69a8-9ebb-4742-abee-1161300602c5",
                            "label": "jsonSchemaData",
                            "fieldType": "Input"
                        },
                        {
                            "key": "c4437c7a-9797-4348-b80b-a3f037099a7w",
                            "value": "c4437c7a-9797-4348-b80b-a3f037099a7w",
                            "label": "mobile",
                            "fieldType": "Input",
                            "fieldTitle": "手机号"
                        },
                        {
                            "key": "255083a3-3c55-4e3f-a48b-966e70107f27",
                            "value": "255083a3-3c55-4e3f-a48b-966e70107f27",
                            "label": "name",
                            "fieldType": "Input",
                            "fieldTitle": "用户名称"
                        },
                        {
                            "key": "1a97d42e-d1ba-4af1-8110-44e5e172aea2",
                            "value": "1a97d42e-d1ba-4af1-8110-44e5e172aea2",
                            "label": "roles",
                            "fieldType": "Select",
                            "fieldTitle": "角色",
                            "children": [
                                {
                                    "key": "1fdda845-46b0-41ba-8212-ceeab4225009",
                                    "value": "1fdda845-46b0-41ba-8212-ceeab4225009",
                                    "label": "appDefId",
                                    "fieldType": "Input"
                                },
                                {
                                    "key": "14ffe7e8-fb82-4fff-a56e-791a8d7d5a34",
                                    "value": "14ffe7e8-fb82-4fff-a56e-791a8d7d5a34",
                                    "label": "appInstId",
                                    "fieldType": "Input"
                                },
                                {
                                    "key": "1fef2e66-fba7-4676-b29e-7df323b33adf",
                                    "value": "1fef2e66-fba7-4676-b29e-7df323b33adf",
                                    "label": "client",
                                    "fieldType": "Input"
                                },
                                {
                                    "key": "1634769b-6ff2-44f4-9a8d-11781f0c0e2a",
                                    "value": "1634769b-6ff2-44f4-9a8d-11781f0c0e2a",
                                    "label": "created",
                                    "fieldType": "DatePicker"
                                },
                                {
                                    "key": "18e32aeb-3801-4d3d-90f8-fd02613e0498",
                                    "value": "18e32aeb-3801-4d3d-90f8-fd02613e0498",
                                    "label": "createdby",
                                    "fieldType": "Input"
                                },
                                {
                                    "key": "1bb8d909-37d3-42ee-9906-5a98a607baff",
                                    "value": "1bb8d909-37d3-42ee-9906-5a98a607baff",
                                    "label": "id",
                                    "fieldType": "Input"
                                },
                                {
                                    "key": "15d118d6-f09d-4905-9035-7a24bb8d1c68",
                                    "value": "15d118d6-f09d-4905-9035-7a24bb8d1c68",
                                    "label": "jsonSchemaData",
                                    "fieldType": "Input"
                                },
                                {
                                    "key": "14164816-91e9-4300-a987-21e64d41af11",
                                    "value": "14164816-91e9-4300-a987-21e64d41af11",
                                    "label": "key",
                                    "fieldType": "Input",
                                    "fieldTitle": "编码"
                                },
                                {
                                    "key": "14164816-91e9-4300-a987-21e64d41af1d",
                                    "value": "14164816-91e9-4300-a987-21e64d41af1d",
                                    "label": "name",
                                    "fieldType": "Input",
                                    "fieldTitle": "名称"
                                },
                                {
                                    "key": "18b96f5d-58bc-46cf-bc7a-815823353185",
                                    "value": "18b96f5d-58bc-46cf-bc7a-815823353185",
                                    "label": "ocSchemaId",
                                    "fieldType": "Input"
                                },
                                {
                                    "key": "19f6acde-c8de-4726-a1e7-741c585beef0",
                                    "value": "19f6acde-c8de-4726-a1e7-741c585beef0",
                                    "label": "org",
                                    "fieldType": "Input"
                                },
                                {
                                    "key": "161429b0-162e-47fb-8dbb-745876960198",
                                    "value": "161429b0-162e-47fb-8dbb-745876960198",
                                    "label": "tenantId",
                                    "fieldType": "Input"
                                }
                            ]
                        },
                        {
                            "key": "68f9f2a9-2afc-42fd-96b6-ed2ea9c6f378",
                            "value": "68f9f2a9-2afc-42fd-96b6-ed2ea9c6f378",
                            "label": "sex",
                            "fieldType": "Select",
                            "fieldTitle": "性别"
                        },
                        {
                            "key": "9f51e0a8-edf5-4c54-8b7c-eb0a634ae1c7",
                            "value": "9f51e0a8-edf5-4c54-8b7c-eb0a634ae1c7",
                            "label": "username",
                            "fieldType": "Input",
                            "fieldTitle": "登录名"
                        }
                    ]
                },
                {
                    "key": "e51e9fe8-4fdd-455d-b5f7-9ef02f790813",
                    "value": "e51e9fe8-4fdd-455d-b5f7-9ef02f790813",
                    "label": "角色",
                    "children": [
                        {
                            "key": "1fdda845-46b0-41ba-8212-ceeab4225009",
                            "value": "1fdda845-46b0-41ba-8212-ceeab4225009",
                            "label": "appDefId",
                            "fieldType": "Input"
                        },
                        {
                            "key": "14ffe7e8-fb82-4fff-a56e-791a8d7d5a34",
                            "value": "14ffe7e8-fb82-4fff-a56e-791a8d7d5a34",
                            "label": "appInstId",
                            "fieldType": "Input"
                        },
                        {
                            "key": "1fef2e66-fba7-4676-b29e-7df323b33adf",
                            "value": "1fef2e66-fba7-4676-b29e-7df323b33adf",
                            "label": "client",
                            "fieldType": "Input"
                        },
                        {
                            "key": "1634769b-6ff2-44f4-9a8d-11781f0c0e2a",
                            "value": "1634769b-6ff2-44f4-9a8d-11781f0c0e2a",
                            "label": "created",
                            "fieldType": "DatePicker"
                        },
                        {
                            "key": "18e32aeb-3801-4d3d-90f8-fd02613e0498",
                            "value": "18e32aeb-3801-4d3d-90f8-fd02613e0498",
                            "label": "createdby",
                            "fieldType": "Input"
                        },
                        {
                            "key": "1bb8d909-37d3-42ee-9906-5a98a607baff",
                            "value": "1bb8d909-37d3-42ee-9906-5a98a607baff",
                            "label": "id",
                            "fieldType": "Input"
                        },
                        {
                            "key": "15d118d6-f09d-4905-9035-7a24bb8d1c68",
                            "value": "15d118d6-f09d-4905-9035-7a24bb8d1c68",
                            "label": "jsonSchemaData",
                            "fieldType": "Input"
                        },
                        {
                            "key": "14164816-91e9-4300-a987-21e64d41af11",
                            "value": "14164816-91e9-4300-a987-21e64d41af11",
                            "label": "key",
                            "fieldType": "Input",
                            "fieldTitle": "编码"
                        },
                        {
                            "key": "14164816-91e9-4300-a987-21e64d41af1d",
                            "value": "14164816-91e9-4300-a987-21e64d41af1d",
                            "label": "name",
                            "fieldType": "Input",
                            "fieldTitle": "名称"
                        },
                        {
                            "key": "18b96f5d-58bc-46cf-bc7a-815823353185",
                            "value": "18b96f5d-58bc-46cf-bc7a-815823353185",
                            "label": "ocSchemaId",
                            "fieldType": "Input"
                        },
                        {
                            "key": "19f6acde-c8de-4726-a1e7-741c585beef0",
                            "value": "19f6acde-c8de-4726-a1e7-741c585beef0",
                            "label": "org",
                            "fieldType": "Input"
                        },
                        {
                            "key": "161429b0-162e-47fb-8dbb-745876960198",
                            "value": "161429b0-162e-47fb-8dbb-745876960198",
                            "label": "tenantId",
                            "fieldType": "Input"
                        }
                    ]
                },
                {
                    "key": "267faf10-3935-4044-b992-50719171b150",
                    "value": "267faf10-3935-4044-b992-50719171b150",
                    "label": "DirectPayInstitutionRuleConfigDTO",
                    "children": [
                        {
                            "key": "8e430039-db54-46df-931e-5106b32b0bbc",
                            "value": "8e430039-db54-46df-931e-5106b32b0bbc",
                            "label": "dynamicVolumeConfig",
                            "fieldType": "Input",
                            "fieldTitle": "动态单量配置",
                            "children": [
                                {
                                    "key": "8e430039-db54-46df-931e-5106b32b0bbc.ebe634fd-12a8-403a-bec8-1a5f7a9ac692",
                                    "value": "8e430039-db54-46df-931e-5106b32b0bbc.ebe634fd-12a8-403a-bec8-1a5f7a9ac692",
                                    "label": "allocationStrategy",
                                    "fieldType": "Select",
                                    "fieldTitle": "分配策略"
                                },
                                {
                                    "key": "8e430039-db54-46df-931e-5106b32b0bbc.b8ebb190-92f2-4375-ae4f-98e5e4d5c530",
                                    "value": "8e430039-db54-46df-931e-5106b32b0bbc.b8ebb190-92f2-4375-ae4f-98e5e4d5c530",
                                    "label": "platformConfigs",
                                    "fieldType": "Select",
                                    "fieldTitle": "平台配置列表",
                                    "children": [
                                        {
                                            "key": "8e430039-db54-46df-931e-5106b32b0bbc.b8ebb190-92f2-4375-ae4f-98e5e4d5c530.964f87c2-22c5-495f-8b24-04a58ba0c0e3",
                                            "value": "964f87c2-22c5-495f-8b24-04a58ba0c0e3",
                                            "label": "minimumRatio",
                                            "fieldType": "Input",
                                            "fieldTitle": "最小保障比例"
                                        },
                                        {
                                            "key": "8e430039-db54-46df-931e-5106b32b0bbc.b8ebb190-92f2-4375-ae4f-98e5e4d5c530.0e9223dd-88ab-4a60-89a2-a40b47b2a19d",
                                            "value": "0e9223dd-88ab-4a60-89a2-a40b47b2a19d",
                                            "label": "platformName",
                                            "fieldType": "Input",
                                            "fieldTitle": "平台名称"
                                        },
                                        {
                                            "key": "8e430039-db54-46df-931e-5106b32b0bbc.b8ebb190-92f2-4375-ae4f-98e5e4d5c530.917fc063-6ea8-4af3-a5a0-c326186210ae",
                                            "value": "917fc063-6ea8-4af3-a5a0-c326186210ae",
                                            "label": "targetRatio",
                                            "fieldType": "Input",
                                            "fieldTitle": "目标分配比例"
                                        }
                                    ]
                                },
                                {
                                    "key": "8e430039-db54-46df-931e-5106b32b0bbc.ae1c4411-6533-4a7b-875d-3bfc5a87d2e1",
                                    "value": "8e430039-db54-46df-931e-5106b32b0bbc.ae1c4411-6533-4a7b-875d-3bfc5a87d2e1",
                                    "label": "rebalanceThreshold",
                                    "fieldType": "Input",
                                    "fieldTitle": "重新平衡阈值"
                                },
                                {
                                    "key": "8e430039-db54-46df-931e-5106b32b0bbc.2bc8a24f-a9a5-43ea-bca4-63ddf697129a",
                                    "value": "8e430039-db54-46df-931e-5106b32b0bbc.2bc8a24f-a9a5-43ea-bca4-63ddf697129a",
                                    "label": "statisticsDimension",
                                    "fieldType": "Select",
                                    "fieldTitle": "统计维度"
                                },
                                {
                                    "key": "8e430039-db54-46df-931e-5106b32b0bbc.4f0e5124-d88d-419e-997a-6f01dd2ba7dc",
                                    "value": "8e430039-db54-46df-931e-5106b32b0bbc.4f0e5124-d88d-419e-997a-6f01dd2ba7dc",
                                    "label": "statisticsPeriod",
                                    "fieldType": "Select",
                                    "fieldTitle": "统计周期"
                                }
                            ]
                        },
                        {
                            "key": "63dddd3b-4f84-4a93-a7c1-17e55452f756",
                            "value": "63dddd3b-4f84-4a93-a7c1-17e55452f756",
                            "label": "fixedPriorityConfig",
                            "fieldType": "Input",
                            "fieldTitle": "固定优先级配置",
                            "children": [
                                {
                                    "key": "63dddd3b-4f84-4a93-a7c1-17e55452f756.42b6d45d-2445-4445-9ccf-404aa400cb7c",
                                    "value": "63dddd3b-4f84-4a93-a7c1-17e55452f756.42b6d45d-2445-4445-9ccf-404aa400cb7c",
                                    "label": "platformPriorities",
                                    "fieldType": "Select",
                                    "fieldTitle": "支付平台列表",
                                    "children": [
                                        {
                                            "key": "63dddd3b-4f84-4a93-a7c1-17e55452f756.42b6d45d-2445-4445-9ccf-404aa400cb7c.2403fe96-7abd-44b9-b2c3-8fd045498fd8",
                                            "value": "2403fe96-7abd-44b9-b2c3-8fd045498fd8",
                                            "label": "platformId",
                                            "fieldType": "Input"
                                        },
                                        {
                                            "key": "63dddd3b-4f84-4a93-a7c1-17e55452f756.42b6d45d-2445-4445-9ccf-404aa400cb7c.ed7e58aa-ca3a-4872-94bf-ab7a64a0f37e",
                                            "value": "ed7e58aa-ca3a-4872-94bf-ab7a64a0f37e",
                                            "label": "platformName",
                                            "fieldType": "Input",
                                            "fieldTitle": "平台名称"
                                        },
                                        {
                                            "key": "63dddd3b-4f84-4a93-a7c1-17e55452f756.42b6d45d-2445-4445-9ccf-404aa400cb7c.1c42b9bd-eb28-49d2-9a3a-143b59db7b47",
                                            "value": "1c42b9bd-eb28-49d2-9a3a-143b59db7b47",
                                            "label": "priority",
                                            "fieldType": "Input",
                                            "fieldTitle": "优先级"
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            "key": "456d13d4-8836-4ed3-955e-e28eb7b991b4",
                            "value": "456d13d4-8836-4ed3-955e-e28eb7b991b4",
                            "label": "strategy",
                            "fieldType": "Select",
                            "fieldTitle": "规则策略"
                        }
                    ]
                }
            ],
            "status": "0",
            "error": ""
        }
    }


}
export async function saveWindowMetaUIData({ data, windowId }) {
    const { appContext, currentPage: { pageMode } } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    if (windowId) {
        return await axios.put(`${proxy}/saveWindowMetaUIData/${windowId}?client=${pageMode}`, data, { headers, responseType: 'json' });
    } else {
        return await axios.post(`${proxy}/saveWindowMetaUIData?client=${pageMode}`, data, { headers, responseType: 'json' });
    }
}
export async function previewWindow(data) {
    const { appContext, currentPage: { pageMode } } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return await axios.post(`${proxy}/previewWindowMetaData?client=${pageMode}`, data, { headers, responseType: 'json' });

}

export async function getTags() {
    const { appContext,currentPage:{pageMode}, isQuestionnaireApp } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)
    if (!isDevelopment) {
        const requestBody = {
            "requestUrl": "/tag/queryAllUITagCategoryTagTitles",
            "httpMethod": "get",
            "uriVariables": {}
        }
        return axios.post(`${proxy}/restExchange`, requestBody, { headers , responseType: 'json' });
    }

    return {
        data: {
            status: '0',
            msg: '',
            data: [
                "tag1",
                "tag2",
                "tag3",
                "tag4",
                "标签",
                "V2022",
                "V88",
                "VIP",
                "GOGOGO",
                "自定义标签"
            ]
        }
    }

}

export async function queryCurrentBranch() {
  const { appContext } = store.getState(),
    headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);

  if (!isDevelopment) {
    const url = `${proxy}/branch/query/current`;

    return axios.get(url, { headers , responseType: 'json' })
      .then(({ data }) => data);
  }

  return new Promise((resolve) => {
    const data = {
      status: '0',
      msg: '',
      data: { branch_name: 'feature1' }
    };

    resolve(data);
  }).then(data => data);
}

export async function queryBranchIsShowOperationsRequest() {
  const { appContext } = store.getState(),
    headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);

  if (!isDevelopment) {
    const url = `${proxy}/branch/getSwitchStatus`;

    return axios.get(url, { headers , responseType: 'json' })
      .then(({ data }) => data);
  }

  return new Promise((resolve) => {
    const data = {
      status: '0',
      msg: '',
      data: { switchStatus: true }
    };

    resolve(data);
  }).then(data => data);
}

export async function queryBranches() {
  const { appContext } = store.getState(),
    headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);

  if (!isDevelopment) {
    const url = `${proxy}/branch/query/all`;

    return axios.get(url, { headers , responseType: 'json' })
      .then(({ data }) => data);
  }

  return new Promise((resolve) => {
    const data = {
      status: '0',
      msg: '',
      data: ['feature_linmindong_task3304', 'feature1-test', 'dev', 'master']
    };

    resolve(data);
  }).then(data => data);
}

export async function checkoutBranchRequest(branchName) {
  const { appContext } = store.getState(),
    headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);

  if (!isDevelopment) {
    const url = `${proxy}/branch/checkout/${branchName}`;

    return axios.post(url, null, { headers , responseType: 'json' })
      .then(({ data }) => data);
  }

  return new Promise((resolve) => {
    const data = {
      status: '0',
      msg: '切换成功',
      data: { 'branch_name': branchName }
    };

    resolve(data);
  }).then(data => data);
}

export async function refreshBranchRequest() {
  const { appContext } = store.getState(),
    headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);

  if (!isDevelopment) {
    const url = `${proxy}/branch/refresh`;

    return axios.post(url, {}, { headers , responseType: 'json' })
      .then(({ data }) => data);
  }

  return new Promise((resolve) => {
    const data = {
      status: '0',
      msg: '刷新成功',
      data: {}
    };

    resolve(data);
  }).then(data => data);
}

export async function queryImportLogDetailRequest(params) {
  const { appContext } = store.getState(),
    headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);

  if (!isDevelopment) {
    const { pagination, filters } = params;

    const url = `${proxy}/query`,
      body = {
        entityName: 'com.open_care.configuration.log.OCImportLogDetail',
        fieldSet: '{fileName,errMsg,status}',
        filters,
        pagination,
        sorter: { created: 'descend' }
      };

    return axios.post(url, body, { headers , responseType: 'json' })
      .then(({ data }) => data);
  }

  return new Promise((resolve) => {
    const response = {
      "msg": "",
      "data": {
        "pagination": {
          "pageSize": 10,
          "current": 1,
          "total": 22
        },
        "summary": {
          "record_total": 22
        },
        "data": [
          {
            "__class": "com.open_care.configuration.log.OCImportLog",
            "__jsog_id": "1",
            "id": "6bf9d7aa-d95e-47f0-b12f-fc2d9af3bcf7",
            "client": "open-care",
            "tenantId": "0",
            "appDefId": "0",
            "appInstId": "0",
            "created": "2023-08-17 16:01:23",
            "updated": "2023-08-17 16:02:01",
            "fileName": "文件名称test",
            "startTime": "2023-08-17T16:01:23.653+0800",
            "endTime": "2023-08-17T16:02:01.927+0800",
            "status": "success",
            "errMsg": "初始化成功",
            "importLogDetails": [],
            "_rowid": "6bf9d7aa-d95e-47f0-b12f-fc2d9af3bcf7",
            "_entityName": "com.open_care.configuration.log.OCImportLog"
          }
        ],
        "authority": []
      },
      "status": "0",
      "error": ""
  };

    resolve(response);
  }).then(response => response);
}

export async function queryImportLogsRequest(params) {
  const { appContext } = store.getState(),
    headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);

  if (!isDevelopment) {
    const { pagination, filters } = params,
      defaultFilters = [
        {
          fieldName: 'oc_import_log_type',
          fieldValue: 'import_branch_to_db',
          operator: 'equal'
        }
      ];

    const url = `${proxy}/query`,
      body = {
        entityName: 'com.open_care.configuration.log.OCImportLog',
        fieldSet: '{fileName,created,errMsg,status}',
        filters: defaultFilters.concat(filters),
        pagination,
        sorter: { created: 'descend' }
      };

    return axios.post(url, body, { headers , responseType: 'json' })
      .then(({ data }) => data);
  }

  return new Promise((resolve) => {
    const response = {
      "msg": "",
      "data": {
        "pagination": {
          "pageSize": 10,
          "current": 1,
          "total": 22
        },
        "summary": {
          "record_total": 22
        },
        "data": [
          {
            "__class": "com.open_care.configuration.log.OCImportLog",
            "__jsog_id": "1",
            "id": "6bf9d7aa-d95e-47f0-b12f-fc2d9af3bcf7",
            "client": "open-care",
            "tenantId": "0",
            "appDefId": "0",
            "appInstId": "0",
            "created": "2023-08-17 16:01:23",
            "updated": "2023-08-17 16:02:01",
            "fileName": "init Schema",
            "startTime": "2023-08-17T16:01:23.653+0800",
            "endTime": "2023-08-17T16:02:01.927+0800",
            "status": "success",
            "errMsg": "初始化成功",
            "importLogDetails": [],
            "_rowid": "6bf9d7aa-d95e-47f0-b12f-fc2d9af3bcf7",
            "_entityName": "com.open_care.configuration.log.OCImportLog"
          },
          {
            "__class": "com.open_care.configuration.log.OCImportLog",
            "__jsog_id": "1",
            "id": "68ea9b7d-f4a6-490a-8488-9c957d375819",
            "client": "open-care",
            "tenantId": "0",
            "appDefId": "0",
            "appInstId": "0",
            "created": "2023-08-17 14:26:08",
            "updated": "2023-08-17 14:26:44",
            "fileName": "init Schema",
            "startTime": "2023-08-17T14:26:08.556+0800",
            "endTime": "2023-08-17T14:26:44.000+0800",
            "status": "failure",
            "errMsg": "java.lang.IllegalStateException: 根据tkpsms未找到应用记",
            "importLogDetails": [],
            "_rowid": "68ea9b7d-f4a6-490a-8488-9c957d375819",
            "_entityName": "com.open_care.configuration.log.OCImportLog"
          }
        ],
        "authority": []
      },
      "status": "0",
      "error": ""
  };

    resolve(response);
  }).then(response => response);
}

export async function windowList() {
    const { appContext,currentPage:{pageMode}, isQuestionnaireApp } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)
    if (!isDevelopment) {
        return axios.get(`${proxy}/windowList?client=${pageMode}`, { headers , responseType: 'json' });
    }

    return {
        data: {
            status: '0',
            msg: '',
            data: [
                {
                    "windowId": "ed6ce525-aa0c-40a6-a538-df1cb7aa14891",
                    "title": "Graphql查询1Graphql查询1",
                    "type": "pc"
                },
                {
                    "windowId": "ed6ce525-aa0c-40a6-a538-df1cb7aa14892",
                    "title": "Graphql查询2",
                    "type": "pc"
                },{
                    "windowId": "ed6ce525-aa0c-40a6-a538-df1cb7aa14893",
                    "title": "Graphql查询3Graphql查询3",
                    "type": "pc"
                },{
                    "windowId": "ed6ce525-aa0c-40a6-a538-df1cb7aa14894",
                    "title": "Graphql查询4",
                    "type": "pc"
                },{
                    "windowId": "ed6ce525-aa0c-40a6-a538-df1cb7aa14895",
                    "title": "Graphql查询5",
                    "type": "pc"
                },{
                    "windowId": "ed6ce525-aa0c-40a6-a538-df1cb7aa14896",
                    "title": "Graphql查询6",
                    "type": "pc"
                },{
                    "windowId": "ed6ce525-aa0c-40a6-a538-df1cb7aa14897",
                    "title": "Graphql查询7",
                    "type": "pc"
                }
            ]
        }
    }
    
}

export async function templateList() {
    const { appContext,currentPage:{pageMode} } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return axios.get(`${proxy}/getTemplateList`, { headers , responseType: 'json' });
    // return {
    //     data: {
    //         status: '0',
    //         msg: '',
    //         data: [
    //           {
    //             templateTitle: '通用列表和编辑模板', // 模板名称
    //             templateId: '90zi-al9c-lkdi-3904',  // 模板id
    //             builtIn: true, // 是否是内置模板，内置模板不可删除
    //             windows: [
    //               {
    //                 windowId: '5d364379-8cea-4dd5-a6ad-fb7c50447e44',
    //                 windowTitle: '通用列表页'
    //               },
    //               {
    //                 windowId: 'ab07105f-7420-4fc4-bc45-3990bb7ceaf7',
    //                 windowTitle: '通用编辑页'
    //               }
    //             ]
    //           },
    //           {
    //             templateTitle: '自定义模板001',
    //             templateId: 'custom-al9c-lkdi-3905',
    //             builtIn: false,
    //             windows: [
    //               {
    //                 windowId: '3162686a-1bf6-4d09-a944-835aafc7228e',
    //                 windowTitle: '自定义列表页'
    //               }
    //             ]
    //           }
    //         ]
    //       }
    // };

}

export async function windowMetaData(windowId) {
    const { appContext, isQuestionnaireApp } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    if (!isDevelopment || !isQuestionnaireApp) {
        return axios.get(`${proxy}/windowMetaData/${windowId}`, { headers , responseType: 'json' });
    }
    // mock 问卷
    // return {
    //     data: {
    //         status: '0',
    //         msg: '',
    //         data: {
    //             windowId: windowId,
    //             data: [
    //                 {
    //                     "componentId": "9288c5eb-6e37-4a1b-85fc-c487b7e1c9ac",
    //                     "componentName": "Layout1_9288c5eb-6e37-4a1b-85fc-c487b7e1c9ac",
    //                     "label": "Layout",
    //                     "title": "布局",
    //                     "layout": true,
    //                     "category": "容器组件",
    //                     "icon": "credit-card",
    //                     "formData": {
    //                         "events": [
    //                             {
    //                                 "eventName": "onCreate",
    //                                 "actions": [
    //                                     {
    //                                         "actionName": "jsAction",
    //                                         "scripts": "\n            var params = OC.getAllParams();\n            console.log('all params: ', params);\n\n            var winCtxs = ocWindow.getContexts();\n            console.log('winCtxs: ', winCtxs);\n\n            var allParams = {};\n            \n            Object.keys(params).forEach(function(key) {\n              allParams[key] = params[key];\n            });\n            Object.keys(winCtxs).forEach(function(key) {\n              allParams[key] = winCtxs[key];\n            });\n\n            console.log('allParams: ', allParams);\n            var questionnaireForm = ocWindow.getComponentByName('questionnaireForm');\n            questionnaireForm.allParams = allParams;\n\n            // 控制是否可编辑\n            if (allParams.editable == false) {\n              questionnaireForm.setEditable(false);\n              ocWindow.getComponentByName('questionSaveBtnBoxRowIns').setVisible(false);\n            }\n\n            // 获取答案\n            if (allParams.answerInstId) {\n              var entityName = 'com.open_care.survey.OCSurveyAnswer';\n              OC.getOne(entityName, allParams.answerInstId, '', function(response) {\n                if (response && response.status === '0') {\n                  OC.updateDataSource(ocWindow, \"questionnaireForm\", response.data);\n                }\n              }, function() {});\n            }\n          "
    //                                     }
    //                                 ]
    //                             }
    //                         ],
    //                         "title": "",
    //                         "windowContext": [],
    //                         "visible": true,
    //                         "styles": "{}",
    //                         "componentDataCache": true,
    //                         "uiSchemaDataCache": false,
    //                         "enableBackgroundLoading": false,
    //                         "seqno": 1,
    //                         "canDelete": false,
    //                         "componentName": "Layout1_9288c5eb-6e37-4a1b-85fc-c487b7e1c9ac"
    //                     },
    //                     "schema": {
    //                         "type": "object",
    //                         "title": "formData",
    //                         "properties": {
    //                             "componentName": {
    //                                 "type": "string",
    //                                 "title": "组件名称"
    //                             },
    //                             "showMaxAndMinIcon": {
    //                                 "type": "boolean",
    //                                 "title": "显示最小化最大化按钮",
    //                                 "default": false
    //                             },
    //                             "visible": {
    //                                 "type": "boolean",
    //                                 "title": "可见"
    //                             },
    //                             "componentDataCache": {
    //                                 "type": "boolean",
    //                                 "title": "开启组件数据缓存(前端)",
    //                                 "default": true
    //                             },
    //                             "uiSchemaDataCache": {
    //                                 "type": "boolean",
    //                                 "title": "开启UI界面缓存(后端)",
    //                                 "default": false
    //                             },
    //                             "enableBackgroundLoading": {
    //                                 "type": "boolean",
    //                                 "title": "开启预加载(静默加载)",
    //                                 "default": false
    //                             },
    //                             "windowContext": {
    //                                 "type": "array",
    //                                 "title": "窗口上下文",
    //                                 "items": {
    //                                     "type": "object",
    //                                     "properties": {
    //                                         "key": {
    //                                             "type": "string",
    //                                             "title": "主键"
    //                                         },
    //                                         "value": {
    //                                             "type": "string",
    //                                             "title": "键值"
    //                                         }
    //                                     }
    //                                 }
    //                             },
    //                             "title": {
    //                                 "type": "string",
    //                                 "title": "窗口名称"
    //                             }
    //                         },
    //                         "required": [
    //                             "componentName",
    //                             "title"
    //                         ]
    //                     },
    //                     "uiSchema": {},
    //                     "children": [
    //                         {
    //                             "componentId": "9da6025b-6bb3-453d-95eb-40d1a1075d43",
    //                             "componentName": "Form1",
    //                             "label": "Form",
    //                             "title": "表单",
    //                             "layout": true,
    //                             "category": "数据组件",
    //                             "icon": "form",
    //                             "formData": {
    //                                 "events": [],
    //                                 "searchForm": false,
    //                                 "trimValues": false,
    //                                 "enablePromptOfUnSavedData": true,
    //                                 "formItemLabelWidthAdaptation": false,
    //                                 "displayColNum": "1",
    //                                 "visible": true,
    //                                 "readonlyStyle": "disable",
    //                                 "editable": true,
    //                                 "showColon": true,
    //                                 "layout": "vertical",
    //                                 "styles": "{\"background\": \"#fff\", \"padding\":\"10px\"}",
    //                                 "seqno": 1,
    //                                 "canDelete": false,
    //                                 "componentName": "Form1"
    //                             },
    //                             "schema": {
    //                                 "type": "object",
    //                                 "title": "formData",
    //                                 "properties": {
    //                                     "searchForm": {
    //                                         "type": "boolean",
    //                                         "title": "是否为searchForm"
    //                                     },
    //                                     "formItemLabelWidthAdaptation": {
    //                                         "type": "boolean",
    //                                         "title": "formItemLabel宽度自适应"
    //                                     },
    //                                     "trimValues": {
    //                                         "type": "boolean",
    //                                         "title": "子组件取值时自动去除两端空格(目前仅支持绑定了字段的Input和TextArea)",
    //                                         "default": false
    //                                     },
    //                                     "componentName": {
    //                                         "type": "string",
    //                                         "title": "组件名称"
    //                                     },
    //                                     "displayColNum": {
    //                                         "type": "string",
    //                                         "title": "displayColNum"
    //                                     },
    //                                     "entityDefId": {
    //                                         "type": "string",
    //                                         "title": "实体"
    //                                     },
    //                                     "primaryFieldName": {
    //                                         "type": "string",
    //                                         "title": "主键字段名称"
    //                                     },
    //                                     "readonlyStyle": {
    //                                         "type": "string",
    //                                         "title": "readonly显示样式"
    //                                     },
    //                                     "editable": {
    //                                         "type": "boolean",
    //                                         "title": "是否可编辑"
    //                                     },
    //                                     "showColon": {
    //                                         "type": "boolean",
    //                                         "title": "是否显示label后面的冒号(:)"
    //                                     },
    //                                     "layout": {
    //                                         "type": "string",
    //                                         "title": "FormItemLayout",
    //                                         "enum": [
    //                                             "horizontal",
    //                                             "vertical"
    //                                         ],
    //                                         "enumNames": [
    //                                             "水平",
    //                                             "垂直"
    //                                         ]
    //                                     },
    //                                     "enablePromptOfUnSavedData": {
    //                                         "type": "boolean",
    //                                         "title": "离开页面前提示保存"
    //                                     },
    //                                     "promptMessageOfUnSavedData": {
    //                                         "type": "string",
    //                                         "title": "离开页面前提示信息",
    //                                         "default": "有未保存的数据，确认离开吗？"
    //                                     },
    //                                     "jsonPathName": {
    //                                         "type": "string",
    //                                         "title": "jsonPathName"
    //                                     },
    //                                     "visible": {
    //                                         "type": "boolean",
    //                                         "title": "可见"
    //                                     }
    //                                 },
    //                                 "required": [
    //                                     "componentName",
    //                                     "entityDefId"
    //                                 ]
    //                             },
    //                             "uiSchema": {},
    //                             "children": [
    //                                 {
    //                                     "componentId": "923e7a07-3f07-4f15-9918-96c15df00721",
    //                                     "componentName": "questionnaireHeader",
    //                                     "label": "Row",
    //                                     "title": "行",
    //                                     "layout": true,
    //                                     "category": "容器组件",
    //                                     "icon": "menu-fold",
    //                                     "formData": {
    //                                         "gutter": 0,
    //                                         "align": "top",
    //                                         "justify": "start",
    //                                         "visible": true,
    //                                         "styles": "{\"padding\": \"10px\", \"background\": \"#f5f5f9\"}",
    //                                         "seqno": 1,
    //                                         "componentName": "questionnaireHeader",
    //                                         "canDelete": false,
    //                                         "canMove": false
    //                                     },
    //                                     "schema": {
    //                                         "type": "object",
    //                                         "title": "formData",
    //                                         "properties": {
    //                                             "componentName": {
    //                                                 "type": "string",
    //                                                 "title": "组件名称"
    //                                             },
    //                                             "align": {
    //                                                 "type": "string",
    //                                                 "title": "flex 布局下的垂直对齐方式",
    //                                                 "enum": [
    //                                                     "top",
    //                                                     "middle",
    //                                                     "bottom"
    //                                                 ]
    //                                             },
    //                                             "gutter": {
    //                                                 "type": "number",
    //                                                 "title": "栅格间隔"
    //                                             },
    //                                             "justify": {
    //                                                 "type": "string",
    //                                                 "title": "flex 布局下的水平排列方式",
    //                                                 "enum": [
    //                                                     "start",
    //                                                     "end",
    //                                                     "center",
    //                                                     "space-around",
    //                                                     " space-between"
    //                                                 ]
    //                                             },
    //                                             "type": {
    //                                                 "type": "string",
    //                                                 "title": "布局模式",
    //                                                 "enum": [
    //                                                     "flex"
    //                                                 ]
    //                                             },
    //                                             "visible": {
    //                                                 "type": "boolean",
    //                                                 "title": "可见"
    //                                             }
    //                                         },
    //                                         "required": [
    //                                             "componentName"
    //                                         ]
    //                                     },
    //                                     "uiSchema": {},
    //                                     "children": [
    //                                         {
    //                                             "componentId": "02260235-b64f-4040-823f-07c6b93eba9c",
    //                                             "componentName": "Col1",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "seqno": 1,
    //                                                 "canDelete": false,
    //                                                 "canMove": false,
    //                                                 "componentName": "Col1"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "163f8973-3f99-48b6-a2be-78d3e25af296",
    //                                                     "componentName": "questionnaireTitle",
    //                                                     "label": "Label",
    //                                                     "title": "文本标签",
    //                                                     "category": "其他组件",
    //                                                     "icon": "appstore",
    //                                                     "formData": {
    //                                                         "visible": true,
    //                                                         "styles": "{\"font-size\": \"26px\", \"text-align\":\"center\", \"display\":\"inline-block\",\"width\":\"100%\",\"margin\":\"10px 0\" }",
    //                                                         "seqno": 1,
    //                                                         "children": "北京市机关事务管理局单位员工健康状况调查问卷",
    //                                                         "canMove": false,
    //                                                         "componentName": "questionnaireTitle",
    //                                                         "valueChangedForTitle": true,
    //                                                         "canDelete": false
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "componentName": {
    //                                                                 "type": "string",
    //                                                                 "title": "组件名称"
    //                                                             },
    //                                                             "visible": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "可见"
    //                                                             },
    //                                                             "children": {
    //                                                                 "type": "string",
    //                                                                 "title": "内容"
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "6dea6fd4-67dd-4e51-ba10-a3b6226bae39",
    //                                             "componentName": "Col2",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "seqno": 1,
    //                                                 "canDelete": false,
    //                                                 "canMove": false,
    //                                                 "componentName": "Col2"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "34b3b173-5c13-46f5-bb5b-aefc00f9a32a",
    //                                                     "componentName": "questionnaireDescription",
    //                                                     "label": "Label",
    //                                                     "title": "文本标签",
    //                                                     "category": "其他组件",
    //                                                     "icon": "appstore",
    //                                                     "formData": {
    //                                                         "visible": true,
    //                                                         "styles": "{}",
    //                                                         "seqno": 1,
    //                                                         "canMove": false,
    //                                                         "children": "现代人久坐少动的生活方式，导致肌肉减少、运动能力下降，进而引发以下健康问题： 1、“肩、颈、腰、膝”等部位的慢性疼痛； 2、“超重、脂肪肝、高血压、糖尿病、高脂血症”等慢性代谢性疾病。 现代医学已经证明：正确、科学的运动能有效改善上述健康问题。 为改善各单位员工健康状况，落实“健康中国、健康企业建设”工作，现针对各单位员工的健康状况做一次调研摸底。 此次问卷调查共24题，请大家认真填写，以便于后续工作更有针对性、有效性！",
    //                                                         "componentName": "questionnaireDescription",
    //                                                         "canDelete": false
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "componentName": {
    //                                                                 "type": "string",
    //                                                                 "title": "组件名称"
    //                                                             },
    //                                                             "visible": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "可见"
    //                                                             },
    //                                                             "children": {
    //                                                                 "type": "string",
    //                                                                 "title": "内容"
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         }
    //                                     ]
    //                                 },
    //                                 {
    //                                     "componentId": "75ad765c-22ff-4abd-b209-bc90c2ab331d",
    //                                     "componentName": "questionnaireBody",
    //                                     "label": "Row",
    //                                     "title": "行",
    //                                     "layout": true,
    //                                     "category": "容器组件",
    //                                     "icon": "menu-fold",
    //                                     "formData": {
    //                                         "gutter": 0,
    //                                         "align": "top",
    //                                         "justify": "start",
    //                                         "visible": true,
    //                                         "styles": "{\"padding\": \"10px\", \"background\": \"#f5f5f9\"}",
    //                                         "seqno": 1,
    //                                         "componentName": "questionnaireBody",
    //                                         "canDelete": false,
    //                                         "canMove": false
    //                                     },
    //                                     "schema": {
    //                                         "type": "object",
    //                                         "title": "formData",
    //                                         "properties": {
    //                                             "componentName": {
    //                                                 "type": "string",
    //                                                 "title": "组件名称"
    //                                             },
    //                                             "align": {
    //                                                 "type": "string",
    //                                                 "title": "flex 布局下的垂直对齐方式",
    //                                                 "enum": [
    //                                                     "top",
    //                                                     "middle",
    //                                                     "bottom"
    //                                                 ]
    //                                             },
    //                                             "gutter": {
    //                                                 "type": "number",
    //                                                 "title": "栅格间隔"
    //                                             },
    //                                             "justify": {
    //                                                 "type": "string",
    //                                                 "title": "flex 布局下的水平排列方式",
    //                                                 "enum": [
    //                                                     "start",
    //                                                     "end",
    //                                                     "center",
    //                                                     "space-around",
    //                                                     " space-between"
    //                                                 ]
    //                                             },
    //                                             "type": {
    //                                                 "type": "string",
    //                                                 "title": "布局模式",
    //                                                 "enum": [
    //                                                     "flex"
    //                                                 ]
    //                                             },
    //                                             "visible": {
    //                                                 "type": "boolean",
    //                                                 "title": "可见"
    //                                             }
    //                                         },
    //                                         "required": [
    //                                             "componentName"
    //                                         ]
    //                                     },
    //                                     "uiSchema": {},
    //                                     "children": [
    //                                         {
    //                                             "componentId": "3be82d1c-72f4-4aca-8ab8-3969d34124b4",
    //                                             "componentName": "Col3",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col3"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "cda555eb-f394-44c9-ad7d-71323cfc9412",
    //                                                     "componentName": "QRadio1",
    //                                                     "label": "QRadio",
    //                                                     "title": "单项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-circle",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "select",
    //                                                     "formData": {
    //                                                         "events": [],
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "超重",
    //                                                                 "value": "a9679792-6b94-48e2-84ef-9e994a19be39",
    //                                                                 "componentId": "a9679792-6b94-48e2-84ef-9e994a19be39"
    //                                                             },
    //                                                             {
    //                                                                 "label": "脂肪肝",
    //                                                                 "value": "48ce0201-6554-4c5a-9c88-adee0af6d329",
    //                                                                 "componentId": "48ce0201-6554-4c5a-9c88-adee0af6d329"
    //                                                             },
    //                                                             {
    //                                                                 "label": "高血压",
    //                                                                 "componentId": "cb988d57-17af-4639-bf0b-d83200070681",
    //                                                                 "value": "cb988d57-17af-4639-bf0b-d83200070681"
    //                                                             },
    //                                                             {
    //                                                                 "label": "高血糖",
    //                                                                 "componentId": "14d3c06b-a909-45d0-9b3c-b4be27896ed4",
    //                                                                 "value": "14d3c06b-a909-45d0-9b3c-b4be27896ed4"
    //                                                             },
    //                                                             {
    //                                                                 "label": "高血脂",
    //                                                                 "componentId": "3e051fbd-67c3-4762-b081-fc0ff846c5f8",
    //                                                                 "value": "3e051fbd-67c3-4762-b081-fc0ff846c5f8"
    //                                                             },
    //                                                             {
    //                                                                 "label": "高尿酸血症",
    //                                                                 "componentId": "75de55e4-6254-431a-9a9d-9f0f0d4d13ac",
    //                                                                 "value": "75de55e4-6254-431a-9a9d-9f0f0d4d13ac"
    //                                                             },
    //                                                             {
    //                                                                 "label": "以上皆无异常",
    //                                                                 "componentId": "1aaaffd0-4dc8-4ef0-ae15-d0d133f123e9",
    //                                                                 "value": "1aaaffd0-4dc8-4ef0-ae15-d0d133f123e9"
    //                                                             }
    //                                                         ],
    //                                                         "visible": true,
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "请标出您有异常的体检指标",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "displayType": "radio",
    //                                                         "triggerTargetType": "auto",
    //                                                         "styles": "{}",
    //                                                         "componentName": "QRadio1",
    //                                                         "formItemName": "bf4bc2e0-1da9-4e98-a35d-446343765a57",
    //                                                         "required": false,
    //                                                         "showSeqNo": true,
    //                                                         "rules": [
    //                                                             {
    //                                                                 "rule": {
    //                                                                     "treeValue": {
    //                                                                         "id": "8baaa9b8-0123-4456-b89a-b17e95b4dcb1",
    //                                                                         "type": "group",
    //                                                                         "properties": {
    //                                                                             "conjunction": "AND"
    //                                                                         },
    //                                                                         "children1": {
    //                                                                             "8aabb899-89ab-4cde-b012-317e95b52d8e": {
    //                                                                                 "type": "rule",
    //                                                                                 "properties": {
    //                                                                                     "field": "answer",
    //                                                                                     "operator": "select_equals",
    //                                                                                     "value": [
    //                                                                                         "a9679792-6b94-48e2-84ef-9e994a19be39"
    //                                                                                     ],
    //                                                                                     "valueSrc": [
    //                                                                                         "value"
    //                                                                                     ],
    //                                                                                     "valueType": [
    //                                                                                         "select"
    //                                                                                     ],
    //                                                                                     "operatorOptions": null
    //                                                                                 },
    //                                                                                 "id": "8aabb899-89ab-4cde-b012-317e95b52d8e",
    //                                                                                 "path": [
    //                                                                                     "8baaa9b8-0123-4456-b89a-b17e95b4dcb1",
    //                                                                                     "8aabb899-89ab-4cde-b012-317e95b52d8e"
    //                                                                                 ]
    //                                                                             }
    //                                                                         },
    //                                                                         "path": [
    //                                                                             "8baaa9b8-0123-4456-b89a-b17e95b4dcb1"
    //                                                                         ]
    //                                                                     },
    //                                                                     "jsonLogicValue": {
    //                                                                         "and": [
    //                                                                             {
    //                                                                                 "==": [
    //                                                                                     {
    //                                                                                         "var": "answer"
    //                                                                                     },
    //                                                                                     "a9679792-6b94-48e2-84ef-9e994a19be39"
    //                                                                                 ]
    //                                                                             }
    //                                                                         ]
    //                                                                     }
    //                                                                 },
    //                                                                 "actions": [
    //                                                                     {
    //                                                                         "type": "goto_cmp",
    //                                                                         "target_id": "b4e3747d-1854-4a03-98dc-5c82e2c4300a"
    //                                                                     }
    //                                                                 ]
    //                                                             }
    //                                                         ]
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "fieldId"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "689170b6-c48c-4fb8-b347-27ea76947c65",
    //                                             "componentName": "Col4",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col4"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "fcd1bbb7-4f17-4515-b628-29ae5ba2ff86",
    //                                                     "componentName": "QCheckboxGroup1",
    //                                                     "label": "QCheckboxGroup",
    //                                                     "title": "多项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-square",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "multiselect",
    //                                                     "formData": {
    //                                                         "visible": true,
    //                                                         "styles": "{}",
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "药物治疗",
    //                                                                 "value": "c228b1e7-9d7e-492d-a2f5-bbe301f67c3d",
    //                                                                 "componentId": "c228b1e7-9d7e-492d-a2f5-bbe301f67c3d"
    //                                                             },
    //                                                             {
    //                                                                 "label": "运动健身",
    //                                                                 "value": "3c5566e6-5788-44fb-a276-e82091303b7b",
    //                                                                 "componentId": "3c5566e6-5788-44fb-a276-e82091303b7b"
    //                                                             },
    //                                                             {
    //                                                                 "label": "饮食管理",
    //                                                                 "componentId": "9e56aa5d-694f-4345-aedc-d03f41f32e9d",
    //                                                                 "value": "9e56aa5d-694f-4345-aedc-d03f41f32e9d"
    //                                                             },
    //                                                             {
    //                                                                 "label": "其他",
    //                                                                 "componentId": "1b955eab-1619-4ade-bc6b-7d3501741d0e",
    //                                                                 "value": "1b955eab-1619-4ade-bc6b-7d3501741d0e"
    //                                                             }
    //                                                         ],
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "出现生理指标异常后您更愿意通过以下哪种途径解决？",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "events": [],
    //                                                         "componentName": "QCheckboxGroup1",
    //                                                         "formItemName": "cd9323db-8f22-458d-bde4-ab0ac4f3389c",
    //                                                         "showSeqNo": false,
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "maxChecked": {
    //                                                                 "type": "string",
    //                                                                 "title": "最多可选几个选项"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "d9a1631a-fc09-49d8-bd07-2dab7eb8ac4a",
    //                                             "componentName": "Col5",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col5"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "b4e3747d-1854-4a03-98dc-5c82e2c4300a",
    //                                                     "componentName": "QRadio2",
    //                                                     "label": "QRadio",
    //                                                     "title": "单项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-circle",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "select",
    //                                                     "formData": {
    //                                                         "events": [],
    //                                                         "showSeqNo": true,
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "1小时以内",
    //                                                                 "value": "a1d3e63b-819c-4aa2-9230-4838ac8cd030",
    //                                                                 "componentId": "a1d3e63b-819c-4aa2-9230-4838ac8cd030"
    //                                                             },
    //                                                             {
    //                                                                 "label": "1-2小时",
    //                                                                 "value": "ca66cfd3-14d6-4f60-b595-708ebf5c4e1e",
    //                                                                 "componentId": "ca66cfd3-14d6-4f60-b595-708ebf5c4e1e"
    //                                                             },
    //                                                             {
    //                                                                 "label": "2-3小时",
    //                                                                 "componentId": "f1f65238-3548-4601-a209-efd5594a8c36",
    //                                                                 "value": "f1f65238-3548-4601-a209-efd5594a8c36"
    //                                                             },
    //                                                             {
    //                                                                 "label": "3-4小时",
    //                                                                 "componentId": "4a82d5eb-7606-4a01-b38a-6e1ef9d5f120",
    //                                                                 "value": "4a82d5eb-7606-4a01-b38a-6e1ef9d5f120"
    //                                                             },
    //                                                             {
    //                                                                 "label": "4小时以上",
    //                                                                 "componentId": "e0fb57ce-0cdf-42a0-b7ff-fa2a3e667bec",
    //                                                                 "value": "e0fb57ce-0cdf-42a0-b7ff-fa2a3e667bec"
    //                                                             }
    //                                                         ],
    //                                                         "visible": true,
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "请选择一个选项日常工作，平均单次连续伏案工作时长？",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "displayType": "radio",
    //                                                         "triggerTargetType": "auto",
    //                                                         "styles": "{}",
    //                                                         "componentName": "QRadio2",
    //                                                         "formItemName": "3e438c02-4f3e-4278-a6e0-bfe12fa0f02d",
    //                                                         "required": false
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "fieldId"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "9e214349-3f46-4c46-a63d-8241909984f4",
    //                                             "componentName": "Col6",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col6"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "3c61a42a-7c2a-4047-9f66-17ce0acff068",
    //                                                     "componentName": "QRadio3",
    //                                                     "label": "QRadio",
    //                                                     "title": "单项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-circle",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "select",
    //                                                     "formData": {
    //                                                         "showSeqNo": true,
    //                                                         "events": [],
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "是",
    //                                                                 "value": "28e5207f-9cf3-470b-84e5-47b6751cc118",
    //                                                                 "componentId": "28e5207f-9cf3-470b-84e5-47b6751cc118"
    //                                                             },
    //                                                             {
    //                                                                 "label": "否",
    //                                                                 "value": "7553a043-93e5-4627-a78a-83d0c21cd1d5",
    //                                                                 "componentId": "7553a043-93e5-4627-a78a-83d0c21cd1d5"
    //                                                             }
    //                                                         ],
    //                                                         "visible": true,
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "请选择一个选项日常办公时，坐姿是否符合以下标准？（双脚平放地面，膝盖和腿部保持在一个高度或者略低于臀部，椅背约为100°-110°的倾斜角度）",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "displayType": "radio",
    //                                                         "triggerTargetType": "auto",
    //                                                         "styles": "{}",
    //                                                         "componentName": "QRadio3",
    //                                                         "formItemName": "236042eb-7dec-4a19-9c6d-07143b2d80e9",
    //                                                         "required": false
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "fieldId"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "245e5c3c-df5d-47f7-8e74-1d3b8667687b",
    //                                             "componentName": "Col7",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col7"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "e8a6f817-4c60-4e67-8979-9b18ea800fac",
    //                                                     "componentName": "QRadio4",
    //                                                     "label": "QRadio",
    //                                                     "title": "单项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-circle",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "select",
    //                                                     "formData": {
    //                                                         "showSeqNo": true,
    //                                                         "events": [],
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "是",
    //                                                                 "value": "807319df-55f5-4a3b-a093-3aca893fee66",
    //                                                                 "componentId": "807319df-55f5-4a3b-a093-3aca893fee66"
    //                                                             },
    //                                                             {
    //                                                                 "label": "否",
    //                                                                 "value": "7c5063ca-90de-478d-aab1-7f2b66fed402",
    //                                                                 "componentId": "7c5063ca-90de-478d-aab1-7f2b66fed402"
    //                                                             }
    //                                                         ],
    //                                                         "visible": true,
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "日常办公时，坐姿是否符合以下标准？（双肩放松，手肘微打开，手腕和手保持直线）",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "displayType": "radio",
    //                                                         "triggerTargetType": "auto",
    //                                                         "styles": "{}",
    //                                                         "componentName": "QRadio4",
    //                                                         "formItemName": "ddfae68f-f5ae-42e7-8d3b-15a55ac0dca3"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "fieldId"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "252b9a7d-7ea0-4c66-ad3e-b4420e211008",
    //                                             "componentName": "Col8",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col8"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "cb597db7-7e03-4fa7-a8d2-b78337e6f043",
    //                                                     "componentName": "QRadio5",
    //                                                     "label": "QRadio",
    //                                                     "title": "单项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-circle",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "select",
    //                                                     "formData": {
    //                                                         "showSeqNo": true,
    //                                                         "events": [],
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "是",
    //                                                                 "value": "5b73c27f-85ca-490b-ae70-01cc8a4b820d",
    //                                                                 "componentId": "5b73c27f-85ca-490b-ae70-01cc8a4b820d"
    //                                                             },
    //                                                             {
    //                                                                 "label": "否",
    //                                                                 "value": "179d5fb7-a13f-45da-ab09-8af624ad02d0",
    //                                                                 "componentId": "179d5fb7-a13f-45da-ab09-8af624ad02d0"
    //                                                             }
    //                                                         ],
    //                                                         "visible": true,
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "请选择一个选项日常办公时，坐姿是否符合以下标准？（与显示器距离45-70cm，显示器显示第一行字微微低于视线或与视线齐平）",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "displayType": "radio",
    //                                                         "triggerTargetType": "auto",
    //                                                         "styles": "{}",
    //                                                         "componentName": "QRadio5",
    //                                                         "formItemName": "05eac017-08a3-4286-8ae1-5357bad90635"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "fieldId"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "ad0788cd-2405-473c-a92e-a045da91598a",
    //                                             "componentName": "Col9",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col9"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "5100c772-e878-422f-a889-3bdead7dec97",
    //                                                     "componentName": "QRadio6",
    //                                                     "label": "QRadio",
    //                                                     "title": "单项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-circle",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "select",
    //                                                     "formData": {
    //                                                         "events": [],
    //                                                         "showSeqNo": true,
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "工作操",
    //                                                                 "value": "70abdcc6-cffd-418b-bc78-c5c1d95ef3e4",
    //                                                                 "componentId": "70abdcc6-cffd-418b-bc78-c5c1d95ef3e4"
    //                                                             },
    //                                                             {
    //                                                                 "label": "中午休息或下班后统一组织运动训练",
    //                                                                 "value": "f42efcdf-985e-4f90-973d-a32443b62675",
    //                                                                 "componentId": "f42efcdf-985e-4f90-973d-a32443b62675"
    //                                                             },
    //                                                             {
    //                                                                 "label": "将专业指导方案发送到手机回家练习",
    //                                                                 "componentId": "1f6f858f-a953-4835-a258-fc57880fa915",
    //                                                                 "value": "1f6f858f-a953-4835-a258-fc57880fa915"
    //                                                             },
    //                                                             {
    //                                                                 "label": "休息",
    //                                                                 "componentId": "e3e6d381-532c-41fc-81bb-adae8dcb4a17",
    //                                                                 "value": "e3e6d381-532c-41fc-81bb-adae8dcb4a17"
    //                                                             },
    //                                                             {
    //                                                                 "label": "其他",
    //                                                                 "componentId": "74310ead-2f56-4b2f-a0f7-888fd114da97",
    //                                                                 "value": "74310ead-2f56-4b2f-a0f7-888fd114da97"
    //                                                             }
    //                                                         ],
    //                                                         "visible": true,
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "请选择一个选项您更愿意通过以下哪种方式改善长期伏案工作带来的不适感？（可多选）",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "displayType": "radio",
    //                                                         "triggerTargetType": "auto",
    //                                                         "styles": "{}",
    //                                                         "componentName": "QRadio6",
    //                                                         "formItemName": "8333e0f3-841e-4297-813f-c816222f3d49"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "fieldId"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "078d6b79-10cf-4018-afac-2994bf11e7c4",
    //                                             "componentName": "Col10",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col10"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "22393160-49e7-4302-87de-14d6f6380ea1",
    //                                                     "componentName": "QRadio7",
    //                                                     "label": "QRadio",
    //                                                     "title": "单项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-circle",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "select",
    //                                                     "formData": {
    //                                                         "events": [],
    //                                                         "showSeqNo": true,
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "0-1次/周",
    //                                                                 "value": "08a39a3f-4e48-4924-b655-b8a514dee80d",
    //                                                                 "componentId": "08a39a3f-4e48-4924-b655-b8a514dee80d"
    //                                                             },
    //                                                             {
    //                                                                 "label": "1-2次/周",
    //                                                                 "value": "15c0111a-2eef-4cde-b9c3-66fa36b8eef4",
    //                                                                 "componentId": "15c0111a-2eef-4cde-b9c3-66fa36b8eef4"
    //                                                             },
    //                                                             {
    //                                                                 "label": "2-3次/周",
    //                                                                 "componentId": "8e466a8d-2bb6-4bfe-96b9-e8f07f5fbb26",
    //                                                                 "value": "8e466a8d-2bb6-4bfe-96b9-e8f07f5fbb26"
    //                                                             },
    //                                                             {
    //                                                                 "label": "3次/周以上",
    //                                                                 "componentId": "868a484d-f1de-47b6-aecb-a90dc3f8ad8c",
    //                                                                 "value": "868a484d-f1de-47b6-aecb-a90dc3f8ad8c"
    //                                                             }
    //                                                         ],
    //                                                         "visible": true,
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "您现在运动的频率（每次30分钟以上）？",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "displayType": "radio",
    //                                                         "triggerTargetType": "auto",
    //                                                         "styles": "{}",
    //                                                         "componentName": "QRadio7",
    //                                                         "formItemName": "8f55dbdb-24d2-4cdd-9d0f-10d76dae39d2"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "fieldId"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "8ee7c785-0587-4fc6-94eb-00c7bf12c7b2",
    //                                             "componentName": "Col11",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col11"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "b01efa5b-6643-4e3c-a5c2-dfdd4c093dcc",
    //                                                     "componentName": "QCheckboxGroup2",
    //                                                     "label": "QCheckboxGroup",
    //                                                     "title": "多项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-square",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "multiselect",
    //                                                     "formData": {
    //                                                         "visible": true,
    //                                                         "showSeqNo": true,
    //                                                         "styles": "{}",
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "羽毛球",
    //                                                                 "value": "0f3d6acd-b3d9-4710-9de5-d79704ae03d1",
    //                                                                 "componentId": "0f3d6acd-b3d9-4710-9de5-d79704ae03d1"
    //                                                             },
    //                                                             {
    //                                                                 "label": "篮球",
    //                                                                 "value": "af747572-cde8-417e-b354-1c6da5408889",
    //                                                                 "componentId": "af747572-cde8-417e-b354-1c6da5408889"
    //                                                             },
    //                                                             {
    //                                                                 "label": "足球",
    //                                                                 "componentId": "f7f85e14-58ae-4ff0-974f-496986fa0626",
    //                                                                 "value": "f7f85e14-58ae-4ff0-974f-496986fa0626"
    //                                                             },
    //                                                             {
    //                                                                 "label": "乒乓球",
    //                                                                 "componentId": "23c24a62-b802-483c-a6ed-92c02d89c5b6",
    //                                                                 "value": "23c24a62-b802-483c-a6ed-92c02d89c5b6"
    //                                                             },
    //                                                             {
    //                                                                 "label": "跑步",
    //                                                                 "componentId": "95f2270c-c4fc-4eef-9218-8bea20b1bc2a",
    //                                                                 "value": "95f2270c-c4fc-4eef-9218-8bea20b1bc2a"
    //                                                             },
    //                                                             {
    //                                                                 "label": "户外徒步",
    //                                                                 "componentId": "33f2c75c-be1e-46d9-843b-59dd33588aa5",
    //                                                                 "value": "33f2c75c-be1e-46d9-843b-59dd33588aa5"
    //                                                             },
    //                                                             {
    //                                                                 "label": "自行车户外骑行",
    //                                                                 "componentId": "82c70142-573f-4a4b-9352-d5d37893abf5",
    //                                                                 "value": "82c70142-573f-4a4b-9352-d5d37893abf5"
    //                                                             },
    //                                                             {
    //                                                                 "label": "健身训练",
    //                                                                 "componentId": "dfa26c06-1534-45fc-bd70-ef542762ac51",
    //                                                                 "value": "dfa26c06-1534-45fc-bd70-ef542762ac51"
    //                                                             },
    //                                                             {
    //                                                                 "label": "瑜伽",
    //                                                                 "componentId": "bffe2856-c981-44a7-85fd-1cdbc97a4330",
    //                                                                 "value": "bffe2856-c981-44a7-85fd-1cdbc97a4330"
    //                                                             },
    //                                                             {
    //                                                                 "label": "游泳",
    //                                                                 "componentId": "7f45e36e-c101-4e5b-a227-cc71b3f280ee",
    //                                                                 "value": "7f45e36e-c101-4e5b-a227-cc71b3f280ee"
    //                                                             },
    //                                                             {
    //                                                                 "label": "其他",
    //                                                                 "componentId": "2dd00265-054e-49b1-86ac-cf24cdc322de",
    //                                                                 "value": "2dd00265-054e-49b1-86ac-cf24cdc322de"
    //                                                             },
    //                                                             {
    //                                                                 "label": "无",
    //                                                                 "componentId": "4c89ee7e-83e4-43db-ae1b-9cb18fd5d6e9",
    //                                                                 "value": "4c89ee7e-83e4-43db-ae1b-9cb18fd5d6e9"
    //                                                             }
    //                                                         ],
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "请勾选需要的选项您目前在做的运动项目？",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "events": [],
    //                                                         "componentName": "QCheckboxGroup2",
    //                                                         "formItemName": "c5ab3ff8-1043-4a4f-99aa-77754518e553"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "maxChecked": {
    //                                                                 "type": "string",
    //                                                                 "title": "最多可选几个选项"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "03fd3656-9c56-42c5-b60b-d8abd21196d5",
    //                                             "componentName": "Col12",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col12"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "f5b23fa9-aa2f-4a6d-999d-45e9df1ace78",
    //                                                     "componentName": "QCheckboxGroup3",
    //                                                     "label": "QCheckboxGroup",
    //                                                     "title": "多项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-square",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "multiselect",
    //                                                     "formData": {
    //                                                         "visible": true,
    //                                                         "showSeqNo": true,
    //                                                         "styles": "{}",
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "慢性疼痛的预防及改善",
    //                                                                 "value": "5d60ca7c-7701-4c26-8d62-1baff1fda036",
    //                                                                 "componentId": "5d60ca7c-7701-4c26-8d62-1baff1fda036"
    //                                                             },
    //                                                             {
    //                                                                 "label": "慢病的预防及改善",
    //                                                                 "value": "3917a7f2-eef1-4142-952e-327cb9631d2f",
    //                                                                 "componentId": "3917a7f2-eef1-4142-952e-327cb9631d2f"
    //                                                             },
    //                                                             {
    //                                                                 "label": "减脂、塑形",
    //                                                                 "componentId": "7c2c0f7f-115c-4246-9b2e-9a7f62eb740c",
    //                                                                 "value": "7c2c0f7f-115c-4246-9b2e-9a7f62eb740c"
    //                                                             },
    //                                                             {
    //                                                                 "label": "参加比赛",
    //                                                                 "componentId": "f7ca5bc3-1156-480f-8942-d36f0a185c61",
    //                                                                 "value": "f7ca5bc3-1156-480f-8942-d36f0a185c61"
    //                                                             },
    //                                                             {
    //                                                                 "label": "其他",
    //                                                                 "componentId": "4c0e3084-3a32-47a4-9416-a989c4c19671",
    //                                                                 "value": "4c0e3084-3a32-47a4-9416-a989c4c19671"
    //                                                             },
    //                                                             {
    //                                                                 "label": "无",
    //                                                                 "componentId": "1fb64467-d4c1-4394-b4b8-ecc4f0dcaf10",
    //                                                                 "value": "1fb64467-d4c1-4394-b4b8-ecc4f0dcaf10"
    //                                                             }
    //                                                         ],
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "您希望通过运动实现哪些目标？",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "events": [],
    //                                                         "componentName": "QCheckboxGroup3",
    //                                                         "formItemName": "368d12c3-8e04-4d24-9cdb-e912661328dd"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "maxChecked": {
    //                                                                 "type": "string",
    //                                                                 "title": "最多可选几个选项"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "5874889c-c804-4f47-8771-ea0a676aea63",
    //                                             "componentName": "Col13",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col13"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "97f1f438-ae7d-4112-9fd6-7883097b6a54",
    //                                                     "componentName": "QRadio8",
    //                                                     "label": "QRadio",
    //                                                     "title": "单项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-circle",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "select",
    //                                                     "formData": {
    //                                                         "events": [],
    //                                                         "showSeqNo": true,
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "很好",
    //                                                                 "value": "ecef4552-c311-44da-8c60-147a06cee7e5",
    //                                                                 "componentId": "ecef4552-c311-44da-8c60-147a06cee7e5"
    //                                                             },
    //                                                             {
    //                                                                 "label": "一般",
    //                                                                 "value": "c5ea2fda-08ea-479d-b2c3-f675cb22dfa9",
    //                                                                 "componentId": "c5ea2fda-08ea-479d-b2c3-f675cb22dfa9"
    //                                                             },
    //                                                             {
    //                                                                 "label": "不满意",
    //                                                                 "componentId": "ac5ab5b0-0980-495d-b660-e531f35d2224",
    //                                                                 "value": "ac5ab5b0-0980-495d-b660-e531f35d2224"
    //                                                             }
    //                                                         ],
    //                                                         "visible": true,
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "现在的运动帮助您实现了预期目标？",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "displayType": "radio",
    //                                                         "triggerTargetType": "auto",
    //                                                         "styles": "{}",
    //                                                         "componentName": "QRadio8",
    //                                                         "formItemName": "bb49d19e-aae7-410e-9ccd-c0a1ca6da744"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "fieldId"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "7755990e-c498-4f5e-b879-a0b44a09d6d8",
    //                                             "componentName": "Col14",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col14"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "790e8118-2106-465f-b869-d316f1a29748",
    //                                                     "componentName": "QRadio9",
    //                                                     "label": "QRadio",
    //                                                     "title": "单项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-circle",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "select",
    //                                                     "formData": {
    //                                                         "events": [],
    //                                                         "showSeqNo": true,
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "是",
    //                                                                 "value": "52a2ad14-f531-4a8a-b2e7-54f4da35ae74",
    //                                                                 "componentId": "52a2ad14-f531-4a8a-b2e7-54f4da35ae74"
    //                                                             },
    //                                                             {
    //                                                                 "label": "否",
    //                                                                 "value": "0f7fe289-08d3-4a1b-b710-b3d7f4b74165",
    //                                                                 "componentId": "0f7fe289-08d3-4a1b-b710-b3d7f4b74165"
    //                                                             }
    //                                                         ],
    //                                                         "visible": true,
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "您是否需要运动训练专业指导？",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "displayType": "radio",
    //                                                         "triggerTargetType": "auto",
    //                                                         "styles": "{}",
    //                                                         "componentName": "QRadio9",
    //                                                         "formItemName": "b7a9fff0-c2a5-4a4f-9fcf-1fe5401eff98"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "fieldId"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
    //                                         {
    //                                             "componentId": "a8e045b2-c00c-423c-88dd-5c0951d08428",
    //                                             "componentName": "Col15",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": false,
    //                                                 "buttonAlign": "left",
    //                                                 "styles": "{}",
    //                                                 "componentName": "Col15"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "21deed6c-ca19-4ba1-ad41-5185cb9a2cf6",
    //                                                     "componentName": "QCheckboxGroup4",
    //                                                     "label": "QCheckboxGroup",
    //                                                     "title": "多项选择",
    //                                                     "category": "常用题型",
    //                                                     "icon": "check-square",
    //                                                     "isQuestion": true,
    //                                                     "answerType": "multiselect",
    //                                                     "formData": {
    //                                                         "visible": true,
    //                                                         "showSeqNo": true,
    //                                                         "styles": "{}",
    //                                                         "options": [
    //                                                             {
    //                                                                 "label": "没有时间",
    //                                                                 "value": "f5e2a8df-bed9-4bd5-81ef-9f6980d35818",
    //                                                                 "componentId": "f5e2a8df-bed9-4bd5-81ef-9f6980d35818"
    //                                                             },
    //                                                             {
    //                                                                 "label": "没有合适的场地（器械）",
    //                                                                 "value": "a53460dd-a48d-43b7-843b-6a7b59a0c830",
    //                                                                 "componentId": "a53460dd-a48d-43b7-843b-6a7b59a0c830"
    //                                                             },
    //                                                             {
    //                                                                 "label": "怕运动损伤",
    //                                                                 "componentId": "b6adb46e-8a9f-4c46-a8f7-ac9913c76a03",
    //                                                                 "value": "b6adb46e-8a9f-4c46-a8f7-ac9913c76a03"
    //                                                             },
    //                                                             {
    //                                                                 "label": "缺少教练指导",
    //                                                                 "componentId": "e96b7ee0-63aa-4c42-b5d3-30ed86db2b76",
    //                                                                 "value": "e96b7ee0-63aa-4c42-b5d3-30ed86db2b76"
    //                                                             },
    //                                                             {
    //                                                                 "label": "没有同伴",
    //                                                                 "componentId": "6f196dec-5a87-4708-be69-a04f52ac61c6",
    //                                                                 "value": "6f196dec-5a87-4708-be69-a04f52ac61c6"
    //                                                             },
    //                                                             {
    //                                                                 "label": "已有损伤存在，运动时有疼痛",
    //                                                                 "componentId": "cea7d940-da0a-4ad1-97fc-4406f5d5a218",
    //                                                                 "value": "cea7d940-da0a-4ad1-97fc-4406f5d5a218"
    //                                                             },
    //                                                             {
    //                                                                 "label": "其他",
    //                                                                 "componentId": "045ba92f-59f4-449f-9df7-b22d6353531a",
    //                                                                 "value": "045ba92f-59f4-449f-9df7-b22d6353531a"
    //                                                             }
    //                                                         ],
    //                                                         "direction": "horizontal",
    //                                                         "readonlyStyle": "basedOnForm",
    //                                                         "editable": true,
    //                                                         "additionalProperties": {
    //                                                             "label": "影响您参与运动的原因？",
    //                                                             "description": "",
    //                                                             "labelCol": {
    //                                                                 "span": 6
    //                                                             },
    //                                                             "wrapperCol": {
    //                                                                 "span": 18
    //                                                             }
    //                                                         },
    //                                                         "events": [],
    //                                                         "componentName": "QCheckboxGroup4",
    //                                                         "formItemName": "ef0d609a-e877-4f71-a968-d66c34675f9a"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "additionalProperties": {
    //                                                                 "type": "object",
    //                                                                 "title": "",
    //                                                                 "properties": {
    //                                                                     "label": {
    //                                                                         "type": "string",
    //                                                                         "title": "标题"
    //                                                                     },
    //                                                                     "description": {
    //                                                                         "type": "string",
    //                                                                         "title": "副标题(说明文字)"
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "options": {
    //                                                                 "type": "array",
    //                                                                 "title": "选项",
    //                                                                 "items": {
    //                                                                     "type": "object",
    //                                                                     "properties": {
    //                                                                         "label": {
    //                                                                             "type": "string",
    //                                                                             "title": " "
    //                                                                         }
    //                                                                     }
    //                                                                 }
    //                                                             },
    //                                                             "direction": {
    //                                                                 "type": "string",
    //                                                                 "title": "选项排列方式"
    //                                                             },
    //                                                             "displayPerRow": {
    //                                                                 "type": "string",
    //                                                                 "title": "每行显示选项的个数(排列方式为：水平 时生效)"
    //                                                             },
    //                                                             "maxChecked": {
    //                                                                 "type": "string",
    //                                                                 "title": "最多可选几个选项"
    //                                                             },
    //                                                             "required": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "是否必填",
    //                                                                 "default": false
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": []
    //                                                 }
    //                                             ]
    //                                         },
                                            
    //                                     ]
    //                                 },
    //                                 {
    //                                     "componentId": "6edf44ca-2c1d-4984-85ef-6fffdaa90567",
    //                                     "componentName": "questionSaveBtnBoxRowIns",
    //                                     "label": "Row",
    //                                     "title": "行",
    //                                     "layout": true,
    //                                     "category": "容器组件",
    //                                     "icon": "menu-fold",
    //                                     "formData": {
    //                                         "gutter": 0,
    //                                         "align": "top",
    //                                         "justify": "start",
    //                                         "visible": true,
    //                                         "styles": "{\"padding\": \"10px\", \"background\": \"#f5f5f9\"}",
    //                                         "seqno": 1,
    //                                         "componentName": "questionSaveBtnBoxRowIns",
    //                                         "canDelete": false,
    //                                         "canMove": false
    //                                     },
    //                                     "schema": {
    //                                         "type": "object",
    //                                         "title": "formData",
    //                                         "properties": {
    //                                             "componentName": {
    //                                                 "type": "string",
    //                                                 "title": "组件名称"
    //                                             },
    //                                             "align": {
    //                                                 "type": "string",
    //                                                 "title": "flex 布局下的垂直对齐方式",
    //                                                 "enum": [
    //                                                     "top",
    //                                                     "middle",
    //                                                     "bottom"
    //                                                 ]
    //                                             },
    //                                             "gutter": {
    //                                                 "type": "number",
    //                                                 "title": "栅格间隔"
    //                                             },
    //                                             "justify": {
    //                                                 "type": "string",
    //                                                 "title": "flex 布局下的水平排列方式",
    //                                                 "enum": [
    //                                                     "start",
    //                                                     "end",
    //                                                     "center",
    //                                                     "space-around",
    //                                                     " space-between"
    //                                                 ]
    //                                             },
    //                                             "type": {
    //                                                 "type": "string",
    //                                                 "title": "布局模式",
    //                                                 "enum": [
    //                                                     "flex"
    //                                                 ]
    //                                             },
    //                                             "visible": {
    //                                                 "type": "boolean",
    //                                                 "title": "可见"
    //                                             }
    //                                         },
    //                                         "required": [
    //                                             "componentName"
    //                                         ]
    //                                     },
    //                                     "uiSchema": {},
    //                                     "children": [
    //                                         {
    //                                             "componentId": "52650319-8421-4a8e-b13c-6d76281409ec",
    //                                             "componentName": "questionSaveBtnBoxColIns",
    //                                             "label": "Col",
    //                                             "title": "列",
    //                                             "layout": true,
    //                                             "category": "容器组件",
    //                                             "icon": "colum-height",
    //                                             "formData": {
    //                                                 "span": 24,
    //                                                 "offset": 0,
    //                                                 "pull": 0,
    //                                                 "push": 0,
    //                                                 "visible": true,
    //                                                 "buttonBox": true,
    //                                                 "buttonAlign": "center",
    //                                                 "styles": "{}",
    //                                                 "seqno": 1,
    //                                                 "canDelete": false,
    //                                                 "canMove": false,
    //                                                 "componentName": "questionSaveBtnBoxColIns"
    //                                             },
    //                                             "schema": {
    //                                                 "type": "object",
    //                                                 "title": "formData",
    //                                                 "properties": {
    //                                                     "componentName": {
    //                                                         "type": "string",
    //                                                         "title": "组件名称"
    //                                                     },
    //                                                     "offset": {
    //                                                         "type": "number",
    //                                                         "title": "栅格左侧的间隔格数"
    //                                                     },
    //                                                     "pull": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向左移动格数"
    //                                                     },
    //                                                     "push": {
    //                                                         "type": "number",
    //                                                         "title": "栅格向右移动格数"
    //                                                     },
    //                                                     "span": {
    //                                                         "type": "number",
    //                                                         "title": "占位格数"
    //                                                     },
    //                                                     "paddingTop": {
    //                                                         "type": "string",
    //                                                         "title": "上内边距（单位px）"
    //                                                     },
    //                                                     "paddingRight": {
    //                                                         "type": "string",
    //                                                         "title": "右内边距（单位px）"
    //                                                     },
    //                                                     "paddingBottom": {
    //                                                         "type": "string",
    //                                                         "title": "下内边距（单位px）"
    //                                                     },
    //                                                     "paddingLeft": {
    //                                                         "type": "string",
    //                                                         "title": "左内边距（单位px）"
    //                                                     },
    //                                                     "anchorId": {
    //                                                         "type": "string",
    //                                                         "title": "锚点ID"
    //                                                     },
    //                                                     "visible": {
    //                                                         "type": "boolean",
    //                                                         "title": "可见"
    //                                                     },
    //                                                     "buttonBox": {
    //                                                         "type": "boolean",
    //                                                         "title": "作为 button-box",
    //                                                         "default": false
    //                                                     },
    //                                                     "buttonAlign": {
    //                                                         "type": "string",
    //                                                         "title": "按钮对齐(勾选“作为 button-box”时生效)",
    //                                                         "enum": [
    //                                                             "left",
    //                                                             "center",
    //                                                             "right"
    //                                                         ],
    //                                                         "enumNames": [
    //                                                             "左对齐",
    //                                                             "居中",
    //                                                             "右对齐"
    //                                                         ],
    //                                                         "default": "left"
    //                                                     }
    //                                                 },
    //                                                 "required": [
    //                                                     "componentName",
    //                                                     "span"
    //                                                 ]
    //                                             },
    //                                             "uiSchema": {},
    //                                             "children": [
    //                                                 {
    //                                                     "componentId": "0c7d2451-9913-4b46-b9e8-62425eea47fd",
    //                                                     "componentName": "questionnaireSaveBtn",
    //                                                     "label": "Button",
    //                                                     "title": "按钮",
    //                                                     "category": "其他组件",
    //                                                     "icon": "gold",
    //                                                     "formData": {
    //                                                         "children": "保存",
    //                                                         "type": "primary",
    //                                                         "events": [
    //                                                             {
    //                                                                 "eventName": "onClick",
    //                                                                 "actions": [
    //                                                                     {
    //                                                                         "actionName": "jsAction",
    //                                                                         "scripts": "\n                    var questionnaireForm = ocWindow.getComponentByName('questionnaireForm');\n                    var validate = Form1.triggerValidationWhenSubmit();\n                    if(!validate){\n                      return;\n                    }\n                    var values = questionnaireForm.getFieldsValue();\n                    var entityName = 'com.open_care.survey.OCSurveyAnswer';\n\n                    // values['ocId'] = \n\n                    OC.save(entityName, values, function(response) {\n                      if (response && response.status === '0') {\n                        \n                      }\n                    }, function() {});\n                  "
    //                                                                     }
    //                                                                 ]
    //                                                             }
    //                                                         ],
    //                                                         "showBadge": false,
    //                                                         "visible": true,
    //                                                         "styles": "{}",
    //                                                         "overflowCount": 99,
    //                                                         "iconType": "icon",
    //                                                         "size": "default",
    //                                                         "seqno": 1,
    //                                                         "componentName": "questionnaireSaveBtn"
    //                                                     },
    //                                                     "schema": {
    //                                                         "type": "object",
    //                                                         "title": "formData",
    //                                                         "properties": {
    //                                                             "componentName": {
    //                                                                 "type": "string",
    //                                                                 "title": "组件名称"
    //                                                             },
    //                                                             "children": {
    //                                                                 "type": "string",
    //                                                                 "title": "名称"
    //                                                             },
    //                                                             "type": {
    //                                                                 "type": "string",
    //                                                                 "title": "类型",
    //                                                                 "enum": [
    //                                                                     "default",
    //                                                                     "primary",
    //                                                                     "ghost",
    //                                                                     "warning",
    //                                                                     "icon",
    //                                                                     "text"
    //                                                                 ],
    //                                                                 "enumNames": [
    //                                                                     "默认",
    //                                                                     "重要",
    //                                                                     "透明背景",
    //                                                                     "警告",
    //                                                                     "图标",
    //                                                                     "超链接文本"
    //                                                                 ]
    //                                                             },
    //                                                             "size": {
    //                                                                 "type": "string",
    //                                                                 "title": "尺寸",
    //                                                                 "enum": [
    //                                                                     "large",
    //                                                                     "default",
    //                                                                     "small"
    //                                                                 ],
    //                                                                 "enumNames": [
    //                                                                     "大",
    //                                                                     "中(默认)",
    //                                                                     "小"
    //                                                                 ],
    //                                                                 "default": "default"
    //                                                             },
    //                                                             "hotKeyNames": {
    //                                                                 "type": "string",
    //                                                                 "title": "快捷键，小写，如'ctrl+alt+q'"
    //                                                             },
    //                                                             "showBadge": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "显示徽标"
    //                                                             },
    //                                                             "badgeColor": {
    //                                                                 "type": "string",
    //                                                                 "title": "徽标颜色"
    //                                                             },
    //                                                             "badgeCount": {
    //                                                                 "type": "number",
    //                                                                 "title": "徽标显示数字(上下文)"
    //                                                             },
    //                                                             "overflowCount": {
    //                                                                 "type": "number",
    //                                                                 "title": "徽标最大显示数字"
    //                                                             },
    //                                                             "iconType": {
    //                                                                 "type": "string",
    //                                                                 "title": "图标类型",
    //                                                                 "enum": [
    //                                                                     "icon",
    //                                                                     "url"
    //                                                                 ],
    //                                                                 "default": "icon"
    //                                                             },
    //                                                             "icon": {
    //                                                                 "type": "string",
    //                                                                 "title": "图标"
    //                                                             },
    //                                                             "visible": {
    //                                                                 "type": "boolean",
    //                                                                 "title": "可见"
    //                                                             }
    //                                                         },
    //                                                         "required": [
    //                                                             "componentName",
    //                                                             "children"
    //                                                         ]
    //                                                     },
    //                                                     "uiSchema": {},
    //                                                     "children": [],
    //                                                     "events": [
    //                                                         {
    //                                                             "eventName": "onClick",
    //                                                             "actions": [
    //                                                                 {
    //                                                                     "actionName": "jsAction",
    //                                                                     "scripts": "\n           \n            var questionnaireForm = ocWindow.getComponentByName('questionnaireForm');\n            console.log('all params: ', questionnaireForm.allParams);\n\n            var validate = questionnaireForm.triggerValidationWhenSubmit();\n            if(!validate){\n              return;\n            }\n            var values = questionnaireForm.getFieldsValue();\n            var entityName = 'com.open_care.survey.OCSurveyAnswer';\n            \n            if (questionnaireForm.allParams.answerInstId) {\n              values.ocId = questionnaireForm.allParams.answerInstId;\n            }\n\n            values['survey#ocId'] = questionnaireForm.allParams.surveyId;\n            values['status'] = 'save';\n            values['customerId'] = questionnaireForm.allParams.customerId;\n            \n            OC.save(entityName, values, function(response) {\n              if (response && response.status === '0') {\n                if (!questionnaireForm.allParams.answerInstId) {\n                  questionnaireForm.allParams.answerInstId = response.data.data.ocId;\n                }\n              }\n            }, function() {});\n          "
    //                                                                 }
    //                                                             ]
    //                                                         }
    //                                                     ]
    //                                                 }
    //                                             ]
    //                                         }
    //                                     ]
    //                                 }
    //                             ]
    //                         }
    //                     ]
    //                 }
    //             ]
    //         }
    //     }
    // }

}
export async function deleteWindow(windowId) {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return axios.post(`${proxy}/deleteWin/${windowId}`,{}, { headers , responseType: 'json' });

}

export async function getQuestionnaireById(questionnaireId) {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    if (!isDevelopment) {
        return axios.post(`${proxy}/get/com.open_care.survey.OCSurvey/${questionnaireId}`,{}, { headers , responseType: 'json' });
    }
    // mock 
    return {
        data: {
            status: '0',
            msg: '',
            data: {
                data: {
                    "ocId": '123',
                    "windowId": "999",
                    "surveyConfig": {
                        "ocId": "456",
                        "theme": "主题数据",
                        "surveyModel" : {
                            "id": "9288c5eb-6e37-4a1b-85fc-c487b7e1c9ac",
                            "title": "北京市机关事务管理局单位员工健康状况调查问卷",
                            "description": "现代人久坐少动的生活方式，导致肌肉减少、运动能力下降，进而引发以下健康问题： 1、“肩、颈、腰、膝”等部位的慢性疼痛； 2、“超重、脂肪肝、高血压、糖尿病、高脂血症”等慢性代谢性疾病。 现代医学已经证明：正确、科学的运动能有效改善上述健康问题。 为改善各单位员工健康状况，落实“健康中国、健康企业建设”工作，现针对各单位员工的健康状况做一次调研摸底。 此次问卷调查共24题，请大家认真填写，以便于后续工作更有针对性、有效性！",
                            "type": "survey",
                            "questions": [
                                {
                                    "id": "cda555eb-f394-44c9-ad7d-71323cfc9412",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "1. 请标出您有异常的体检指标",
                                    "description": "",
                                    "options": [
                                        {
                                            "id": "a9679792-6b94-48e2-84ef-9e994a19be39",
                                            "value": "a9679792-6b94-48e2-84ef-9e994a19be39",
                                            "type": "option",
                                            "title": "超重"
                                        },
                                        {
                                            "id": "48ce0201-6554-4c5a-9c88-adee0af6d329",
                                            "value": "48ce0201-6554-4c5a-9c88-adee0af6d329",
                                            "type": "option",
                                            "title": "脂肪肝"
                                        },
                                        {
                                            "id": "cb988d57-17af-4639-bf0b-d83200070681",
                                            "value": "cb988d57-17af-4639-bf0b-d83200070681",
                                            "type": "option",
                                            "title": "高血压"
                                        },
                                        {
                                            "id": "14d3c06b-a909-45d0-9b3c-b4be27896ed4",
                                            "value": "14d3c06b-a909-45d0-9b3c-b4be27896ed4",
                                            "type": "option",
                                            "title": "高血糖"
                                        },
                                        {
                                            "id": "3e051fbd-67c3-4762-b081-fc0ff846c5f8",
                                            "value": "3e051fbd-67c3-4762-b081-fc0ff846c5f8",
                                            "type": "option",
                                            "title": "高血脂"
                                        },
                                        {
                                            "id": "75de55e4-6254-431a-9a9d-9f0f0d4d13ac",
                                            "value": "75de55e4-6254-431a-9a9d-9f0f0d4d13ac",
                                            "type": "option",
                                            "title": "高尿酸血症"
                                        },
                                        {
                                            "id": "1aaaffd0-4dc8-4ef0-ae15-d0d133f123e9",
                                            "value": "1aaaffd0-4dc8-4ef0-ae15-d0d133f123e9",
                                            "type": "option",
                                            "title": "以上皆无异常"
                                        }
                                    ],
                                    "required": false,
                                    "showSeqNo": true,
                                    "rules": [
                                        {
                                            "rule": {
                                                "treeValue": {
                                                    "id": "8baaa9b8-0123-4456-b89a-b17e95b4dcb1",
                                                    "type": "group",
                                                    "properties": {
                                                        "conjunction": "AND"
                                                    },
                                                    "children1": {
                                                        "8aabb899-89ab-4cde-b012-317e95b52d8e": {
                                                            "type": "rule",
                                                            "properties": {
                                                                "field": "answer",
                                                                "operator": "select_equals",
                                                                "value": [
                                                                    "a9679792-6b94-48e2-84ef-9e994a19be39"
                                                                ],
                                                                "valueSrc": [
                                                                    "value"
                                                                ],
                                                                "valueType": [
                                                                    "select"
                                                                ],
                                                                "operatorOptions": null
                                                            },
                                                            "id": "8aabb899-89ab-4cde-b012-317e95b52d8e",
                                                            "path": [
                                                                "8baaa9b8-0123-4456-b89a-b17e95b4dcb1",
                                                                "8aabb899-89ab-4cde-b012-317e95b52d8e"
                                                            ]
                                                        }
                                                    },
                                                    "path": [
                                                        "8baaa9b8-0123-4456-b89a-b17e95b4dcb1"
                                                    ]
                                                },
                                                "jsonLogicValue": {
                                                    "and": [
                                                        {
                                                            "==": [
                                                                {
                                                                    "var": "answer"
                                                                },
                                                                "a9679792-6b94-48e2-84ef-9e994a19be39"
                                                            ]
                                                        }
                                                    ]
                                                }
                                            },
                                            "actions": [
                                                {
                                                    "type": "goto_cmp",
                                                    "target_id": "b4e3747d-1854-4a03-98dc-5c82e2c4300a"
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    "id": "fcd1bbb7-4f17-4515-b628-29ae5ba2ff86",
                                    "type": "question",
                                    "answerType": "multiselect",
                                    "title": "2. 出现生理指标异常后您更愿意通过以下哪种途径解决？",
                                    "description": "",
                                    "showSeqNo": false,
                                    "options": [
                                        {
                                            "id": "c228b1e7-9d7e-492d-a2f5-bbe301f67c3d",
                                            "value": "c228b1e7-9d7e-492d-a2f5-bbe301f67c3d",
                                            "type": "option",
                                            "title": "药物治疗"
                                        },
                                        {
                                            "id": "3c5566e6-5788-44fb-a276-e82091303b7b",
                                            "value": "3c5566e6-5788-44fb-a276-e82091303b7b",
                                            "type": "option",
                                            "title": "运动健身"
                                        },
                                        {
                                            "id": "9e56aa5d-694f-4345-aedc-d03f41f32e9d",
                                            "value": "9e56aa5d-694f-4345-aedc-d03f41f32e9d",
                                            "type": "option",
                                            "title": "饮食管理"
                                        },
                                        {
                                            "id": "1b955eab-1619-4ade-bc6b-7d3501741d0e",
                                            "value": "1b955eab-1619-4ade-bc6b-7d3501741d0e",
                                            "type": "option",
                                            "title": "其他"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "b4e3747d-1854-4a03-98dc-5c82e2c4300a",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "3. 请选择一个选项日常工作，平均单次连续伏案工作时长？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "id": "a1d3e63b-819c-4aa2-9230-4838ac8cd030",
                                            "value": "a1d3e63b-819c-4aa2-9230-4838ac8cd030",
                                            "type": "option",
                                            "title": "1小时以内"
                                        },
                                        {
                                            "id": "ca66cfd3-14d6-4f60-b595-708ebf5c4e1e",
                                            "value": "ca66cfd3-14d6-4f60-b595-708ebf5c4e1e",
                                            "type": "option",
                                            "title": "1-2小时"
                                        },
                                        {
                                            "id": "f1f65238-3548-4601-a209-efd5594a8c36",
                                            "value": "f1f65238-3548-4601-a209-efd5594a8c36",
                                            "type": "option",
                                            "title": "2-3小时"
                                        },
                                        {
                                            "id": "4a82d5eb-7606-4a01-b38a-6e1ef9d5f120",
                                            "value": "4a82d5eb-7606-4a01-b38a-6e1ef9d5f120",
                                            "type": "option",
                                            "title": "3-4小时"
                                        },
                                        {
                                            "id": "e0fb57ce-0cdf-42a0-b7ff-fa2a3e667bec",
                                            "value": "e0fb57ce-0cdf-42a0-b7ff-fa2a3e667bec",
                                            "type": "option",
                                            "title": "4小时以上"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "3c61a42a-7c2a-4047-9f66-17ce0acff068",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "4. 请选择一个选项日常办公时，坐姿是否符合以下标准？（双脚平放地面，膝盖和腿部保持在一个高度或者略低于臀部，椅背约为100°-110°的倾斜角度）",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "id": "28e5207f-9cf3-470b-84e5-47b6751cc118",
                                            "value": "28e5207f-9cf3-470b-84e5-47b6751cc118",
                                            "type": "option",
                                            "title": "是"
                                        },
                                        {
                                            "id": "7553a043-93e5-4627-a78a-83d0c21cd1d5",
                                            "value": "7553a043-93e5-4627-a78a-83d0c21cd1d5",
                                            "type": "option",
                                            "title": "否"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "e8a6f817-4c60-4e67-8979-9b18ea800fac",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "5. 日常办公时，坐姿是否符合以下标准？（双肩放松，手肘微打开，手腕和手保持直线）",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "是",
                                            "value": "807319df-55f5-4a3b-a093-3aca893fee66",
                                            "componentId": "807319df-55f5-4a3b-a093-3aca893fee66"
                                        },
                                        {
                                            "label": "否",
                                            "value": "7c5063ca-90de-478d-aab1-7f2b66fed402",
                                            "componentId": "7c5063ca-90de-478d-aab1-7f2b66fed402"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "cb597db7-7e03-4fa7-a8d2-b78337e6f043",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "6. 请选择一个选项日常办公时，坐姿是否符合以下标准？（与显示器距离45-70cm，显示器显示第一行字微微低于视线或与视线齐平）",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "是",
                                            "value": "5b73c27f-85ca-490b-ae70-01cc8a4b820d",
                                            "componentId": "5b73c27f-85ca-490b-ae70-01cc8a4b820d"
                                        },
                                        {
                                            "label": "否",
                                            "value": "179d5fb7-a13f-45da-ab09-8af624ad02d0",
                                            "componentId": "179d5fb7-a13f-45da-ab09-8af624ad02d0"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "5100c772-e878-422f-a889-3bdead7dec97",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "7. 请选择一个选项您更愿意通过以下哪种方式改善长期伏案工作带来的不适感？（可多选）",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "工作操",
                                            "value": "70abdcc6-cffd-418b-bc78-c5c1d95ef3e4",
                                            "componentId": "70abdcc6-cffd-418b-bc78-c5c1d95ef3e4"
                                        },
                                        {
                                            "label": "中午休息或下班后统一组织运动训练",
                                            "value": "f42efcdf-985e-4f90-973d-a32443b62675",
                                            "componentId": "f42efcdf-985e-4f90-973d-a32443b62675"
                                        },
                                        {
                                            "label": "将专业指导方案发送到手机回家练习",
                                            "componentId": "1f6f858f-a953-4835-a258-fc57880fa915",
                                            "value": "1f6f858f-a953-4835-a258-fc57880fa915"
                                        },
                                        {
                                            "label": "休息",
                                            "componentId": "e3e6d381-532c-41fc-81bb-adae8dcb4a17",
                                            "value": "e3e6d381-532c-41fc-81bb-adae8dcb4a17"
                                        },
                                        {
                                            "label": "其他",
                                            "componentId": "74310ead-2f56-4b2f-a0f7-888fd114da97",
                                            "value": "74310ead-2f56-4b2f-a0f7-888fd114da97"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "22393160-49e7-4302-87de-14d6f6380ea1",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "8. 您现在运动的频率（每次30分钟以上）？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "0-1次/周",
                                            "value": "08a39a3f-4e48-4924-b655-b8a514dee80d",
                                            "componentId": "08a39a3f-4e48-4924-b655-b8a514dee80d"
                                        },
                                        {
                                            "label": "1-2次/周",
                                            "value": "15c0111a-2eef-4cde-b9c3-66fa36b8eef4",
                                            "componentId": "15c0111a-2eef-4cde-b9c3-66fa36b8eef4"
                                        },
                                        {
                                            "label": "2-3次/周",
                                            "componentId": "8e466a8d-2bb6-4bfe-96b9-e8f07f5fbb26",
                                            "value": "8e466a8d-2bb6-4bfe-96b9-e8f07f5fbb26"
                                        },
                                        {
                                            "label": "3次/周以上",
                                            "componentId": "868a484d-f1de-47b6-aecb-a90dc3f8ad8c",
                                            "value": "868a484d-f1de-47b6-aecb-a90dc3f8ad8c"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "b01efa5b-6643-4e3c-a5c2-dfdd4c093dcc",
                                    "type": "question",
                                    "answerType": "multiselect",
                                    "title": "9. 请勾选需要的选项您目前在做的运动项目？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "羽毛球",
                                            "value": "0f3d6acd-b3d9-4710-9de5-d79704ae03d1",
                                            "componentId": "0f3d6acd-b3d9-4710-9de5-d79704ae03d1"
                                        },
                                        {
                                            "label": "篮球",
                                            "value": "af747572-cde8-417e-b354-1c6da5408889",
                                            "componentId": "af747572-cde8-417e-b354-1c6da5408889"
                                        },
                                        {
                                            "label": "足球",
                                            "componentId": "f7f85e14-58ae-4ff0-974f-496986fa0626",
                                            "value": "f7f85e14-58ae-4ff0-974f-496986fa0626"
                                        },
                                        {
                                            "label": "乒乓球",
                                            "componentId": "23c24a62-b802-483c-a6ed-92c02d89c5b6",
                                            "value": "23c24a62-b802-483c-a6ed-92c02d89c5b6"
                                        },
                                        {
                                            "label": "跑步",
                                            "componentId": "95f2270c-c4fc-4eef-9218-8bea20b1bc2a",
                                            "value": "95f2270c-c4fc-4eef-9218-8bea20b1bc2a"
                                        },
                                        {
                                            "label": "户外徒步",
                                            "componentId": "33f2c75c-be1e-46d9-843b-59dd33588aa5",
                                            "value": "33f2c75c-be1e-46d9-843b-59dd33588aa5"
                                        },
                                        {
                                            "label": "自行车户外骑行",
                                            "componentId": "82c70142-573f-4a4b-9352-d5d37893abf5",
                                            "value": "82c70142-573f-4a4b-9352-d5d37893abf5"
                                        },
                                        {
                                            "label": "健身训练",
                                            "componentId": "dfa26c06-1534-45fc-bd70-ef542762ac51",
                                            "value": "dfa26c06-1534-45fc-bd70-ef542762ac51"
                                        },
                                        {
                                            "label": "瑜伽",
                                            "componentId": "bffe2856-c981-44a7-85fd-1cdbc97a4330",
                                            "value": "bffe2856-c981-44a7-85fd-1cdbc97a4330"
                                        },
                                        {
                                            "label": "游泳",
                                            "componentId": "7f45e36e-c101-4e5b-a227-cc71b3f280ee",
                                            "value": "7f45e36e-c101-4e5b-a227-cc71b3f280ee"
                                        },
                                        {
                                            "label": "其他",
                                            "componentId": "2dd00265-054e-49b1-86ac-cf24cdc322de",
                                            "value": "2dd00265-054e-49b1-86ac-cf24cdc322de"
                                        },
                                        {
                                            "label": "无",
                                            "componentId": "4c89ee7e-83e4-43db-ae1b-9cb18fd5d6e9",
                                            "value": "4c89ee7e-83e4-43db-ae1b-9cb18fd5d6e9"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "f5b23fa9-aa2f-4a6d-999d-45e9df1ace78",
                                    "type": "question",
                                    "answerType": "multiselect",
                                    "title": "10. 您希望通过运动实现哪些目标？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "慢性疼痛的预防及改善",
                                            "value": "5d60ca7c-7701-4c26-8d62-1baff1fda036",
                                            "componentId": "5d60ca7c-7701-4c26-8d62-1baff1fda036"
                                        },
                                        {
                                            "label": "慢病的预防及改善",
                                            "value": "3917a7f2-eef1-4142-952e-327cb9631d2f",
                                            "componentId": "3917a7f2-eef1-4142-952e-327cb9631d2f"
                                        },
                                        {
                                            "label": "减脂、塑形",
                                            "componentId": "7c2c0f7f-115c-4246-9b2e-9a7f62eb740c",
                                            "value": "7c2c0f7f-115c-4246-9b2e-9a7f62eb740c"
                                        },
                                        {
                                            "label": "参加比赛",
                                            "componentId": "f7ca5bc3-1156-480f-8942-d36f0a185c61",
                                            "value": "f7ca5bc3-1156-480f-8942-d36f0a185c61"
                                        },
                                        {
                                            "label": "其他",
                                            "componentId": "4c0e3084-3a32-47a4-9416-a989c4c19671",
                                            "value": "4c0e3084-3a32-47a4-9416-a989c4c19671"
                                        },
                                        {
                                            "label": "无",
                                            "componentId": "1fb64467-d4c1-4394-b4b8-ecc4f0dcaf10",
                                            "value": "1fb64467-d4c1-4394-b4b8-ecc4f0dcaf10"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "97f1f438-ae7d-4112-9fd6-7883097b6a54",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "11. 现在的运动帮助您实现了预期目标？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "很好",
                                            "value": "ecef4552-c311-44da-8c60-147a06cee7e5",
                                            "componentId": "ecef4552-c311-44da-8c60-147a06cee7e5"
                                        },
                                        {
                                            "label": "一般",
                                            "value": "c5ea2fda-08ea-479d-b2c3-f675cb22dfa9",
                                            "componentId": "c5ea2fda-08ea-479d-b2c3-f675cb22dfa9"
                                        },
                                        {
                                            "label": "不满意",
                                            "componentId": "ac5ab5b0-0980-495d-b660-e531f35d2224",
                                            "value": "ac5ab5b0-0980-495d-b660-e531f35d2224"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "790e8118-2106-465f-b869-d316f1a29748",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "12. 您是否需要运动训练专业指导？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "是",
                                            "value": "52a2ad14-f531-4a8a-b2e7-54f4da35ae74",
                                            "componentId": "52a2ad14-f531-4a8a-b2e7-54f4da35ae74"
                                        },
                                        {
                                            "label": "否",
                                            "value": "0f7fe289-08d3-4a1b-b710-b3d7f4b74165",
                                            "componentId": "0f7fe289-08d3-4a1b-b710-b3d7f4b74165"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "21deed6c-ca19-4ba1-ad41-5185cb9a2cf6",
                                    "type": "question",
                                    "answerType": "multiselect",
                                    "title": "13. 影响您参与运动的原因？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "没有时间",
                                            "value": "f5e2a8df-bed9-4bd5-81ef-9f6980d35818",
                                            "componentId": "f5e2a8df-bed9-4bd5-81ef-9f6980d35818"
                                        },
                                        {
                                            "label": "没有合适的场地（器械）",
                                            "value": "a53460dd-a48d-43b7-843b-6a7b59a0c830",
                                            "componentId": "a53460dd-a48d-43b7-843b-6a7b59a0c830"
                                        },
                                        {
                                            "label": "怕运动损伤",
                                            "componentId": "b6adb46e-8a9f-4c46-a8f7-ac9913c76a03",
                                            "value": "b6adb46e-8a9f-4c46-a8f7-ac9913c76a03"
                                        },
                                        {
                                            "label": "缺少教练指导",
                                            "componentId": "e96b7ee0-63aa-4c42-b5d3-30ed86db2b76",
                                            "value": "e96b7ee0-63aa-4c42-b5d3-30ed86db2b76"
                                        },
                                        {
                                            "label": "没有同伴",
                                            "componentId": "6f196dec-5a87-4708-be69-a04f52ac61c6",
                                            "value": "6f196dec-5a87-4708-be69-a04f52ac61c6"
                                        },
                                        {
                                            "label": "已有损伤存在，运动时有疼痛",
                                            "componentId": "cea7d940-da0a-4ad1-97fc-4406f5d5a218",
                                            "value": "cea7d940-da0a-4ad1-97fc-4406f5d5a218"
                                        },
                                        {
                                            "label": "其他",
                                            "componentId": "045ba92f-59f4-449f-9df7-b22d6353531a",
                                            "value": "045ba92f-59f4-449f-9df7-b22d6353531a"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "1cc1b419-6643-47ac-ac10-054cc8989bb2",
                                    "type": "question",
                                    "answerType": "multiselect",
                                    "title": "14. 是否有以下部位的疼痛困扰您？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "颈",
                                            "value": "725b1e8d-1454-4eac-9b9c-bfcf65e28f59",
                                            "componentId": "725b1e8d-1454-4eac-9b9c-bfcf65e28f59"
                                        },
                                        {
                                            "label": "肩",
                                            "value": "3ce8d7fb-1df2-46a6-a09a-8a4d4d0b7788",
                                            "componentId": "3ce8d7fb-1df2-46a6-a09a-8a4d4d0b7788"
                                        },
                                        {
                                            "label": "肘",
                                            "componentId": "ff02e92e-68c1-4269-abf3-158bcec94d51",
                                            "value": "ff02e92e-68c1-4269-abf3-158bcec94d51"
                                        },
                                        {
                                            "label": "腕",
                                            "componentId": "238231dc-d800-4b0b-af65-087c447e860f",
                                            "value": "238231dc-d800-4b0b-af65-087c447e860f"
                                        },
                                        {
                                            "label": "腰",
                                            "componentId": "3a43e15b-273a-4269-af1a-100ccc205db2",
                                            "value": "3a43e15b-273a-4269-af1a-100ccc205db2"
                                        },
                                        {
                                            "label": "髋",
                                            "componentId": "011772cf-ebed-453c-b30a-706b9e06c850",
                                            "value": "011772cf-ebed-453c-b30a-706b9e06c850"
                                        },
                                        {
                                            "label": "膝",
                                            "componentId": "d4f9b09e-5865-4e82-927c-9dadb4d73760",
                                            "value": "d4f9b09e-5865-4e82-927c-9dadb4d73760"
                                        },
                                        {
                                            "label": "踝",
                                            "componentId": "b83ff748-3964-4d63-8c49-503bb7fbd8fe",
                                            "value": "b83ff748-3964-4d63-8c49-503bb7fbd8fe"
                                        },
                                        {
                                            "label": "足底",
                                            "componentId": "186407eb-5468-45b7-8b2a-c96c81b7511e",
                                            "value": "186407eb-5468-45b7-8b2a-c96c81b7511e"
                                        },
                                        {
                                            "label": "其他",
                                            "componentId": "03a9d4d4-62ad-4c5f-adcb-a57e13e77974",
                                            "value": "03a9d4d4-62ad-4c5f-adcb-a57e13e77974"
                                        },
                                        {
                                            "label": "没有",
                                            "componentId": "cb9c0031-9b5d-46c7-a664-414aa5ea2859",
                                            "value": "cb9c0031-9b5d-46c7-a664-414aa5ea2859"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "80f62260-2a92-4f2d-b18d-e0f9d0d99020",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "15. 困扰您的主要疼痛最早何时出现？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "1个月内",
                                            "value": "393ee756-86dc-4e28-bd5a-c589228bee3a",
                                            "componentId": "393ee756-86dc-4e28-bd5a-c589228bee3a"
                                        },
                                        {
                                            "label": "2-3个月",
                                            "value": "c6bab68b-bab0-4318-9244-c720f2c04bb6",
                                            "componentId": "c6bab68b-bab0-4318-9244-c720f2c04bb6"
                                        },
                                        {
                                            "label": "4-6个月",
                                            "componentId": "9f93b122-7090-4493-9f0f-287c1eaab9f0",
                                            "value": "9f93b122-7090-4493-9f0f-287c1eaab9f0"
                                        },
                                        {
                                            "label": "6个月以上",
                                            "componentId": "bb3d3a29-8bd5-41ee-a2ef-798c107faa0e",
                                            "value": "bb3d3a29-8bd5-41ee-a2ef-798c107faa0e"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "d98a694e-ee81-4d87-b6dd-b6cfd7374485",
                                    "type": "question",
                                    "answerType": "multiselect",
                                    "title": "16. 疼痛的诱发与什么有关？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "活动量或运动量（动的多了会疼）",
                                            "value": "c446c428-70d9-4dfb-8c7f-ec92557ceb88",
                                            "componentId": "c446c428-70d9-4dfb-8c7f-ec92557ceb88"
                                        },
                                        {
                                            "label": "长时间保持一个姿势",
                                            "value": "8a186a6c-5abc-4141-87a4-b5a0d22ba8f6",
                                            "componentId": "8a186a6c-5abc-4141-87a4-b5a0d22ba8f6"
                                        },
                                        {
                                            "label": "某一特定姿势或动作",
                                            "componentId": "30fc7d8d-21fb-414d-a20f-4f0c4f8239e2",
                                            "value": "30fc7d8d-21fb-414d-a20f-4f0c4f8239e2"
                                        },
                                        {
                                            "label": "天气",
                                            "componentId": "cccf2567-c7e4-4646-821e-bd994ad683d3",
                                            "value": "cccf2567-c7e4-4646-821e-bd994ad683d3"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "725cd6de-af4b-42aa-9f11-7533fc58dacc",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "17. 疼痛的程度?",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "无",
                                            "value": "22ad4192-bc1e-49e9-bdf6-2d10c622fa06",
                                            "componentId": "22ad4192-bc1e-49e9-bdf6-2d10c622fa06"
                                        },
                                        {
                                            "label": "有很稍微的痛",
                                            "value": "40374fd4-1501-4f73-b7ed-9af332958d95",
                                            "componentId": "40374fd4-1501-4f73-b7ed-9af332958d95"
                                        },
                                        {
                                            "label": "较明显的痛（中度）",
                                            "componentId": "7fbd5696-3acb-441f-a078-9a55bf71a3bb",
                                            "value": "7fbd5696-3acb-441f-a078-9a55bf71a3bb"
                                        },
                                        {
                                            "label": "明显的痛（相当严重）",
                                            "componentId": "59d28d46-8349-4071-a171-13de5da3b93a",
                                            "value": "59d28d46-8349-4071-a171-13de5da3b93a"
                                        },
                                        {
                                            "label": "严重的痛（非常严重）",
                                            "componentId": "0299bf35-b6f9-47d8-8bcc-e6422e481fdf",
                                            "value": "0299bf35-b6f9-47d8-8bcc-e6422e481fdf"
                                        },
                                        {
                                            "label": "痛到不能做任何事",
                                            "componentId": "6585b02d-222f-46b4-87cc-9743e1cf7652",
                                            "value": "6585b02d-222f-46b4-87cc-9743e1cf7652"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "798596bc-3137-4006-a367-5560e4eb6d9e",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "18. 请选择一个选项关于肩颈腰椎疼痛引起的睡眠问题？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "无",
                                            "value": "f268e7b3-4b3a-4ede-ac97-350088848beb",
                                            "componentId": "f268e7b3-4b3a-4ede-ac97-350088848beb"
                                        },
                                        {
                                            "label": "对睡眠无影响",
                                            "value": "97d88a97-1ddf-4b41-955b-733a963d960a",
                                            "componentId": "97d88a97-1ddf-4b41-955b-733a963d960a"
                                        },
                                        {
                                            "label": "睡眠偶尔会受到干扰（不超过一个小时）",
                                            "componentId": "518b5547-dfd7-4b11-837c-f371677de779",
                                            "value": "518b5547-dfd7-4b11-837c-f371677de779"
                                        },
                                        {
                                            "label": "睡眠会受到轻微程度干扰（一到两个小时）",
                                            "componentId": "1c10a202-8d8d-4d8c-b894-18a6008ceee1",
                                            "value": "1c10a202-8d8d-4d8c-b894-18a6008ceee1"
                                        },
                                        {
                                            "label": "睡眠会受到中等程度干扰（两到三个小时）",
                                            "componentId": "39234767-6395-4851-828d-8e32ccb507a4",
                                            "value": "39234767-6395-4851-828d-8e32ccb507a4"
                                        },
                                        {
                                            "label": "睡眠会受到严重程度干扰（三到五个小时）",
                                            "componentId": "bbda6c8f-8796-44d1-9527-5fac2161838a",
                                            "value": "bbda6c8f-8796-44d1-9527-5fac2161838a"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "d1ad10c5-d47b-40dc-9177-8099d092d066",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "19. 请选择一个选项提东西时腰腿痛或颈部疼痛?",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "无",
                                            "value": "3b8d7106-b5f1-43d7-a0ca-83512229a8ab",
                                            "componentId": "3b8d7106-b5f1-43d7-a0ca-83512229a8ab"
                                        },
                                        {
                                            "label": "我能搬重物而不会有任何疼痛",
                                            "value": "5c859f7c-ec1c-4c8b-a39a-fa93cb832525",
                                            "componentId": "5c859f7c-ec1c-4c8b-a39a-fa93cb832525"
                                        },
                                        {
                                            "label": "我能搬东西但会有点疼痛",
                                            "componentId": "32962d01-1cfc-41c8-8203-946819e398cb",
                                            "value": "32962d01-1cfc-41c8-8203-946819e398cb"
                                        },
                                        {
                                            "label": "我会因疼痛无法搬地上的重物，但其他较容易的搬运方式则还可以，例如：搬桌上的重物",
                                            "componentId": "2b4a8ae4-d413-4478-a5e8-29d4a6b67b3a",
                                            "value": "2b4a8ae4-d413-4478-a5e8-29d4a6b67b3a"
                                        },
                                        {
                                            "label": "我会因疼痛而无法搬地上的重物，但其他较容易的搬运方式则还可以，例如：搬桌上轻到中等重量的物品",
                                            "componentId": "60e57205-a80e-4ac4-9d28-5402766e125c",
                                            "value": "60e57205-a80e-4ac4-9d28-5402766e125c"
                                        },
                                        {
                                            "label": "我只能搬轻的物品",
                                            "componentId": "b1e75ebb-f69e-4ffe-b189-f3a974723ab7",
                                            "value": "b1e75ebb-f69e-4ffe-b189-f3a974723ab7"
                                        },
                                        {
                                            "label": "我无法搬任何物品",
                                            "componentId": "876f7cbc-a1e2-4c4f-8711-688846a9772d",
                                            "value": "876f7cbc-a1e2-4c4f-8711-688846a9772d"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "71973bdd-9f16-4079-b384-d6272889cbdb",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "20. 请选择一个选项长坐后的腰腿痛？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "无",
                                            "value": "0eecc036-28e5-47b2-875a-4b88224912ec",
                                            "componentId": "0eecc036-28e5-47b2-875a-4b88224912ec"
                                        },
                                        {
                                            "label": "只要椅子高度合适。想坐多久坐多久",
                                            "value": "8b1748ef-6b8d-4415-a851-e7ab2c802942",
                                            "componentId": "8b1748ef-6b8d-4415-a851-e7ab2c802942"
                                        },
                                        {
                                            "label": "由于疼痛加重，最多只能坐1个小时",
                                            "componentId": "1fa00948-1069-49c9-8ae6-e66b068b48d3",
                                            "value": "1fa00948-1069-49c9-8ae6-e66b068b48d3"
                                        },
                                        {
                                            "label": "由于疼痛加重，最多只能坐半个小时",
                                            "componentId": "6e953e16-d355-464e-b8fe-d9562682347f",
                                            "value": "6e953e16-d355-464e-b8fe-d9562682347f"
                                        },
                                        {
                                            "label": "由于疼痛加重，最多只能坐10分钟",
                                            "componentId": "4d6de38f-ad89-4591-909f-8cda928b2b53",
                                            "value": "4d6de38f-ad89-4591-909f-8cda928b2b53"
                                        },
                                        {
                                            "label": "由于疼痛加重，一点也不敢坐",
                                            "componentId": "85f7f2ef-139d-4917-8599-9a67d9038721",
                                            "value": "85f7f2ef-139d-4917-8599-9a67d9038721"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "4fc8cbdc-eb5a-466c-a0ee-2227fdfc1a78",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "21. 开车后的颈部不适？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "无",
                                            "value": "03901fb5-75bd-4855-8a03-4a9ca8961020",
                                            "componentId": "03901fb5-75bd-4855-8a03-4a9ca8961020"
                                        },
                                        {
                                            "label": "我能长时间开车而不会有任何的脖子痛",
                                            "value": "fb8696f0-78a8-4b9b-b8b6-59363cbb8b02",
                                            "componentId": "fb8696f0-78a8-4b9b-b8b6-59363cbb8b02"
                                        },
                                        {
                                            "label": "我能长时间开车而脖子只是轻微痛",
                                            "componentId": "91899192-430a-486a-9981-76532b295e38",
                                            "value": "91899192-430a-486a-9981-76532b295e38"
                                        },
                                        {
                                            "label": "我能长时间开车而脖子只是中等程度的疼痛",
                                            "componentId": "00889536-4ca7-43d9-b6d2-7e9803653a00",
                                            "value": "00889536-4ca7-43d9-b6d2-7e9803653a00"
                                        },
                                        {
                                            "label": "我不能长时间开车，因为脖子会中等程度的疼痛",
                                            "componentId": "7d7056a5-34d1-459f-bf20-2ff2df592757",
                                            "value": "7d7056a5-34d1-459f-bf20-2ff2df592757"
                                        },
                                        {
                                            "label": "我不太能长时间开车，因为脖子会严重程度的痛",
                                            "componentId": "1efb6d49-716c-4be0-acfa-f5e13dfeb2de",
                                            "value": "1efb6d49-716c-4be0-acfa-f5e13dfeb2de"
                                        },
                                        {
                                            "label": "我完全都不能开车",
                                            "componentId": "f57bb163-6116-498e-869e-803dfb70d940",
                                            "value": "f57bb163-6116-498e-869e-803dfb70d940"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "14b2d388-072b-4bfd-9f4c-f6cfe9e3fce0",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "22.请选择一个选项长时间看电脑后的颈部不适？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "无",
                                            "value": "e17101c1-0d25-454a-a066-a322fb327a20",
                                            "componentId": "e17101c1-0d25-454a-a066-a322fb327a20"
                                        },
                                        {
                                            "label": "选项2我能长时间看电脑而不会有脖子痛",
                                            "value": "dd601adc-4a91-475f-a5a5-10559e27e475",
                                            "componentId": "dd601adc-4a91-475f-a5a5-10559e27e475"
                                        },
                                        {
                                            "label": "我能长时间看电脑而脖子只会轻微的痛",
                                            "componentId": "7c93c8ec-45e3-4669-bbe8-9224a754f5ec",
                                            "value": "7c93c8ec-45e3-4669-bbe8-9224a754f5ec"
                                        },
                                        {
                                            "label": "我能长时间看电脑而脖子会中等程度的痛",
                                            "componentId": "f6c769fa-dfd6-435d-bb38-f66c0c1d1035",
                                            "value": "f6c769fa-dfd6-435d-bb38-f66c0c1d1035"
                                        },
                                        {
                                            "label": "我不能长时间看电脑，因为脖子会中等程度的痛",
                                            "componentId": "72c17552-5783-4e21-8842-64f165aa014d",
                                            "value": "72c17552-5783-4e21-8842-64f165aa014d"
                                        },
                                        {
                                            "label": "我不能长时间看电脑，因为脖子严重程度的痛",
                                            "componentId": "87a83aa3-48e3-43ae-85d0-d62d13ec524a",
                                            "value": "87a83aa3-48e3-43ae-85d0-d62d13ec524a"
                                        },
                                        {
                                            "label": "我完全不能看电脑（严重影响日常工作）",
                                            "componentId": "2983d8be-b513-46ff-a896-c646ab89afbd",
                                            "value": "2983d8be-b513-46ff-a896-c646ab89afbd"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "b239dc7f-f2a4-4eb1-8376-e1d4ba584a38",
                                    "type": "question",
                                    "answerType": "select",
                                    "title": "23.请选择一个选项颈肩腰膝等慢性疼痛对于社会活动的影响？",
                                    "description": "",
                                    "showSeqNo": true,
                                    "options": [
                                        {
                                            "label": "无",
                                            "value": "ab894efb-84af-4279-aff8-5502dd5ddf3b",
                                            "componentId": "ab894efb-84af-4279-aff8-5502dd5ddf3b"
                                        },
                                        {
                                            "label": "社会活动完全正常，绝不会因为活动导致疼痛加重",
                                            "value": "a9094c4e-c589-4cda-8f52-fe47310a49d8",
                                            "componentId": "a9094c4e-c589-4cda-8f52-fe47310a49d8"
                                        },
                                        {
                                            "label": "社会活动完全正常，但这些活动会加重疼痛",
                                            "componentId": "1db853ce-5c4f-4edd-979c-d51762c7987f",
                                            "value": "1db853ce-5c4f-4edd-979c-d51762c7987f"
                                        },
                                        {
                                            "label": "疼痛限制了剧烈活动，如运动，但对参加其他日常类社会活动没有明显影响",
                                            "componentId": "3fc6907b-7cbe-47aa-a2f7-2fd52634f8e5",
                                            "value": "3fc6907b-7cbe-47aa-a2f7-2fd52634f8e5"
                                        },
                                        {
                                            "label": "由于疼痛限制了正常的社会活动，以致不能参加某些经常性的活动",
                                            "componentId": "06df5d52-9877-4924-b047-e680375b6879",
                                            "value": "06df5d52-9877-4924-b047-e680375b6879"
                                        },
                                        {
                                            "label": "由于疼痛限制参加社会活动，只能在家从事一些社会活动",
                                            "componentId": "f49df670-423a-4af9-93f0-d6e4ce3348d9",
                                            "value": "f49df670-423a-4af9-93f0-d6e4ce3348d9"
                                        },
                                        {
                                            "label": "由于疼痛根本无法从事任何社会活动",
                                            "componentId": "7080f361-955b-43ba-9a99-d9a1b5b98a34",
                                            "value": "7080f361-955b-43ba-9a99-d9a1b5b98a34"
                                        }
                                    ],
                                    "required": false
                                },
                                {
                                    "id": "53b770af-8403-416e-973c-7c09e5bc2ab4",
                                    "type": "question",
                                    "answerType": "text",
                                    "showSeqNo": true,
                                    "title": "24.标题您对员工健康管理运动方面的其他合理化建议？",
                                    "required": false
                                }
                            ]
                        }
                    }
                }
            }
        }
    }
}

export async function systemContext() {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return axios.get(`${proxy}/systemContext`, { headers, responseType: 'json' });
}
export async function commonActions() {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return axios.get(`${proxy}/commonActions`, { headers, responseType: 'json' });
}
export async function windowContext(windowId) {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return axios.get(`${proxy}/windowContext/${windowId}`, { headers, responseType: 'json' });
    
}
export async function processDefinitions() {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return axios.get(`${proxyURL}/bpm/processDefinitions`, { headers, responseType: 'json' });
}
export async function getDrools() {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return axios.get(`${proxy}/getDrools`, { headers, responseType: 'json' });
}
export async function userTaskDefinitions() {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return axios.get(`${proxy}/bpm/userTaskDefinitions`, { headers, responseType: 'json' });
}

export async function saveNavigation(data) {
    const { appContext,currentPage:{pageMode} } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    if (!isDevelopment) {
        return axios.post(`${proxy}/saveNavigation`, data, { headers , responseType: 'json' });
    }

    return {
        data: {"msg":"","data":{"id":"a03bd06b-8e18-4601-889e-bd22f5a796c9","name":null,"type":"siderMenu","path":null,"icon":null,"windowId":null,"componentId":"08459EC56B0445D2ACAA942B3C56BE4A","componentName":"siderMenu10456","title":null,"visible":false,"disabled":false,"develop":false,"children":[{"id":"7d771c52-213d-4f54-b53c-276643183313","name":"员工管理","type":"metaMenu","path":"employee","icon":"user","windowId":"5d364379-8cea-4dd5-a6ad-fb7c50447e44","componentId":"F87509C2A210450DA34EB24DF566E43D","componentName":"metaMenu12140","title":"员工管理","visible":true,"disabled":true,"develop":false,"children":[],"homePage":false},{"id":"cf068bc6-9d8e-4f4b-a6b6-b4304c766243","name":"字典设置","type":"metaMenu","path":"dictionary","icon":"user","windowId":"af863dd5-e473-4f5e-a1a7-ef9b1fdb206e","componentId":"B92BC4846ACA4C0FB2BDA1C2BF1EBE60","componentName":"metaMenu12144","title":"字典设置","visible":true,"disabled":true,"develop":true,"children":[],"homePage":false},{"id":"92d10fc4-971d-41e1-87e7-89dcb99f2318","name":"消息配置","type":"metaMenu","path":"msgConfig","icon":"user","windowId":"c223dd44-33af-49b2-ab96-36a3d1b226f3","componentId":"FD0399129F4940779E83B496AE4B69F7","componentName":"metaMenu12181","title":"消息配置","visible":false,"disabled":true,"develop":true,"children":[],"homePage":false},{"id":"b3105937-abfd-41c6-98bc-296158276f8c","name":"消息模板","type":"metaMenu","path":"msgTemplate","icon":"user","windowId":"a460c14a-05bb-4045-92ec-46e126311c7b","componentId":"B79709A3388C4091A04287B66F35908B","componentName":"metaMenu12280","title":"消息模板","visible":false,"disabled":true,"develop":true,"children":[],"homePage":false},{"id":"dc060d86-f76a-4437-8e51-f2000a23c25b","name":"消息渠道","type":"metaMenu","path":"msgChannel","icon":"user","windowId":"d1c97bfe-a440-4e0c-a33c-a6436090acf6","componentId":"8672154FC3AB44C881983BAE98654E6C","componentName":"metaMenu12423","title":"消息渠道","visible":false,"disabled":true,"develop":true,"children":[],"homePage":false},{"id":"7390be20-11ba-499e-b095-2e1b12e038bb","name":"角色管理","type":"metaMenu","path":"role","icon":"user","windowId":"9e2bd4a1-d060-4d4a-9901-089517e7902b","componentId":"6A9F5738AC8B443F9F979954E38CFAA0","componentName":"metaMenu12436","title":"角色管理","visible":true,"disabled":true,"develop":true,"children":[],"homePage":false},{"id":"51c017b1-9c4d-425e-924a-0e8ddeebe0da","name":"流程管理","type":"metaMenu","path":"process","icon":"user","windowId":"6b0c57ba-c7d8-41bf-a09d-ecdd502a1a07","componentId":"60F4F4D6073447968FC840E733F757E7","componentName":"metaMenu12449","title":"流程管理","visible":true,"disabled":true,"develop":true,"children":[],"homePage":false},{"id":"fa0f40f3-3816-4c0c-b1cd-3baed5724956","name":"供应商维护","type":"metaMenu","path":null,"icon":"user","windowId":"9d124c8e-6f76-4db5-b23b-6d0317c1b674","componentId":"80a334d2-09f2-4fa0-b916-59f0213dd88e","componentName":"Menu1","title":"供应商维护","visible":true,"disabled":false,"develop":false,"children":[],"homePage":false},{"id":"4b76c96d-3a40-4bb5-84c7-90e3b0c756b4","name":"服务项目维护","type":"metaMenu","path":null,"icon":"user","windowId":null,"componentId":"13d67e9e-f428-4f49-a780-a0157e28194e","componentName":"Menu2","title":"服务项目维护","visible":true,"disabled":false,"develop":false,"children":[],"homePage":false},{"id":"26e0977e-3587-4a98-a17c-5c1eb00e200f","name":"服务项目申请","type":"metaMenu","path":null,"icon":"user","windowId":null,"componentId":"a1d67b95-5248-486e-adaa-5aa4841c183f","componentName":"Menu3","title":"服务项目申请","visible":true,"disabled":false,"develop":false,"children":[],"homePage":false},{"id":"d0b2d992-57d5-47e2-a110-ce83f6af3c20","name":"新增菜单","type":"metaMenu","path":null,"icon":"user","windowId":"c92259f6-73e2-446e-aebd-33364d93da79","componentId":"6ab30b90-dacb-4bc0-aaa1-04747876a44f","componentName":"Menu4","title":"新增菜单","visible":true,"disabled":false,"develop":false,"children":[],"homePage":false},{"id":"ede13049-e076-4e03-874c-be4b278208be","name":"新增菜单","type":"metaMenu","path":null,"icon":"user","windowId":"da7145a4-37cb-4c0c-82ef-efe2b46a067f","componentId":"055519af-2768-4cec-9955-11ac130606b5","componentName":"Menu5","title":"新增菜单","visible":true,"disabled":false,"develop":false,"children":[],"homePage":false}],"homePage":false},"status":"0"}
    }
}
export async function deleteNavigation(componentId) {
    const { appContext,currentPage:{pageMode} } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    if (!isDevelopment) {
        return axios.get(`${proxy}/deleteNavigation/${componentId}?client=${pageMode}`, { headers, responseType: 'json' });
    }

    return {
        data: {"msg":"","data":null,"status":"0"}
    }
}
export async function getNavigation() {
    const { appContext,currentPage:{pageMode} } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)
    if (!isDevelopment) {
        return axios.get(`${proxy}/getNavigation?client=${pageMode}`, { headers, responseType: 'json' });
    }
    return {
        data:{
            msg:"",
            data: {
                "id": "7598e3f8-e11a-6666-a81f-c8f9ebb34504",
                "name": null,
                "type": "siderMenu",
                "path": null,
                "icon": null,
                "windowId": null,
                "componentId": "E0838F75D0AD4F2FB803C8A9D4385E2D",
                "componentName": "siderMenu8255",
                "title": null,
                "client": null,
                "selectedIcon": null,
                "seqno": null,
                "visible": true,
                "disabled": false,
                "develop": false,
                "children": [
                  {
                    "id": "f9f1ca2f-fd1e-43cd-8fa3-4b094e95329a",
                    "name": "我的工作",
                    "type": "parentMenu",
                    "path": "Menu2",
                    "icon": "/0/icon/darkIcon/work.png",
                    "windowId": null,
                    "componentId": "416b3c66-ae6e-477e-9781-349ac69b179e",
                    "componentName": "Menu2",
                    "title": "我的工作",
                    "client": null,
                    "selectedIcon": "/0/icon/darkIcon/work.png",
                    "seqno": 0,
                    "visible": true,
                    "disabled": false,
                    "develop": false,
                    "children": [
                      {
                        "id": "b7cd0eed-4eac-4b47-acb0-026b5782c654",
                        "name": "待办事项",
                        "type": "parentMenu",
                        "path": "Menu6",
                        "icon": "caret-right",
                        "windowId": null,
                        "componentId": "c9335221-b872-49a5-8c1a-d46341937b99",
                        "componentName": "Menu6",
                        "title": "待办事项",
                        "client": null,
                        "selectedIcon": "caret-right",
                        "seqno": 0,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [
                          {
                            "id": "1ae239cc-007b-4803-bc6c-e5fd4a27317f",
                            "name": "共享池",
                            "type": "metaMenu",
                            "path": null,
                            "icon": "/0/icon/darkIcon/empty_icon.png",
                            "windowId": "05bd1c2a-8524-4785-ab29-5f1d00a38935",
                            "componentId": "e5b66f4a-71a3-4e2d-9bed-f1ada22d678a",
                            "componentName": "Menu7",
                            "title": "共享池",
                            "client": null,
                            "selectedIcon": null,
                            "seqno": 0,
                            "visible": true,
                            "disabled": false,
                            "develop": false,
                            "children": [],
                            "homePage": false
                          },
                          {
                            "id": "a2194655-a37f-40b8-89f2-6df4a872c6b9",
                            "name": "个人池",
                            "type": "metaMenu",
                            "path": null,
                            "icon": "/0/icon/darkIcon/empty_icon.png",
                            "windowId": "a25749ab-bc16-48a6-bd7c-890cb98f7a94",
                            "componentId": "66b5762d-5de6-450d-b544-ad285b8ee78b",
                            "componentName": "Menu8",
                            "title": "个人池",
                            "client": null,
                            "selectedIcon": null,
                            "seqno": 1,
                            "visible": true,
                            "disabled": false,
                            "develop": false,
                            "children": [],
                            "homePage": false
                          }
                        ],
                        "homePage": false
                      },
                      {
                        "id": "47e7a6a0-f932-4337-8baf-a7f098b4db6b",
                        "name": "已办事项",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "25e1331e-355b-4d8f-9e16-c33305fec689",
                        "componentId": "774a341c-ff10-4393-a55d-114936a38ad0",
                        "componentName": "Menu9",
                        "title": "已办事项",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 1,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "47e7a6a0-f932-4337-8baf-a7f098b4db6b2",
                        "name": "已办事项2",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "25e1331e-355b-4d8f-9e16-c33305fec6892",
                        "componentId": "774a341c-ff10-4393-a55d-114936a38ad02",
                        "componentName": "Menu92",
                        "title": "已办事项2",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 1,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "47e7a6a0-f932-4337-8baf-a7f098b4db6b3",
                        "name": "已办事项3",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "25e1331e-355b-4d8f-9e16-c33305fec6893",
                        "componentId": "774a341c-ff10-4393-a55d-114936a38ad03",
                        "componentName": "Menu93",
                        "title": "已办事项3",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 1,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "47e7a6a0-f932-4337-8baf-a7f098b4db6b4",
                        "name": "已办事项4",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "25e1331e-355b-4d8f-9e16-c33305fec6894",
                        "componentId": "774a341c-ff10-4393-a55d-114936a38ad04",
                        "componentName": "Menu94",
                        "title": "已办事项4",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 1,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "47e7a6a0-f932-4337-8baf-a7f098b4db6b5",
                        "name": "已办事项5",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "25e1331e-355b-4d8f-9e16-c33305fec6895",
                        "componentId": "774a341c-ff10-4393-a55d-114936a38ad05",
                        "componentName": "Menu95",
                        "title": "已办事项5",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 1,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "5442f002-0ce1-4a0a-a919-eaef59908ebd",
                        "name": "草稿箱",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "37e06d73-c736-4aca-9cdc-77c5341f115e",
                        "componentId": "e154cd1e-7dd6-41e7-be92-500e98684e24",
                        "componentName": "Menu5",
                        "title": "草稿箱",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 2,
                        "visible": false,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      }
                    ],
                    "homePage": false
                  },
                  {
                    "id": "3e5f6ef2-0e23-43a7-b11f-70cf9ff2935a",
                    "name": "供应商管理",
                    "type": "parentMenu",
                    "path": "Menu3",
                    "icon": "/0/icon/darkIcon/supplier3.png",
                    "windowId": null,
                    "componentId": "a6ee1b80-5b07-483b-abdc-b8dfe6553d3d",
                    "componentName": "Menu3",
                    "title": "供应商管理",
                    "client": null,
                    "selectedIcon": "/0/icon/darkIcon/supplier3.png",
                    "seqno": 1,
                    "visible": true,
                    "disabled": false,
                    "develop": false,
                    "children": [
                      {
                        "id": "204b0e71-91b7-4b1d-972e-195492643e95",
                        "name": "供应商组织机构配置",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "a66927b9-8747-4aae-b3c7-20873fb7dddf",
                        "componentId": "951eb35d-8a10-411e-9d50-2a9f526e098e",
                        "componentName": "Menu13",
                        "title": "供应商组织机构配置",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 0,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "37a12121-3ee6-449f-bec5-9547dc566df4",
                        "name": "供应商分类",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "8120516e-77c6-4ea6-a42f-1c0bf3646c4a",
                        "componentId": "5a95d9e9-2992-4251-9f58-d73b0cc6990d",
                        "componentName": "Menu101",
                        "title": "供应商分类",
                        "client": null,
                        "selectedIcon": "/0/icon/darkIcon/empty_icon.png",
                        "seqno": 1,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "75862cc2-ed7d-4650-b3a2-13593426615c",
                        "name": "供应商维护",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "599043fd-bf9b-4490-a6a3-5f2bb0ff7a68",
                        "componentId": "3d596f67-5eb5-4ec4-b241-968445479d9d",
                        "componentName": "Menu10",
                        "title": "供应商维护",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 2,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "7c925904-b7a9-47d4-be04-ced09a1d892c",
                        "name": "供应商查询",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "6b949173-9fe8-4924-8722-d9e7113995ad",
                        "componentId": "8644647d-c8e6-4ddf-864c-a592b058a888",
                        "componentName": "Menu11",
                        "title": "供应商查询",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 3,
                        "visible": false,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "f5737001-42ca-4030-9676-dd83fc5ec01f",
                        "name": "服务项目查询",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "e38343ce-753c-4c67-9edf-de2bff565052",
                        "componentId": "d7ec474f-35b1-41c7-b465-99640abcd5ca",
                        "componentName": "Menu12",
                        "title": "服务项目查询",
                        "client": null,
                        "selectedIcon": "/0/icon/darkIcon/empty_icon.png",
                        "seqno": 4,
                        "visible": false,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "5a94db7c-20bf-4128-9ef7-02420a0aa21c",
                        "name": "测试页面",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "3d66fdb9-0609-405e-8aa2-562a1bef9f25",
                        "componentId": "6db85c92-3aa6-4ae6-a1c9-723397ee18b3",
                        "componentName": "Menu19",
                        "title": "测试页面",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 5,
                        "visible": false,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "029513bd-e87d-4da4-a06b-e9a58612ca52",
                        "name": "供应商门店管理",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "1ceb35ed-bf44-4aec-947e-de14d887647c",
                        "componentId": "013abb9e-2a44-48dc-8120-fe42864f8d73",
                        "componentName": "Menu21",
                        "title": "供应商门店管理",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 6,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "31ff3f0e-1a70-43d7-8efe-aba11fda3e42",
                        "name": "服务门店管理",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "274bfbad-1f4a-43f3-8554-7ff0dc6f9bd0",
                        "componentId": "7ac4263e-161f-4847-bc93-69eb0a850c83",
                        "componentName": "Menu48",
                        "title": "服务门店管理",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 7,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "17cf13ce-95c0-4289-9280-380182509c91",
                        "name": "供应商账户信息",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "90d8df45-0845-4c04-b62e-92cf5e973ef1",
                        "componentId": "e3ce22c0-299b-4a9b-8407-dc5312f530b3",
                        "componentName": "Menu44",
                        "title": "供应商账户信息",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 8,
                        "visible": false,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      },
                      {
                        "id": "fcb0394c-2961-4f03-985d-1a56850c68e6",
                        "name": "合同维护",
                        "type": "metaMenu",
                        "path": null,
                        "icon": "/0/icon/darkIcon/empty_icon.png",
                        "windowId": "ed2d9c6a-e090-4129-a06a-68932b1b9546",
                        "componentId": "8f1531ac-5ff8-4f36-978c-e623f2216ae2",
                        "componentName": "Menu59",
                        "title": "合同维护",
                        "client": null,
                        "selectedIcon": null,
                        "seqno": 9,
                        "visible": true,
                        "disabled": false,
                        "develop": false,
                        "children": [],
                        "homePage": false
                      }
                    ],
                    "homePage": false
                  },
                ],
                "homePage": false
              },
            status: '0'

        }
    }
}
export async function getSiderMenuWindow() {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)

    return axios.get(`${proxy}/getSiderMenuWindow`, { headers, responseType: 'json' });
}
export async function themeSettings() {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)
    if (!isDevelopment) {
        return axios.get(`${proxy}/getAppTheme`, { headers, responseType: 'json' });
    }
    

    return {
        data: {
            data: {
                "primaryColor": "#1890FF",
                "loadingDelay": 0,
                "navTheme": "dark",
                "layout": "sidemenu",
                "fontFamily": "Microsoft YaHei",
                "documentTitleTemplate": "${windowName}-${documentTitle}",
                "globalCustomStyles": "",
                "showMobileTabBar": true,
                "headHeight": "64px",
                "projectClassName": "",
                "hasMobileNavBar": true,
                "useWebsocket": true,
                "tableSize": "default",
                "contentBackgroundColor": "",
                "tableBordered": false,
                "cardBordered": true,
                "collapseBordered": true,
                "formItemLabelWidthAdaptation": false,
                "themeName": "default",
                "navFold": "onlyOne",
                "autoHideMenu": false,
                "autoHideTop": false,
                "hideMenu": false,
                "hideBreadcrumb": false,
                "enableUIMetadataCache": true
            },
            status: '0',
            msg: ''
        }
    }
}
export async function saveThemeSettings(data) {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization)
    return axios.post(`${proxy}/saveAppTheme`, data, { headers, responseType: 'json' });
}
export async function currentUser() {
    const { appContext } = store.getState();
    // const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);
    const headers = Object.assign({}, Authorization);

    if (!isDevelopment) {
        return axios.get(`${proxy}/currentUser`, { headers, responseType: 'json' });
    }
    
    return {
        data: { "name": "Serati Ma", "avatar": "https://gw.alipayobjects.com/zos/rmsportal/BiazfanxmamNRoxxVxka.png", "userId": "00000001", "notifyCount": 3, "appId": 12 }
    }
    
}

export async function getVariables() {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);

    if (!isDevelopment) {
        return axios.post(`${proxy}/survey/queryVariables`, {}, { headers, responseType: 'json' });
    }
    const responseData = {
        "msg": "",
        "data": {
            "data": [
                {
                    "id": "5",
                    "name": "currentTime",
                    "chineseName": "系统当前时间",
                    "description": "系统当前时间",
                    "group": {
                        "id": "survey-variable-group-111111",
                        "name": "system",
                        "chineseName": "系统变量",
                        "description": "系统变量"
                    }
                },
                {
                    "id": "6",
                    "name": "createTime",
                    "chineseName": "问卷生成时间",
                    "description": "问卷生成时间",
                    "group": {
                        "id": "survey-variable-group-111111",
                        "name": "system",
                        "chineseName": "系统变量",
                        "description": "系统变量"
                    }
                },
                {
                    "id": "4",
                    "name": "policyHolderName",
                    "chineseName": "投保姓名",
                    "description": "投保姓名",
                    "group": {
                        "id": "survey-variable-group-22222",
                        "name": "firstVisit",
                        "chineseName": "首访问卷",
                        "description": "首访问卷"
                    }
                },
                {
                    "id": "3",
                    "name": "housekeeperNo",
                    "chineseName": "管家工号",
                    "description": "管家工号",
                    "group": {
                        "id": "survey-variable-group-22222",
                        "name": "firstVisit",
                        "chineseName": "首访问卷",
                        "description": "首访问卷"
                    }
                },
                {
                    "id": "2",
                    "name": "customerSex",
                    "chineseName": "客户性别",
                    "description": "客户性别",
                    "group": {
                        "id": "survey-variable-group-22222",
                        "name": "firstVisit",
                        "chineseName": "首访问卷",
                        "description": "首访问卷"
                    }
                },
                {
                    "id": "1",
                    "name": "customerName",
                    "chineseName": "客户姓名",
                    "description": "客户姓名",
                    "group": {
                        "id": "survey-variable-group-22222",
                        "name": "firstVisit",
                        "chineseName": "首访问卷",
                        "description": "首访问卷"
                    }
                }
            ],
            "pagination": null,
            "authority": [],
            "summary": null
        },
        "status": "0",
        "error": ""
    };
    const resPromise = new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve({data: responseData}); // 模拟异步
        }, 2*1000);
    });
    
    return resPromise
    
}

export async function deleteTemplates(data) {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);
    return axios.post(`${proxy}/deleteTemplates`, data, { headers, responseType: 'json' });
    // return {
    //     data: {
    //         "status": "0",
    //         "msg": "success"
    //     }
    // }
}

export async function saveTemplate(data) {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);
    return axios.post(`${proxy}/saveTemplate`, data, { headers, responseType: 'json' });
    // return {
    //     data: {
    //         "status": "0",
    //         "msg": "success",
    //         "data": {
    //             "templateId": data.templateId || 'newTemplateId001'
    //         }
    //     }
    // }
}

export async function createWindowsByTemplate(data) {
    const { appContext } = store.getState();
    const headers = Object.assign({}, { appContext: JSON.stringify(appContext) }, Authorization);
    return axios.post(`${proxy}/useTemplateCreateWindows`, data, { headers, responseType: 'json' });
    // return {
    //     data: {
    //         "status": "0",
    //         "msg": "success",
    //         "data": ["newWindowId001", "newWindowId002"]
    //     }
    // }
}