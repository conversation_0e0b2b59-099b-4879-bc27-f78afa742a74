/**
 * Copyright 2018-2024 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
hr,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  margin: 0;
  padding: 0;
}
ul,
ol {
  list-style: none;
}


body,html{
  font-family: sans-serif;
  position: relative;
  width: 100%;
  height: 100%;
}
#root{
  min-width: 1024px;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.rjsf fieldset {
  padding: 1px 1px 1px !important;
}
/* .rjsf .row {
  margin-top: -35px !important;
} */

/* .rjsf{
  margin-bottom: 45px;
} */
.content_sider .rjsf button[type='submit']{
  display: none;
}
 
#rjsf_open-care_window__title,.rjsf_open-care_window_props_events__title{
  display: none;
  height: 0px;
}

.control-label{
  font-size: 14px;
  font-weight: initial;
}
legend{
  font-size: initial;
}
.content_sider{
    position: absolute;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    padding-bottom: 48px;
}

.editor_button{
  position: fixed;
    bottom: 40px;
    width: 300px;
    text-align: center;
    /* background-color: #ffffff; */
}

.field_label:hover,.field_label:focus{
  color:#800080;
  text-decoration:underline;
  font-size: medium
}
.field_label:active{
  color:#800080;
  text-decoration:underline;
  font-size: medium
}
/* overwrite */
.field_wrapper .ant-form-item {
  margin-bottom: 0;
}

.field_wrapper {
  overflow: auto;
  /* margin-bottom: 24px; */
  border: 1px dashed #ccc;
  padding: 5px;
  position: relative;
}

.titleAndToolBox {

}
/* .field_wrapper:hover {
  box-shadow: 0 0 10px #ccc;
} */
.edit__btn {
  display: inline-block;
  margin-right: 20px;
  cursor: pointer;
}


.field_wrapper > .am-flexbox-item,
.field_wrapper > .am-flexbox,
.field_wrapper > .ant-layout,
.field_wrapper > .ant-form,
.field_wrapper > .ant-row,
.field_wrapper > .am-tabs {
  border: 0px dashed #ccc;
  min-height: 60px;
}


.field_wrapper > .ant-col-24,
.field_wrapper > .ant-col-23,
.field_wrapper > .ant-col-22,
.field_wrapper > .ant-col-21,
.field_wrapper > .ant-col-20,
.field_wrapper > .ant-col-19,
.field_wrapper > .ant-col-18,
.field_wrapper > .ant-col-17,
.field_wrapper > .ant-col-16,
.field_wrapper > .ant-col-15,
.field_wrapper > .ant-col-14,
.field_wrapper > .ant-col-13,
.field_wrapper > .ant-col-12,
.field_wrapper > .ant-col-11,
.field_wrapper > .ant-col-10,
.field_wrapper > .ant-col-9,
.field_wrapper > .ant-col-8,
.field_wrapper > .ant-col-7,
.field_wrapper > .ant-col-6,
.field_wrapper > .ant-col-5,
.field_wrapper > .ant-col-4,
.field_wrapper > .ant-col-3,
.field_wrapper > .ant-col-2,
.field_wrapper > .ant-col-1
 {
  border: 0px dashed #ccc;
  min-height: 60px;
}

.isQuestionnaireApp .field_wrapper > .ant-col-24
 {
  min-height: auto;
}

.field_wrapper > .ant-card {
  min-height: 60px;
}


/* TODO: 由于 Picker 相关组件预览时，自动弹出选项并且无法关闭，此处暂时隐藏选项 */
.am-picker-popup-mask,
.am-picker-popup-wrap  {
  display: none;
}


.am-navbar-title > .field_wrapper{
  max-height: 40px;
}

.oc-time-range-picker-hide-icon .ant-time-picker-icon {
  display: none;
}

/* button box align */
.open-care-button-box>.questionnaireBodyNoChildren{
  display: inline-block;
}
.open-care-button-box.open-care-button-align-left {
  text-align: left;
}
.open-care-button-box.open-care-button-align-center {
  text-align: center;
}
.open-care-button-box.open-care-button-align-right {
  text-align: right;
}

/* 文本编辑器 css */
#blah2 .ace_scroller .ace_print-margin {
  left: auto !important;
  right: 0;
}

.nav-button {
  background-color: #E1E1E1!important;
}

.show-nav-button {
  width: 18px;
  height: 38px;
  line-height: 38px;
  color: #fff;
  background-color: #001529;
  box-shadow: 0 4px 4px 0 rgba(0,0,0,.2);
  border-radius: 0 4px 4px 0;
  display: inline-block;
  position: relative;
  left: -50px;
  top: 0;
  cursor: pointer;
}

ul.CodeMirror-hints {
  z-index: 999999 !important;
}

.componentToolBox {
  float: right;
  padding-right: 5px;
}

.noTitle .componentTitleBox {
  display: none;
}

.noTitle .showAllInCurrentComponent>.titleAndToolBox>.componentTitleBox {
  display: inline-block;
}

.noTitle.noTools.noBorderLine .componentTitleBox {
  display: none;
}

.noTools .componentToolBox {
  display: none !important;
}

.noTitle.noTools.noBorderLine .mark .componentToolBox {
  display: none !important;
}

.noTools .mark>.titleAndToolBox>.componentToolBox {
  display: inline-block !important;
}

.noTools .showAllInCurrentComponent>.titleAndToolBox>.componentToolBox {
  display: inline-block !important;
}

.noTitle.noTools.noBorderLine .showAllInCurrentComponent>.titleAndToolBox>.componentToolBox,
.noTitle.noTools.noBorderLine .mark>.titleAndToolBox>.componentToolBox,
.noTitle.noTools.noBorderLine .mark>.titleAndToolBox>.componentTitleBox {
  display: none;
}

.noBorderLine .componentContentBox:not(.ant-card) {
  border: 0px dashed #ccc;
}

.noTitle .field_wrapper_item:hover .componentTitleBox {
  display: inline-block;
}

.componentContentBox.mark {
  border: 1px solid #1890ff;
  border-radius: 4px;
}

.noBorderLine .field_wrapper > .am-flexbox-item,
.noBorderLine .field_wrapper > .am-flexbox,
.noBorderLine .field_wrapper > .ant-layout,
.noBorderLine .field_wrapper > .ant-form,
.noBorderLine .field_wrapper > .ant-row,
.noBorderLine .field_wrapper > .am-tabs {
  border: 0px dashed #ccc;
  min-height:auto;
}

.componentTitleBox {
 line-height: 26px; 
}
.showAllInCurrentComponent {
  border: 1px solid #1890ff;
  border-radius: 4px;
}

.noTitle.noTools.noBorderLine .mark>.titleAndToolBox>.componentToolBox,
.noTitle.noTools.noBorderLine .mark>.titleAndToolBox>.componentTitleBox,
.noTitle.noTools.noBorderLine .showAllInCurrentComponent>.titleAndToolBox>.componentToolBox,
.noTitle.noTools.noBorderLine .showAllInCurrentComponent>.titleAndToolBox>.componentTitleBox {
  display: none !important;
}
.mark>.titleAndToolBox>.componentToolBox,
.mark>.titleAndToolBox>.componentTitleBox{
  display: inline-block;
}

.componentToolBox>i.anticon{
  padding: 5px;
  font-size: 16px;
}
.componentToolBox>.ant-badge {
  margin-right: 5px;
}
.componentToolBox>.ant-badge>i.anticon {
  font-size: 16px;
}
.componentToolBox>i.anticon:hover,
.componentToolBox>.hasDot.ant-badge>i.anticon,
.componentToolBox>.ant-badge>i.anticon:hover {
  color: #2672ff;
}


.ant-radio-group-oc-vertical>.ant-radio-wrapper{
  display: block;
  line-height: 40px;
  min-height: 0px;
}

.ant-radio-group-oc-trigger-menu .ant-radio-wrapper:not(.ant-radio-wrapper-checked) .ant-radio:not(:hover) .ant-radio-inner{
  border-color: #d9d9d9;
}

.ant-checkbox-group-oc-vertical>.ant-checkbox-wrapper{
  display: block;
  min-height: 40px;
  line-height: 40px;
}

.ant-checkbox-group-oc-vertical .ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0;
}

.question_block .field_wrapper:not(.mark):not(.showAllInCurrentComponent) {
  border: 1px solid #fff;
}

.isQuestionnaireApp .componentContentBox:not(.mark):not(.ant-col-24) {
  padding: 5px !important;
  border: 1px solid #fff;
}
.isQuestionnaireApp .componentContentBox.showAllInCurrentComponent:not(.mark):not(.ant-col-24) {
  border: 1px solid #2672ff;
}

.isQuestionnaireApp .componentToolBox {
  position: absolute;
  right: 5px;
  top: 5px;
  z-index: 2;
  display: none;
}
.isQuestionnaireApp .mark .componentToolBox,
.isQuestionnaireApp .showAllInCurrentComponent .componentToolBox {
  display: inline-block;
}
#fields_container .field_wrapper.ant-col-24 {
  background: #f5f5f9!important;
  padding: 0px !important;
}

.question_block+.question_block{
  /* border-top: 0px; */
  margin-top: 10px;
}
.isQuestionnaireApp .question_block:hover>.titleAndToolBox>.componentToolBox {
  display: inline-block !important;
}

.isQuestionnaireApp .field_wrapper.ant-col-24 {
  border: 0px;
}
.isQuestionnaireApp .ant-form-vertical .ant-form-extra{
  padding: 0; margin: 0;
  margin-bottom: 5px;
}


/* 处理拖拽时 hover 的效果 */
.isQuestionnaireApp .field_wrapper {
  overflow: unset;
}
/* .isQuestionnaireApp .field_wrapper.isOver.isOverAffter {
  margin-bottom: 14px;
}
.isQuestionnaireApp .field_wrapper.isOver.isOverBefore {
  margin-top: 14px;
} */
.isQuestionnaireApp .field_wrapper.isOver.isOverAffter::after {
  display: inline-block;
  content: '';
  background: #2672ff;
  left: 0;
  right: 0;
  height: 10px;
  position: absolute;
  z-index: 99;
  bottom: -10px;
}

.isQuestionnaireApp .field_wrapper.isOver.isOverBefore::before {
  display: inline-block;
  content: '';
  background: #2672ff;
  left: 0;
  right: 0;
  height: 10px;
  position: absolute;
  z-index: 99;
  top: -10px;
}

.isQuestionnaireApp .isOverQuestionnaireBody {
  overflow: unset;
  position: relative;
}
.isQuestionnaireApp .isOverQuestionnaireBody:not(.questionnaireBodyHasChildren)::before {
  display: inline-block;
  content: '';
  background: #2672ff;
  left: 15px;
  right: 15px;
  height: 8px;
  position: absolute;
  z-index: 99;
  top: 11px;
}
.isQuestionnaireApp .componentContentBox>.ant-form-item-label>label {
  margin-right: 120px;
}
.ant-modal-title {
  margin-right: 20px !important;
}
.isQuestionnaireApp .ant-form-item-label label p {
  margin-bottom: auto;
}
.form-item-label-seqNo {
  position: absolute;
  display: inline-block;
  width: 32px;
  overflow: hidden;
}
.form-item-label-seqNo+.form-item-label-html {
  margin-left: 32px;
  display: inline-block;
}

.isQuestionnaireApp .QMultipleFillQuestion .customerLayout-horizontal .ant-form-item-label {
  text-align: right !important;
  padding-right: 10px;
  line-height: 30px;
}

.isQuestionnaireApp .QMultipleFillQuestion .customerLayout-horizontal .ant-form-item-label>label::after{
  content: ":";
  color: #000;
  position: relative;
  top: -0.5px;
  margin: 0 8px 0 2px;
}

.isQuestionnaireApp .QMultipleFillQuestion .QMultipleFillQuestion-sex-form-item-horizontal>.ant-form-item-control-wrapper {
  padding-top: 3px;
}

.ant-checkbox-group-oc-vertical>.ant-checkbox-wrapper{
  display: block;
  min-height: 40px;
  line-height: 40px;
}

.ant-checkbox-group-oc-vertical .ant-checkbox-wrapper + .ant-checkbox-wrapper {
  margin-left: 0;
}

/* For demo */
.ant-carousel .slick-slide {
  text-align: center;
  /* height: 100%;
  line-height: 160px; */
  background: #364d79;
  overflow: hidden;
}

.ant-carousel .slick-slide h3 {
  color: #fff;
}

/* 流程管理页面 */
.screen-xl #Col8165_fd4e1cd3-57c9-4b0c-9a57-c0501c80ca01{
  padding-left: 24px;padding-right: 24px;padding-top: 16px;padding-bottom: 16px;
}
#Col8167_de50e2b3-4e42-4af3-83f8-4e844ebf37a7 .ant-table-footer{
  width: 100%; /* 避免出现无用滚动条，原来是固定宽度 */
}

/* 引用页面出现大量空白（数据模型）*/
.ant-layout-content{
  height: 100%;
}
.ant-layout-content>div{
  height: 100%;
}
.ant-layout-content>div>iframe{   /* 原来是固定高度 */
  height: 100%;
}

#loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  font-size: 20px;
}

.ant-switch{
  width: auto !important;
}
/* UI DESIGNER DRAG */
/* .isOver.isOverAffter::after, */
/* .isOver:not(.isOverAffter,.isOverBefore) */
.isUiDesignerApp .field_wrapper.canShowDropArea.isOver.isOverAffter,
.isUiDesignerApp .field_wrapper.canShowDropArea.isOver:not(.isOverAffter,.isOverBefore)
  >div:nth-child(2)>.field_wrapper.componentContentBox:last-child{
  box-sizing: border-box;
  border-right: 0;
}
.isUiDesignerApp .field_wrapper.canShowDropArea.isOver.isOverBefore{
  box-sizing: border-box;
  border-left: 0;
}
.isUiDesignerApp .field_wrapper.canShowDropArea.isOver.isOverAffter::after,
.isUiDesignerApp .field_wrapper.canShowDropArea.isOver:not(.isOverAffter,.isOverBefore)
  >div:nth-child(2)>.field_wrapper.canShowDropArea.componentContentBox:last-child::after {
  display: inline-block;
  content: '';
  background: rgba(38, 114, 255, 1);
  top: 0;
  bottom: 0;
  width: 3px;
  position: absolute;
  z-index: 99;
  right: 0px;
  border-left: 1px dashed #FFF;
  border-top: 1px dashed rgba(38, 114, 255, 1);
  border-bottom: 1px dashed rgba(38, 114, 255, 1);
  box-sizing: border-box;
}

/* .isOver.isOverBefore */
.isUiDesignerApp .field_wrapper.canShowDropArea.isOver.isOverBefore::before {
  display: inline-block;
  content: '';
  background: rgba(38, 114, 255, 1);
  top: 0;
  bottom: 0;
  width: 3px;
  position: absolute;
  z-index: 99;
  left: 0px;
  border-right: 1px dashed #FFF;
  border-top: 1px dashed rgba(38, 114, 255, 1);
  border-bottom: 1px dashed rgba(38, 114, 255, 1);
  box-sizing: border-box;
}


/* table columns */
.form-group.field.field-array > label[for="rjsf_open-care_window_columns"] {
  font-size: 1.5em;
  border-bottom: 1px solid #e5e5e5;
  width: 100%;
}


/* 菜单列表 */
.menu-list-custom-styles {

  & > .panel-danger.errors {
    display: none !important;
  }

  & > .form-group.field.field-object+div {
    display: none !important;
  }
}



.menu-list-tree-container {
  height: calc(100vh - 75px - 75px - 66px);
  overflow-x: hidden;

  .ant-tree li ul{
    padding: 0 0 0 24px;
  }

  .ant-tree.ant-tree-show-line li span.ant-tree-switcher{
    background-color: transparent;
    margin-top: 6px;
  }

  .menu-list-tree {

    .menu-list-tree-icon-button-box {
      position: absolute;
      margin-top: -2px;
      right: 6px;
    }

    .menu-list-tree-icon-button {
      margin-left: 10px;
      height: 20px;
      line-height: 20px;
      border: 0;
    }

    .ant-tree-child-tree > li:first-child,
    .ant-tree-child-tree > li:last-child {
      padding-top: 0;
      padding-bottom: 0;
    }

    span.ant-tree-node-content-wrapper{
      width: calc(100% - 24px);
    }

    & > li[role="treeitem"] {
      & > span.ant-tree-switcher {
        display: none;
      }
  
      & > span.ant-tree-node-content-wrapper {
        display: none;
      }
  
      & > ul.ant-tree-child-tree {
        padding-left: 0 !important;
      }
    }

    .ant-tree-treenode-switcher-open{

      line-height: 24px;
      padding: 5px 0 7px 0;
      margin: 3px 0;

      > .ant-tree-switcher_open {
        margin-top: 6px;
      }

      > .ant-tree-node-content-wrapper {
        width: calc(100% - 24px);
        height: 36px;
        padding-top: 6px;
        border: 1px solid transparent;

        .menu-list-tree-icon-button-box{
          margin-top: -1px;
        }

        &:hover {
          background-color: #bae7ff;
          border: 1px solid #1890ff;
          box-shadow: 0 1px 2px rgba(24, 144, 255, 0.4);
          box-sizing: border-box;
          border-radius: 6px;

          > .ant-tree-title .menu-list-tree-icon-button-box{
            display: inline-block;
          }
        }
      }

      &.ant-tree-treenode-selected {
        > .ant-tree-node-content-wrapper  {
          background-color: #bae7ff;
          border: 1px solid #1890ff;
          box-shadow: 0 1px 2px rgba(24, 144, 255, 0.4);
          box-sizing: border-box;
          border-radius: 6px;
          cursor: pointer;
        }
      }
    }


    .ant-tree-treenode-switcher-close{

      padding: 0 0 0 0;
      border: 1px solid transparent;
      margin: 3px 0;

      .ant-tree-node-content-wrapper {
        height: 34px;
        padding: 5px 0 7px 0;
        border-radius: 6px;
      }

      &:hover,
      &.ant-tree-treenode-selected {
        background-color: #bae7ff;
        border: 1px solid #1890ff;
        box-shadow: 0 1px 2px rgba(24, 144, 255, 0.4);
        box-sizing: border-box;
        border-radius: 6px;
        cursor: pointer;
  
        .span.ant-tree-switcher {
          background-color: transparent;
        }

        > .ant-tree-node-content-wrapper {
          > .ant-tree-title .menu-list-tree-icon-button-box{
            display: inline-block;
          }
        }
      }
      .ant-tree-node-content-wrapper:hover {
        background-color: transparent;
      }
    }



    

    .ant-tree-node-content-wrapper {

      & > .ant-tree-title .menu-list-tree-icon-button-box{
        display: none;
      }

      &:hover,
      &.ant-tree-node-selected {
        

        & > .ant-tree-title .menu-list-tree-icon-button-box{
          display: inline-block;
        }
      }
    }
  }

}


#rjsf_open-care_menu {
  padding: 0.35em 0.75em 0.625em !important;
  

}

#rjsf_open-care_menu_formData {
  padding: 0.35em 0.75em 0.625em !important;

  .has-error {
    .form-control {
      border-color: red;
    }

    .checkbox,
    .checkbox-inline,
    .control-label,
    .help-block,
    .radio,
    .radio-inline,
    .checkbox label,
    .checkbox-inline label,
    .radio label,
    .radio-inline label,
    .text-danger {
      color: red;
    }
  }

  & > .form-group.field.field-boolean {
    & > .checkbox {
      margin-left: -13px;
    }
  }

  & > .form-group.field.field-string {

    & > .control-label {

      position: relative;

      .required {
        color: red;
        position: absolute;
        margin-left: 5px;
        margin-top: 2px;
      }
    }
  }
}
