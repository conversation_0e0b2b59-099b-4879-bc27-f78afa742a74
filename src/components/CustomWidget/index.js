/**
 * Copyright 2018-2024 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
import CustomSelect from './CustomSelect';
import CustomTableColumns from './CustomTableColumns';
import TagSelect from './TagSelect';
import CustomCSSEditor from './CustomCSSEditor';
import CustonCssModal from './CustomCssModal'
import CustomScriptsEditor from './CustomScriptsEditor';
import CustomCascader from './CustomCascader';
import CustomTreeSelect from './CustomTreeSelect';
import CustomTemplateForMListViewEditor from './CustomTemplateForMListViewEditor';
import CustomHtmlEditor from './CustomHtmlEditor';
import CustomStyleEditor from './CustomStyleEditor';
import CustomSelectIcon from './CustomSelectIcon';
import CustomRichEditor from './CustomRichEditor';
import CustomRichEditorForSurvey from './CustomRichEditorForSurvey';

export const CustomWidget = {
    CustomSelect,
    CustomTableColumns,
    TagSelect,
    CustomCSSEditor,
    CustonCssModal,
    CustomScriptsEditor,
    CustomCascader,
    CustomTreeSelect,
    CustomTemplateForMListViewEditor,
    CustomHtmlEditor,
    CustomRichEditor,
    CustomStyleEditor,
    CustomRichEditorForSurvey,
    CustomSelectIcon
};
