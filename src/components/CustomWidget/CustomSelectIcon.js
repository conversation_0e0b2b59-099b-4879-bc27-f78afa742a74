/**
 * Copyright 2018-2020 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
import React, { Fragment } from "react";
import { Modal, Input, Row, Col, Button, Icon, Tabs } from "antd";

const { TabPane } = Tabs;

// 按照 Ant Design 官方分组的图标
const ICON_GROUPS = {
  directional: {
    title: '方向性图标',
    icons: [
      'step-backward', 'step-forward', 'fast-backward', 'fast-forward', 'shrink', 'arrows-alt',
      'down', 'up', 'left', 'right', 'caret-up', 'caret-down', 'caret-left', 'caret-right',
      'up-circle', 'down-circle', 'left-circle', 'right-circle', 'double-right', 'double-left',
      'vertical-left', 'vertical-right', 'vertical-align-top', 'vertical-align-middle', 'vertical-align-bottom',
      'forward', 'backward', 'rollback', 'enter', 'retweet', 'swap', 'swap-left', 'swap-right',
      'arrow-up', 'arrow-down', 'arrow-left', 'arrow-right', 'play-circle', 'up-square', 'down-square',
      'left-square', 'right-square', 'login', 'logout', 'menu-fold', 'menu-unfold', 'border-bottom',
      'border-horizontal', 'border-inner', 'border-outer', 'border-left', 'border-right', 'border-top',
      'border-verticle', 'pic-center', 'pic-left', 'pic-right', 'radius-bottomleft', 'radius-bottomright',
      'radius-upleft', 'radius-upright', 'fullscreen', 'fullscreen-exit'
    ]
  },
  suggested: {
    title: '提示建议性图标',
    icons: [
      'question', 'question-circle', 'plus', 'plus-circle', 'pause', 'pause-circle', 'minus',
      'minus-circle', 'plus-square', 'minus-square', 'info', 'info-circle', 'exclamation',
      'exclamation-circle', 'close', 'close-circle', 'close-square', 'check', 'check-circle',
      'check-square', 'clock-circle', 'warning', 'issues-close', 'stop'
    ]
  },
  editor: {
    title: '编辑类图标',
    icons: [
      'edit', 'form', 'copy', 'scissor', 'delete', 'snippets', 'diff', 'highlight', 'align-center',
      'align-left', 'align-right', 'bg-colors', 'bold', 'italic', 'underline', 'strikethrough',
      'redo', 'undo', 'zoom-in', 'zoom-out', 'font-colors', 'font-size', 'line-height', 'dash',
      'small-dash', 'sort-ascending', 'sort-descending', 'drag', 'ordered-list', 'unordered-list',
      'radius-setting', 'column-width', 'column-height'
    ]
  },
  data: {
    title: '数据类图标',
    icons: [
      'area-chart', 'pie-chart', 'bar-chart', 'dot-chart', 'line-chart', 'radar-chart', 'heat-map',
      'fall', 'rise', 'stock', 'box-plot', 'fund', 'sliders'
    ]
  },
  brand: {
    title: '品牌和标识',
    icons: [
      'android', 'apple', 'windows', 'ie', 'chrome', 'github', 'aliwangwang', 'dingding',
      'weibo-square', 'weibo-circle', 'taobao-circle', 'html5', 'weibo', 'twitter', 'wechat',
      'youtube', 'alipay-circle', 'taobao', 'skype', 'qq', 'medium-workmark', 'gitlab', 'medium',
      'linkedin', 'google-plus', 'dropbox', 'facebook', 'codepen', 'code-sandbox', 'amazon',
      'google', 'codepen-circle', 'alipay', 'ant-design', 'ant-cloud', 'aliyun', 'zhihu', 'slack',
      'slack-square', 'behance', 'behance-square', 'dribbble', 'dribbble-square', 'instagram',
      'yuque', 'alibaba', 'yahoo', 'reddit', 'sketch'
    ]
  },
  common: {
    title: '网站通用图标',
    icons: [
      'account-book', 'alert', 'api', 'appstore', 'audio', 'bank', 'bell', 'book', 'bug', 'bulb',
      'calculator', 'build', 'calendar', 'camera', 'car', 'carry-out', 'cloud', 'code', 'compass',
      'contacts', 'container', 'control', 'credit-card', 'crown', 'customer-service', 'dashboard',
      'database', 'dislike', 'environment', 'experiment', 'eye-invisible', 'eye', 'file-add',
      'file-excel', 'file-exclamation', 'file-image', 'file-markdown', 'file-pdf', 'file-ppt',
      'file-text', 'file-unknown', 'file-word', 'file-zip', 'file', 'filter', 'fire', 'flag',
      'folder-add', 'folder', 'folder-open', 'frown', 'funnel-plot', 'gift', 'hdd', 'heart',
      'home', 'hourglass', 'idcard', 'insurance', 'interaction', 'layout', 'like', 'lock', 'mail',
      'medicine-box', 'meh', 'message', 'mobile', 'money-collect', 'pay-circle', 'notification',
      'phone', 'picture', 'play-square', 'printer', 'profile', 'project', 'pushpin', 'property-safety',
      'read', 'reconciliation', 'red-envelope', 'rest', 'safety-certificate', 'save', 'schedule',
      'security-scan', 'select', 'setting', 'shop', 'shopping', 'skin', 'smile', 'solution',
      'sound', 'star', 'switcher', 'tablet', 'tag', 'tags', 'team', 'thunderbolt', 'to-top',
      'tool', 'trademark', 'transaction', 'trophy', 'unlock', 'upload', 'usb', 'user', 'video-camera',
      'wallet', 'wifi', 'branches'
    ]
  }
};

// 创建所有图标的扁平化列表用于搜索
const ALL_ICONS = Object.values(ICON_GROUPS).reduce((acc, group) => {
  return acc.concat(group.icons);
}, []);

// 检查是否为第三方图标 URL
const isCustomIcon = (iconValue) => {
  if (!iconValue) return false;
  return iconValue.startsWith('http://') || iconValue.startsWith('https://') || iconValue.startsWith('data:');
};

export default class CustomSelectIcon extends React.PureComponent {
  state = {
    showModal: false,
    value: '',
    searchValue: '',
    filteredIcons: ALL_ICONS,
    activeTab: 'directional',
    customIconUrl: ''
  };
  aceEditor;
  constructor(props) {
    super(props);
  }

  componentDidMount() {
    this.setState({
      value: this.props.value,
      filteredIcons: ALL_ICONS
    })
  }

  showModal = () => {
    this.setState({
      showModal: true,
      value: this.props.value,
      searchValue: '',
      filteredIcons: ALL_ICONS,
      activeTab: 'directional'
    })
  }
  
  handleOk = (e) => {
    this.setState({
      showModal: false,
    });
    this.props.onChange(this.state.value);
  }

  handleCancel = (e) => {
    this.setState({
      showModal: false,
      value: this.props.value,
      searchValue: '',
      filteredIcons: ALL_ICONS,
      activeTab: 'directional'
    });
  }

  // 搜索图标
  handleSearch = (e) => {
    const searchValue = e.target.value;
    const filteredIcons = searchValue ? 
      ALL_ICONS.filter(icon => icon.toLowerCase().includes(searchValue.toLowerCase())) :
      ALL_ICONS;
    
    this.setState({
      searchValue,
      filteredIcons
    });
  }

  // 选择图标
  handleIconSelect = (iconType) => {
    this.setState({
      value: iconType
    });
  }

  // 切换标签页
  handleTabChange = (activeTab) => {
    this.setState({
      activeTab,
      searchValue: '',
      filteredIcons: ALL_ICONS
    });
  }

  // 处理自定义图标 URL 输入
  handleCustomIconUrlChange = (e) => {
    this.setState({
      customIconUrl: e.target.value
    });
  }

  // 选择自定义图标
  handleCustomIconSelect = () => {
    const { customIconUrl } = this.state;
    if (customIconUrl.trim()) {
      this.setState({
        value: customIconUrl.trim()
      });
    }
  }

  // 渲染图标网格
  renderIconGrid = (icons) => {
    const { value } = this.state;
    
    return (
      <div style={{ width: '100%', overflowX: 'hidden' }}>
        <Row gutter={[8, 8]} style={{ padding: '8px 0', margin: 0 }}>
          {icons.map(iconType => (
            <Col span={3} key={iconType}>
              <div
                style={{
                  width: '100%',
                  height: 80,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  borderRadius: 6,
                  backgroundColor: value === iconType ? '#e6f7ff' : 'transparent',
                  border: value === iconType ? '1px solid #1890ff' : '1px solid transparent',
                  transition: 'all 0.3s',
                  padding: '8px 4px'
                }}
                onClick={() => this.handleIconSelect(iconType)}
                onMouseEnter={(e) => {
                  if (value !== iconType) {
                    e.currentTarget.style.backgroundColor = '#1890ff';
                    e.currentTarget.style.borderColor = '#1890ff';
                    // 图标反色并放大
                    const icon = e.currentTarget.querySelector('.anticon');
                    const text = e.currentTarget.querySelector('.icon-text');
                    if (icon) {
                      icon.style.color = '#fff';
                      icon.style.transform = 'scale(1.4)';
                    }
                    if (text) text.style.color = '#fff';
                  }
                }}
                onMouseLeave={(e) => {
                  if (value !== iconType) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.borderColor = 'transparent';
                    // 恢复图标原始状态
                    const icon = e.currentTarget.querySelector('.anticon');
                    const text = e.currentTarget.querySelector('.icon-text');
                    if (icon) {
                      icon.style.color = '#666';
                      icon.style.transform = 'scale(1)';
                    }
                    if (text) text.style.color = '#666';
                  }
                }}
              >
                <div style={{ 
                  textAlign: 'center', 
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%'
                }}>
                  <Icon 
                    type={iconType} 
                    style={{ 
                      fontSize: 36, 
                      color: value === iconType ? '#1890ff' : '#666',
                      marginBottom: 6,
                      transition: 'all 0.3s',
                      transform: 'scale(1)'
                    }} 
                  />
                  <div 
                    className="icon-text"
                    style={{ 
                      fontSize: 12, 
                      color: value === iconType ? '#1890ff' : '#666',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      lineHeight: 1.2,
                      maxWidth: '100%',
                      transition: 'color 0.3s'
                    }}
                  >
                    {iconType}
                  </div>
                </div>
              </div>
            </Col>
          ))}
        </Row>
      </div>
    );
  }

  renderIconList = () => {
    const { filteredIcons, searchValue, activeTab, customIconUrl } = this.state;
    
    return (
      <div style={{ width: '100%', overflowX: 'hidden' }}>
        <div style={{ marginBottom: 16 }}>
          <Input.Search
            placeholder="搜索图标名称"
            value={searchValue}
            onChange={this.handleSearch}
            style={{ width: '100%' }}
            allowClear
          />
        </div>
        
        {searchValue ? (
          // 搜索模式：显示过滤后的所有图标
          <div style={{ 
            maxHeight: 450, 
            overflowY: 'auto', 
            overflowX: 'hidden',
            paddingRight: 8
          }}>
            {filteredIcons.length > 0 ? (
              this.renderIconGrid(filteredIcons)
            ) : (
              <div style={{ 
                textAlign: 'center', 
                color: '#999', 
                padding: 40,
                fontSize: 14 
              }}>
                没有找到匹配的图标
              </div>
            )}
          </div>
        ) : (
          // 分组模式：使用Tabs显示图标分组
          <Tabs activeKey={activeTab} onChange={this.handleTabChange}>
            {Object.entries(ICON_GROUPS).map(([key, group]) => (
              <TabPane tab={group.title} key={key}>
                <div style={{ 
                  maxHeight: 400, 
                  overflowY: 'auto',
                  overflowX: 'hidden',
                  paddingRight: 8
                }}>
                  {this.renderIconGrid(group.icons)}
                </div>
              </TabPane>
            ))}
            <TabPane tab="自定义图标" key="custom">
              <div style={{ 
                maxHeight: 400, 
                overflowY: 'auto',
                overflowX: 'hidden',
                paddingRight: 8,
                padding: '20px 0'
              }}>
                <Row gutter={[0, 16]}>
                  <Col span={24}>
                    <div style={{ marginBottom: 8, fontSize: 14, color: 'rgba(0, 0, 0, 0.85)' }}>
                      图标 URL:
                    </div>
                    <Input
                      placeholder="请输入第三方图标的 URL 地址"
                      value={customIconUrl}
                      onChange={this.handleCustomIconUrlChange}
                      style={{ width: '100%' }}
                    />
                  </Col>
                  <Col span={24}>
                    <Button 
                      type="primary" 
                      onClick={this.handleCustomIconSelect}
                      disabled={!customIconUrl.trim()}
                      style={{ marginTop: 8 }}
                    >
                      选择此图标
                    </Button>
                    {customIconUrl.trim() && (
                      <span style={{ 
                        marginLeft: 12, 
                        fontSize: 12,
                        color: this.state.value === customIconUrl.trim() ? '#52c41a' : '#999'
                      }}>
                        {this.state.value === customIconUrl.trim() ? '✓ 已选中此图标' : '点击按钮选择此图标'}
                      </span>
                    )}
                  </Col>
                  <Col span={24}>
                    <div style={{ 
                      marginTop: 16, 
                      padding: 16, 
                      border: '1px solid #d9d9d9',
                      borderRadius: 6,
                      backgroundColor: '#fafafa'
                    }}>
                      <div style={{ marginBottom: 8, fontSize: 14, color: 'rgba(0, 0, 0, 0.85)' }}>
                        预览:
                      </div>
                      {customIconUrl.trim() ? (
                        <img 
                          src={customIconUrl}
                          alt="自定义图标预览"
                          style={{ 
                            width: 36, 
                            height: 36,
                            objectFit: 'contain'
                          }}
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'flex';
                          }}
                        />
                      ) : null}
                      <div 
                        style={{ 
                          width: 36, 
                          height: 36,
                          display: customIconUrl.trim() ? 'none' : 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          border: '1px dashed #d9d9d9',
                          borderRadius: 4,
                          backgroundColor: '#fff',
                          color: '#999',
                          fontSize: 12
                        }}
                      >
                        <div style={{
                          display: 'flex',
                          flexDirection: 'column',
                          width: '100%',
                          height: '100%',
                          justifyContent: 'space-around'
                        }}>
                          <div style={{ 
                            display: 'flex', 
                            justifyContent: 'space-around', 
                            alignItems: 'center',
                            height: '50%',
                            fontSize: 10
                          }}>
                            <span style={{paddingLeft: 3}}>暂</span>
                            <span style={{paddingRight: 3}}>无 </span>
                          </div>
                          <div style={{ 
                            display: 'flex', 
                            justifyContent: 'space-around', 
                            alignItems: 'center',
                            height: '50%',
                            fontSize: 10
                          }}>
                            <span style={{paddingLeft: 3}}>图</span>
                            <span style={{paddingRight: 3}}>标</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Col>
                </Row>
              </div>
            </TabPane>
          </Tabs>
        )}
      </div>
    );
  }
  
  renderScriptModal() {
    return <Modal
      title="选择图标"
      visible={this.state.showModal}
      onOk={this.handleOk}
      onCancel={this.handleCancel}
      maskClosable={false}
      width={'80%'}
      okText="确定"
      cancelText="取消"
    >
      {this.renderIconList()}
    </Modal>
  }

  render() {
    const { value = ``} = this.props;
    const { showModal } = this.state;
    return (
    <Fragment>
      <Row>
        <Col span={12} style={{width: 'calc(100% - 95px)'}}>
          <Input
            placeholder="请输入图标名称或点击选择图标"
            value={value}
            onChange={(e) => {
              this.setState({
                value: e.target.value
              }, () => {
                this.props.onChange(this.state.value);
              })
            }}
          />
        </Col>
        <Col span={12} style={{width: 95, paddingLeft: 10, textAlign: 'right'}}>
          <Button type="primary" style={{ borderRadius: 4 }} onClick={this.showModal}>选择图标</Button>
        </Col>
        <Col span={24} style={{marginTop: 10}}>
          <span style={{
            fontSize: 14,
            color: 'rgba(0, 0, 0, 0.65)',
          }}>预览：</span>
          {value ? (
            isCustomIcon(value) ? (
              <img 
                src={value} 
                alt="自定义图标"
                style={{ 
                  marginLeft: 8, 
                  width: 16, 
                  height: 16,
                  objectFit: 'contain',
                  verticalAlign: 'middle'
                }}
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
              />
            ) : (
              <Icon type={value} style={{ marginLeft: 8, fontSize: 16 }} />
            )
          ) : (
            <span style={{ marginLeft: 8, color: '#ccc', fontSize: 12 }}>请选择图标</span>
          )}
        </Col>
      </Row>

      {
        showModal && this.renderScriptModal()
      } 
    </Fragment>
    )
  }
}