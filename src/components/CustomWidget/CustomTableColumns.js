/**
 * Copyright 2018-2020 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
import React, { useState, useRef, useEffect } from "react";
import { Input, List, Icon, Popconfirm } from "antd";
import { SortableContainer, SortableElement, SortableHandle } from "react-sortable-hoc";



export default function CustomTableColumns(props) {
  console.log('CustomTableColumns', props)
  const { value = [], onChange = (v) => { } } = props;
  const [focusedInput, setFocusedInput] = useState(null);
  const [cursorPositions, setCursorPositions] = useState({});

  // 添加样式来禁用文本选择
  const disableTextSelection = () => {
    document.body.style.userSelect = 'none';
    document.body.style.webkitUserSelect = 'none';
    document.body.style.mozUserSelect = 'none';
    document.body.style.msUserSelect = 'none';
  };

  // 恢复文本选择
  const enableTextSelection = () => {
    document.body.style.userSelect = '';
    document.body.style.webkitUserSelect = '';
    document.body.style.mozUserSelect = '';
    document.body.style.msUserSelect = '';
  };

  const onSortStart = () => {
    // 拖拽开始时禁用文本选择
    disableTextSelection();
  };

  const onSortEnd = ({ oldIndex, newIndex }) => {
    // 拖拽结束时恢复文本选择
    enableTextSelection();
    
    const newItems = [...value];
    const [removed] = newItems.splice(oldIndex, 1);
    newItems.splice(newIndex, 0, removed);
    onChange(newItems);
  };

  // 删除项目的处理函数
  const handleDelete = (componentId) => {
    const newData = value.filter(item => item.componentId !== componentId);
    onChange(newData);
  };

  // 拖拽图标
  const DragHandle = SortableHandle(() => (
    <Icon type="menu" style={{ cursor: "move", marginRight: 8 }} title="拖拽" />
  ));

  // 包装 List.Item 为可拖拽元素
  const SortableItem = SortableElement(({ item, onInputChange, index }) => {
    const inputRef = useRef(null);

    useEffect(() => {
      // 恢复光标位置
      if (inputRef.current && focusedInput === item.componentId) {
        const cursorPos = cursorPositions[item.componentId];
        if (typeof cursorPos === 'number') {
          // Ant Design Input 组件的原生 input 元素通过 input 属性访问
          const nativeInput = inputRef.current.input;
          if (nativeInput && nativeInput.setSelectionRange) {
            nativeInput.setSelectionRange(cursorPos, cursorPos);
          }
        }
      }
    });

    return (
      <div style={{ display: "flex", alignItems: "center", marginBottom: 2 }}>
        <DragHandle />
        <Input
          ref={inputRef}
          value={item.title}
          autoFocus={focusedInput === item.componentId}
          onFocus={() => setFocusedInput(item.componentId)}
          onChange={(e) => {
            const newTitle = e.target.value;
            const cursorPos = e.target.selectionStart;
            
            // 保存光标位置
            setCursorPositions(prev => ({
              ...prev,
              [item.componentId]: cursorPos
            }));

            const newData = value.map(vItem => {
              if (vItem.componentId === item.componentId) {
                return {
                  ...vItem,
                  title: newTitle
                };
              }
              return vItem;
            });

            onInputChange(newData);
          }}
          style={{ flex: 1 }}
        />
        <Popconfirm
          title={
            <span>
              确定要删除 <span style={{ color: '#ff4d4f', fontWeight: 'bold' }}>"{ item.title || item.dataIndex || '未命名' }"</span> 列吗？
            </span>
          }
          onConfirm={() => handleDelete(item.componentId)}
          okText="确认"
          cancelText="取消"
          placement="topRight"
        >
          <Icon 
            type="delete" 
            style={{ 
              cursor: "pointer", 
              color: "#ff4d4f",
              fontSize: "16px",
              padding: "4px"
            }} 
            title="删除" 
          />
        </Popconfirm>
      </div>
    );
  });

  // 包装 List 为可拖拽容器
  const SortableList = SortableContainer(({ items, onInputChange }) => {
    return (
      <List>
        {items.map((item, itemIndex) => (
          <SortableItem
            key={`${item.componentId}`}
            item={item}
            index={itemIndex}
            onInputChange={onInputChange}
          />
        ))}
      </List>
    );
  });

  return (
    <fieldset className="field field-array field-array-of-object">
      {/* <legend id={props.id}>{label}</legend> */}
      <SortableList 
        items={value} 
        onSortStart={onSortStart}
        onSortEnd={onSortEnd} 
        onInputChange={onChange} 
        useDragHandle 
      />
    </fieldset>
  );
}