/**
 * Copyright 2018-2024 Open-Care - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 *
 */
 
import React, { Component, Fragment } from 'react';
import { Tree, Button, Icon, Row, Col, Spin, Card, Modal } from 'antd';
import jp from 'jsonpath';
import { message } from 'antd';
import uuidv4 from 'uuid/v4';
import store from '../../store';
import * as t from '../../common/actions';
// import Form from "react-jsonschema-form";
import Form from '@rjsf/core';
import { saveNavigation, deleteNavigation } from "../../common/ajax";
import { CustomWidget } from '../CustomWidget';
import {generatorUniqueComponentNameInInstancesOrNavigationByLabel, updateStoreNavigationDataByNewMenuData} from '../../common/util'
import Search from 'antd/lib/input/Search';
import { cloneDeep, debounce, throttle } from 'lodash';


let schema = {
    "definitions": {
        "menu": {
            "type": "object",
            "properties": {
                "componentId": {
                    "type": "string",
                    "default": ""
                },
                "componentName": {
                    "type": "string",
                    "default": ""
                },
                "title": {
                    "type": "string",
                    "default": "",
                    "title": "名称",

                },
                "icon": {
                    "type": "string",
                    "default": "",
                    "title": "图标",

                },
                "selectedIcon": {
                    "type": "string",
                    "default": "",
                    "title": "选中后图标",

                },
                "appContextPath": {
                  "type": "string",
                  "default": "",
                  "title": "上下文路径"
                },
                "visible": {
                    "type": "boolean",
                    "default": false,
                    "title": "是否显示",

                },
                "isHomePage": {
                    "type": "boolean",
                    "default": false,
                    "title": "设为主页",

                },
                "type": {
                    "type": "string",
                    "enum": ["siderMenu", "parentMenu", "commonMenu", "metaMenu"],
                    "enumNames": ["根", "父级", "自定义页", "驱动页"],
                    "title": "类型",

                },
                
                
            },
            "required": ["componentName", "title", "type"],
            "dependencies": {
                "type": {
                    "oneOf": [
                        {
                            properties: {
                                type: {
                                    enum: ['metaMenu']
                                },
                                "windowId": {
                                    "type": "string",
                                    "default": "",
                                    "title": "绑定窗口"
                                },
                            },
                            required: ["windowId"]
                        },
                    ]
                }
            }
        }
    },
    "type": "object",
    "properties": {
        "formData": {
            "title": "",
            "$ref": "#/definitions/menu"
        }
    }
}

const defaultFormData = {
    componentName: "",
    title: "",
    icon: "",
    selectedIcon: "",
    appContextPath: "",
}
const uiSchema = {
    "formData": {
        "componentId": {
            "ui:widget": "hidden",
        },
        "componentName": {
            "classNames": "row"
        },
        "title": {
            "classNames": "row"
            // "ui:widget":"myCustomWidget"
        },
        "icon": {
            "classNames": "row",
            "ui:widget":"CustomSelectIcon"
        },
        "selectedIcon": {
            "classNames": "row",
            "ui:widget":"CustomSelectIcon"
        },
        "appContextPath": {
          "classNames": "row"
        },
        "type": {
            "classNames": "row"
            // "ui:widget":"myCustomWidget"
        },
        "windowId": {
            "classNames": "row",
            "ui:widget": "CustomSelect",
            "ui:options": {
                dataSource: [],
                labelKey: 'title',
                valueKey: 'windowId'
            }
        },
        "children": {
            "items": {
                "componentId": {
                    "ui:widget": "hidden"
                },
                "children": {
                    "items": {

                        "componentId": {
                            "ui:widget": "hidden"
                        },
                        "children": {
                            "items": {
                                "componentId": {
                                    "ui:widget": "hidden"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    
}


export default class MenuList extends Component {
    constructor(params) {
        super(params);
        const { navigation } = store.getState();
        this.state = {
            navigation,
            showSchema: false,
            formData: {},
            uiSchema: uiSchema,
            loading: false,
            expand: true,
            expandedKeys: [],
            foldKeys: [],
            searchValue: '',
            filteredData: [],
            expandAll: true,
        }

        this.formRef = React.createRef();
    }


    componentDidMount() {
        const { windowList } = store.getState();
        uiSchema.formData.windowId['ui:options'].dataSource = windowList;
        this.updateExpandKeys();
    }

    saveNavigation(saveData = {}) {
        this.updateExpandKeys();
        this.setState({loading: true});
        saveNavigation(saveData).then((p) => {
            this.setState({loading: false});
            const { data: { status, msg } = {} } = p;
            if (status != '0') {
                message.error(msg);
                return;
            }
            this.updateStoreNavigationDataByNewMenuData(saveData);
        }).catch((e) => {
            message.error(e.message);
        });
    }

    updateStoreNavigationDataByNewMenuData(newMenuData) {
        const { navigation } = store.getState();
        jp.apply(navigation, `$..[?(@.componentId==="${newMenuData.componentId}")]`, (value) => {
            return newMenuData;
        });
      
        store.dispatch({
            type: t.UPDATE_NAVIGATION,
            payload: {
              navigation
            }
        })
      }
    
    deleteNavigation(componentId = '') {
        this.updateExpandKeys();
        const {formData: {formData: {componentId: showSchemaComponentId} = {}} = {}} = this.state;
        deleteNavigation(componentId).then((p) => {
            const { data: { status, msg } = {} } = p;
            if (status != '0') {
                message.error(msg);
                return;
            }
            if (showSchemaComponentId && componentId === showSchemaComponentId) {
                this.setState({
                    showSchema: false,
                    formData: {}
                });
            }

        }).catch((e) => {
            message.error(e.message);
        });
    }
    handleSubmit = (newData) => {
        const { formData, formData: { formData: componentformData } = {} } = newData;
        const { componentId, isHomePage, type, windowId, componentName, title } = componentformData;

        // 先进行自定义校验
        const customErrors = [];
        
        // 基础必填项校验
        if (!componentName || componentName.trim() === '') {
            customErrors.push('组件名称是必填项');
        }
        
        if (!title || title.trim() === '') {
            customErrors.push('名称是必填项');
        }
        
        if (!type || type.trim() === '') {
            customErrors.push('类型是必填项');
        }

        // 当type为metaMenu时，windowId为必填项
        if (type === 'metaMenu' && (!windowId || typeof windowId !== 'string' || windowId.trim() === '')) {
            customErrors.push('绑定窗口是必填项');
            
            // 模拟触发 windowId 字段的变化以激活 Form 自身的校验
            if (customErrors.includes('绑定窗口是必填项')) {
                // 先给windowId设置一个有效值，触发Form重新渲染
                const dataSource = this.state.uiSchema.formData.windowId['ui:options'].dataSource;
                const firstWindow = dataSource && dataSource.length > 0 ? dataSource[0] : null;
                const tempWindowId = '                              '; // 使用较长的空字符串作为临时值
                
                const tempFormData = {
                    ...formData,
                    formData: {
                        ...componentformData,
                        windowId: tempWindowId
                    }
                };
                
                this.setState({
                    formData: tempFormData
                }, () => {
                    // 等待DOM更新后，模拟点击clear图标
                    setTimeout(() => {
                        try {
                            // 尝试多种选择器找到clear图标
                            let clearIcon = null;
                            const selectors = [
                                '.menu-list-custom-styles .ant-select-selection__clear',
                                '.menu-list-custom-styles .ant-select-clear-icon', 
                                '.ant-select-selection__clear',
                                '.ant-select-clear-icon',
                                '.anticon-close-circle.ant-select-clear-icon',
                                '[id*="windowId"] .ant-select-clear',
                                '#rjsf_open-care_menu_formData_windowId .ant-select-clear',
                                '.ant-select-clear[title="清除"]',
                                '.ant-select .ant-select-clear',
                                '.menu-list-custom-styles .ant-select-clear'
                            ];
                            
                            for (let selector of selectors) {
                                clearIcon = document.querySelector(selector);
                                if (clearIcon) {
                                    break;
                                }
                            }
                            
                            if (clearIcon) {
                                clearIcon.click();
                            } else {
                                // 方法2: 直接通过Form的onChange机制触发校验
                                const emptyFormData = {
                                    ...formData,
                                    formData: {
                                        ...componentformData,
                                        windowId: ''
                                    }
                                };
                                
                                // 使用setState触发Form重新渲染和校验
                                this.setState({
                                    formData: emptyFormData
                                }, () => {
                                    // 尝试手动调用Form的校验方法
                                    setTimeout(() => {
                                        if (this.formRef.current) {
                                            // 尝试不同的校验方法
                                            if (typeof this.formRef.current.validateForm === 'function') {
                                                this.formRef.current.validateForm();
                                            } else if (typeof this.formRef.current.validate === 'function') {
                                                this.formRef.current.validate();
                                            } else if (typeof this.formRef.current.submit === 'function') {
                                                // 模拟提交来触发校验，但不实际提交
                                                const originalSubmit = this.handleSubmit;
                                                this.handleSubmit = () => {
                                                    this.handleSubmit = originalSubmit;
                                                };
                                                this.formRef.current.submit();
                                            }
                                        }
                                    }, 100);
                                });
                            }
                        } catch (error) {
                            // 静默处理错误
                        }
                    }, 100); // 压缩延迟时间到100ms
                });
            }
        }

        // 如果有自定义校验错误，显示错误并阻止提交
        if (customErrors.length > 0) {
            message.error(customErrors.join('；'));
            return;
        }

        // 检查主页设置
        const data = [...this.state.navigation];
        const homepageCount = jp.query(data, `$..[?(@.isHomePage=== true)]`).length;
        if (homepageCount > 0 && isHomePage) {
            message.info('最多只允许设置一个主页');
            return;
        }

        // 校验通过，执行正常的提交逻辑
        jp.apply(data, `$..[?(@.componentId==="${componentId}")]`, (value) => {
            return componentformData;
        });
        const saveData = this.getParentMenuData(data, componentId);
        console.log('saveData', saveData);
        this.setState({
            navigation: data,
            formData
        }, this.saveNavigation(saveData));
    }
    onDragEnter = (info) => {
        // expandedKeys 需要受控时设置
        // this.setState({
        //   expandedKeys: info.expandedKeys,
        // });
    }
    handlTreeSelect = (selectedKeys, e) => {
        // 选中菜单时，显示菜单属性配置
        if (selectedKeys.length) {
            const eventKey = selectedKeys[0];
            const { navigation } = this.state;
            let values = jp.query(navigation, `$..[?(@.componentId==="${eventKey}")]`);
            const showSchema = values[0] && values[0].type !== 'siderMenu';
            this.setState({
                formData: {
                    formData: values[0]
                },
                showSchema
            });
            return;
        }

        // 取消选中菜单时，隐藏菜单属性配置
        this.setState({
            formData: {
                formData: {}
            },
            showSchema: false
        });
    }
    onDrop = (info) => {
        const dropKey = info.node.props.eventKey;
        const dragKey = info.dragNode.props.eventKey;
        const dropPos = info.node.props.pos.split('-');
        const dropPosition = info.dropPosition - Number(dropPos[dropPos.length - 1]);
        // const dragNodesKeys = info.dragNodesKeys;
        const loop = (data, key, callback) => {
            data.forEach((item, index, arr) => {
                if (item.componentId === key) {
                    return callback(item, index, arr);
                }
                if (item.children) {
                    return loop(item.children, key, callback);
                }
            });
        };
        const data = [...this.state.navigation];
        let dragObj;
        loop(data, dragKey, (item, index, arr) => {
            arr.splice(index, 1);
            dragObj = item;
        });
        if (info.dropToGap) {
            let ar;
            let i;
            loop(data, dropKey, (item, index, arr) => {
                ar = arr;
                i = index;
            });
            if (dropPosition === -1) {
                ar.splice(i, 0, dragObj);
            } else {
                ar.splice(i + 1, 0, dragObj);
            }
        } else {
            loop(data, dropKey, (item) => {
                item.children = item.children || [];
                // where to insert 示例添加到尾部，可以是随意位置
                item.children.push(dragObj);
            });
        }
        const dragObjParent = this.getParentMenuData(data, dragObj.componentId)
        this.setState({
            navigation: data
        }, this.saveNavigation(dragObjParent));
    }

    getParentMenuData(totalData = [], curId) {
        const curPath = jp.paths(totalData, `$..[?(@.componentId==="${curId}")]`)[0] || [];
        const curParentPath = jp.stringify(curPath.splice(0, curPath.length - 2));
        const curParentData = jp.value(totalData, curParentPath);
        return curParentData;
    }

    getNeedExpandKeys = (listData, expandedKeys = []) => {
        listData.forEach(item => {
            if (item.children && item.children.length && !expandedKeys.includes(item.componentId)) {
                expandedKeys.push(item.componentId);
            }
            if (item.children && item.children.length) {
                return this.getNeedExpandKeys(item.children, expandedKeys);
            }
        })
        return expandedKeys;
    }

    getNeedFoldKeys = (listData) => {
        return listData.map(item => item.componentId)
    }


    updateExpandKeys = () => {
        const { searchValue, navigation, filteredData } = this.state;
        const listData = searchValue ? filteredData : navigation;
        const expandedKeys = this.getNeedExpandKeys(listData);
        const foldKeys = this.getNeedFoldKeys(listData);
        this.setState({
            expandedKeys,
            foldKeys
        })
    }

    getFilteredData = (listData, value) => {
        const newDS = listData.map((item) => {
            if (item.children && item.children.length) {
                item.children = this.getFilteredData(item.children, value);
            }
    
            if(item.children.length) {
                return item;
            }
    
            if (item.name && item.name.indexOf(value) > -1) {
                return item;
            }
    
            return null;
        }).filter((item, i, self) => item);
    
        return newDS;
    }

    onExpand = expandedKeys => {
        this.setState({
            expand: true,
            expandAll: false,
            expandedKeys
        });
      };

    handleSearch = (e) => {
        const { value } = e.target;
        if (!value) {
            this.setState({
                searchValue: value,
                filteredData: []
            }, this.updateExpandKeys);
            return;
        }

        const { navigation } = this.state;
        const filteredData = this.getFilteredData(cloneDeep(navigation), value);
        this.setState({ autoExpandParent: true, searchValue: value, filteredData }, this.updateExpandKeys);
    }

    getNewMenuData = () => {
        return { title: '新增菜单', children: [], componentId: uuidv4(), componentName: generatorUniqueComponentNameInInstancesOrNavigationByLabel('Menu') };
    }

    addTopLevelMenu = () => {
        const { navigation } = this.state;
        const newMenuData = this.getNewMenuData();
        const navigationData = [...navigation];
        navigationData[0].children.push(newMenuData);
        this.setState({
            navigation: navigationData
        }, () => {
            this.saveNavigation(newMenuData);
            this.setState({
                expand: false
            });
        });
    }

    addSubMenu = (componentId) => {
        const { navigation, expand, expandAll } = this.state;
        const data = [...navigation];
        let saveData = {};
        jp.apply(data, `$..[?(@.componentId==="${componentId}")]`, value => {
            value.children.push(this.getNewMenuData());
            saveData = { ...value };
            return value;
        });
        this.setState({
            navigation: data,
        }, this.saveNavigation(saveData))
    }

    addBrotherMenu = (componentId) => {
        const { navigation } = this.state;
        const data = [...navigation]
        let saveData = {};
        jp.apply(data, `$..[?(@.children && @.children.length >0)]`, value => {
            const { children = [] } = value;
            const itemIndex = children.findIndex(v => v.componentId === `${componentId}`);
            if (itemIndex != -1) {
                children.splice(itemIndex + 1, 0, this.getNewMenuData());
                value.children = [...children];
                saveData = { ...value };
            }
            return value;
        });
        this.setState({
            navigation: data,
        }, this.saveNavigation(saveData))
    }
    deleteMenu = (componentId) => {
        const { navigation } = this.state;
        const data = [...navigation]
        jp.apply(data, `$..[?(@.children && @.children.length >0)]`, value => {
            const { children = [] } = value;
            const itemIndex = children.findIndex(v => v.componentId === `${componentId}`);
            if (itemIndex != -1) {
                children.splice(itemIndex, 1);
                value.children = [...children];
            }
            return value;
        });
        this.setState({
            navigation: data,
        }, this.deleteNavigation(componentId))
    }

    toggleExpandAll = () => {
        const { expand, expandAll } = this.state;
        const newExpand = expand && expandAll;
        this.setState({
            expand: !newExpand,
            expandAll: !newExpand,
        }, this.updateExpandKeys())
    }

    // 简化保存处理方法
    handleSave = () => {
        console.log('保存');
        
        if (!this.formRef.current) {
            return;
        }

        // 直接触发表单提交，校验逻辑移到 handleSubmit 中处理
        console.log('保存2');
        this.formRef.current.submit();
    }

    // 添加表单校验错误处理
    handleFormError = (errors) => {
        console.log('表单校验失败:', errors);
        // 这里不需要额外处理，表单会自动显示错误信息
    }

    transformErrors = (errors) => {
        return errors.map(error => {
            // 自定义必填项错误消息
            if (error.name === 'required') {
                const property = error.property;
                // 检查是否是必填的字段
                if (property === '.formData.componentName') {
                    error.message = '组件名称是必填项';
                } else if (property === '.formData.title') {
                    error.message = '名称是必填项';
                } else if (property === '.formData.type') {
                    error.message = '类型是必填项';
                } else if (property === '.formData.windowId') {
                    error.message = '绑定窗口是必填项';
                } else {
                    error.message = '该项是必填项';
                }
            }
            return error;
        });
    }

    renderCustomTitle = (item) => {
        const { title, componentId, children = [] } = item;
        return (
            <span>
                <span style={{
                    display: 'inline-block',
                }}>{title}</span>
                <span className="menu-list-tree-icon-button-box">
                    <Button
                        type="primary"
                        size="small"
                        className='menu-list-tree-icon-button'
                        onClick={(e) => {
                            e.stopPropagation();
                            this.addSubMenu(componentId);
                        }}
                    >
                        添加子菜单
                    </Button>
                    <Button
                        type="primary"
                        size="small"
                        className='menu-list-tree-icon-button'
                        onClick={(e) => {
                            e.stopPropagation();
                            this.addBrotherMenu(componentId);
                        }}
                    >
                        添加同级菜单
                    </Button>
                    <Button
                        type="primary"
                        size="small"
                        className='menu-list-tree-icon-button'
                        onClick={(e) => {
                            e.stopPropagation();
                            if (children.length) {
                                message.error('此菜单下有子菜单，不可删除');
                                return;
                            }

                            Modal.confirm({
                                title: '提醒',
                                content: <span dangerouslySetInnerHTML={{__html: `确定要删除菜单 <span style="color: red;">「${title}」</span> 吗？`}}></span>,
                                okText: '确定',
                                cancelText: '取消',
                                onOk: () => this.deleteMenu(componentId)
                            })
                        }}
                    >
                        删除
                    </Button>
                </span>
            </span>
        )
    }

    renderTreeNodes = data => data.map((item, index, arr) => {
        const { componentId, children = [], title = '未命名菜单', icon } = item;
        const treeNodeStyle = { fontSize: '20px'}
        const customTitle = this.renderCustomTitle(item);

        return <Tree.TreeNode key={componentId} title={customTitle} icon={<Icon type={icon} />} style={treeNodeStyle} >
            {
                children && children.length ? this.renderTreeNodes(children) : null
            }
        </Tree.TreeNode>;
    });

    render() {
        const { navigation, uiSchema, formData, showSchema, loading, searchValue, 
            filteredData, expand = true, expandedKeys = [], foldKeys = [], expandAll = true } = this.state;

        
        const realExpandedKeys = expand ? expandedKeys : foldKeys;

        const loopData = searchValue ? filteredData : navigation;

        if (formData.formData) {
            // FormData can have no windowId attribute, and if it does, it must be a string
            if (formData.formData.hasOwnProperty('windowId') && (!formData.formData.windowId || typeof formData.formData.windowId !== 'string')) {
                delete formData.formData.windowId;
            }

            Object.keys(defaultFormData).forEach(key => {
                if (formData.formData[key] === undefined || formData.formData[key] === null) {
                    formData.formData[key] = defaultFormData[key];
                }
            })
        }


        return (
            <Spin spinning={loading}>
            <Row
                style={{
                    backgroundColor: 'rgb(240, 242, 245)',
                    padding: '10px'
                }}
            >
                <Col 
                    span={20}
                    style={{ 
                        backgroundColor: '#FFF', 
                        height: 'calc(100vh - 65px - 20px)', 
                        width: 'calc(100% - 320px - 10px)',
                        marginRight: '10px'
                    }}
                >
                    <Card
                        title="菜单列表"
                        headStyle={{
                            fontSize: '16px',
                            fontWeight: 'bold',
                            color: '#000'
                        }}
                        bodyStyle={{
                            height: 'calc(100vh - 75px - 75px)',
                            overflowX: 'hidden',
                            padding: '16px',
                        }}
                        extra={(
                            <Fragment>
                                <Button
                                    type="primary"
                                    onClick={this.addTopLevelMenu}
                                    style={{ marginRight: '10px' }}
                                >
                                    添加顶级菜单
                                </Button>
                                <Button
                                    type="primary" 
                                    onClick={this.toggleExpandAll}
                                >
                                    {
                                        expand && expandAll ? <Icon type="minus-circle" theme="twoTone" /> : <Icon type="plus-circle" theme="twoTone" />
                                    }
                                    {
                                        expand && expandAll ? '收起全部' : '展开全部'
                                    }
                                </Button>
                            </Fragment>
                        )}
                    >
                        <Search
                            placeholder="搜索菜单"
                            onChange={this.handleSearch}
                            className="menu-list-search"
                            allowClear
                        />
                        <div
                            className="menu-list-tree-container"
                        >
                            <Tree
                                // defaultExpandAll
                                showLine
                                draggable
                                showIcon
                                expandedKeys={ realExpandedKeys }
                                onDragEnter={this.onDragEnter}
                                onDrop={this.onDrop}
                                onSelect={this.handlTreeSelect}
                                onExpand={this.onExpand}
                                autoExpandParent={this.state.autoExpandParent}
                                className="menu-list-tree"
                            >
                                {this.renderTreeNodes(loopData)}
                            </Tree>
                        </div>
                    </Card>
                </Col>
                <Col
                    span={4}
                    style={{ 
                        position: 'fixed', 
                        top: 74, 
                        zIndex: 1, 
                        right: 10, 
                        width: 320, 
                        height: 'calc(100vh - 75px - 10px)', 
                        backgroundColor: '#FFF',
                    }}
                >
                    <Card
                        title="菜单属性配置"
                        headStyle={{
                            fontSize: '16px',
                            fontWeight: 'bold',
                            color: '#000',
                        }}
                        bodyStyle={{
                            height: 'calc(100vh - 75px - 75px)',
                            overflowX: 'hidden',
                            padding: '10px',
                            paddingTop: '0px',
                        }}
                        extra={(
                            <Button
                                type="primary"
                                disabled={!showSchema}
                                onClick={this.handleSave}
                            >保存</Button>
                        )}
                    >
                        {showSchema && <Form
                            schema={schema}
                            ref={this.formRef}
                            uiSchema={uiSchema}
                            formData={formData}
                            onSubmit={this.handleSubmit}
                            widgets={CustomWidget}
                            className="menu-list-custom-styles"
                            idPrefix={"rjsf_open-care_menu"}
                            transformErrors={this.transformErrors}
                            liveValidate={true}
                            onError={this.handleFormError}
                        />}
                    </Card>
                </Col>


            </Row>
            </Spin>
        );
    }
}

